
Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  NotFoundError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
} = require('@prisma/client/runtime/library')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.2.0
 * Query Engine version: 2804dc98259d2ea960602aca6b8e7fdc03c1758f
 */
Prisma.prismaVersion = {
  client: "5.2.0",
  engine: "2804dc98259d2ea960602aca6b8e7fdc03c1758f"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.NotFoundError = NotFoundError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}


  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  wallet: 'wallet',
  username: 'username',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ipAddress: 'ipAddress',
  countryCode: 'countryCode',
  isRestricted: 'isRestricted',
  kycStatus: 'kycStatus',
  totalDeposited: 'totalDeposited',
  totalWithdrawn: 'totalWithdrawn',
  totalWagered: 'totalWagered',
  totalWinnings: 'totalWinnings',
  gamesPlayed: 'gamesPlayed',
  highestMass: 'highestMass',
  longestSession: 'longestSession'
};

exports.Prisma.GameSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  lobbyTier: 'lobbyTier',
  sessionToken: 'sessionToken',
  socketId: 'socketId',
  startedAt: 'startedAt',
  endedAt: 'endedAt',
  wagerAmount: 'wagerAmount',
  initialMass: 'initialMass',
  currentMass: 'currentMass',
  currentValue: 'currentValue',
  positionX: 'positionX',
  positionY: 'positionY',
  cellCount: 'cellCount',
  status: 'status',
  eliminatedBy: 'eliminatedBy',
  cashoutAmount: 'cashoutAmount',
  lastInputAt: 'lastInputAt',
  inputCount: 'inputCount',
  suspiciousFlag: 'suspiciousFlag'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionId: 'sessionId',
  type: 'type',
  amount: 'amount',
  feeAmount: 'feeAmount',
  signature: 'signature',
  blockHeight: 'blockHeight',
  confirmations: 'confirmations',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  solPriceUsd: 'solPriceUsd'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionId: 'sessionId',
  eventType: 'eventType',
  eventData: 'eventData',
  severity: 'severity',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp'
};

exports.Prisma.SystemConfigScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  type: 'type',
  description: 'description',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.LobbyStateScalarFieldEnum = {
  id: 'id',
  activePlayers: 'activePlayers',
  totalPoolValue: 'totalPoolValue',
  averageMass: 'averageMass',
  totalSessions: 'totalSessions',
  totalEliminations: 'totalEliminations',
  totalCashouts: 'totalCashouts',
  lastActivity: 'lastActivity',
  updatedAt: 'updatedAt'
};

exports.Prisma.PriceHistoryScalarFieldEnum = {
  id: 'id',
  solPriceUsd: 'solPriceUsd',
  confidence: 'confidence',
  publishTime: 'publishTime',
  oracleAccount: 'oracleAccount',
  slot: 'slot',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  User: 'User',
  GameSession: 'GameSession',
  Transaction: 'Transaction',
  AuditLog: 'AuditLog',
  SystemConfig: 'SystemConfig',
  LobbyState: 'LobbyState',
  PriceHistory: 'PriceHistory'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Documents\\Code\\agario gamble\\node_modules\\.pnpm\\@prisma+client@5.2.0_prisma@5.2.0\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": []
  },
  "relativeEnvPaths": {
    "rootEnvPath": null
  },
  "relativePath": "../../../../../../packages/server/prisma",
  "clientVersion": "5.2.0",
  "engineVersion": "2804dc98259d2ea960602aca6b8e7fdc03c1758f",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "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",
  "inlineSchemaHash": "827a9d4633c7e8b3096370454c868bbc23c593d04f04fe7912a20b1515edfa8f",
  "noEngine": false
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "../../node_modules/.pnpm/@prisma+client@5.2.0_prisma@5.2.0/node_modules/.prisma/client",
    "../node_modules/.pnpm/@prisma+client@5.2.0_prisma@5.2.0/node_modules/.prisma/client",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":\"users\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"wallet\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"countryCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isRestricted\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"kycStatus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":\"none\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalDeposited\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalWithdrawn\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalWagered\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalWinnings\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"gamesPlayed\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"highestMass\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"longestSession\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"GameSession\",\"relationName\":\"GameSessionToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"relationName\":\"TransactionToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"AuditLog\",\"relationName\":\"AuditLogToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"GameSession\":{\"dbName\":\"game_sessions\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lobbyTier\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionToken\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"socketId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"wagerAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"initialMass\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentMass\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"currentValue\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"positionX\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"positionY\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cellCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":\"active\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eliminatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cashoutAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastInputAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inputCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"suspiciousFlag\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"relationName\":\"GameSessionToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"transactions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Transaction\",\"relationName\":\"GameSessionToTransaction\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"AuditLog\",\"relationName\":\"AuditLogToGameSession\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Transaction\":{\"dbName\":\"transactions\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"feeAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"signature\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"blockHeight\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"confirmations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":\"pending\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"solPriceUsd\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"relationName\":\"TransactionToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"session\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"GameSession\",\"relationName\":\"GameSessionToTransaction\",\"relationFromFields\":[\"sessionId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"AuditLog\":{\"dbName\":\"audit_logs\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eventType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"eventData\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"severity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":\"info\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"timestamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"relationName\":\"AuditLogToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"session\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"GameSession\",\"relationName\":\"AuditLogToGameSession\",\"relationFromFields\":[\"sessionId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"SystemConfig\":{\"dbName\":\"system_config\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"key\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"value\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":\"string\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"LobbyState\":{\"dbName\":\"lobby_states\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"activePlayers\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalPoolValue\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Decimal\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"averageMass\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Float\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalSessions\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalEliminations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalCashouts\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastActivity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"PriceHistory\":{\"dbName\":\"price_history\",\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"cuid\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"solPriceUsd\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"confidence\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publishTime\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"oracleAccount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)



const { warnEnvConflicts } = require('@prisma/client/runtime/library')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "../../node_modules/.pnpm/@prisma+client@5.2.0_prisma@5.2.0/node_modules/.prisma/client/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "../../node_modules/.pnpm/@prisma+client@5.2.0_prisma@5.2.0/node_modules/.prisma/client/schema.prisma")
