import { randomBytes } from 'crypto'

// Generate a unique ID for entities
export function generateId(): string {
  return randomBytes(16).toString('hex')
}

// Generate a session token
export function generateSessionToken(): string {
  return randomBytes(32).toString('hex')
}

// Generate a shorter ID for temporary entities
export function generateShortId(): string {
  return randomBytes(8).toString('hex')
}

// Validate ID format
export function isValidId(id: string): boolean {
  return /^[a-f0-9]{32}$/.test(id)
}

// Validate session token format
export function isValidSessionToken(token: string): boolean {
  return /^[a-f0-9]{64}$/.test(token)
}
