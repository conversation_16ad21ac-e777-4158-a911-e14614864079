import { env } from './env'

// Lobby tier configuration
export interface LobbyTier {
  id: number
  name: string
  usdAmount: number // USD equivalent
  minWagerLamports: bigint // Will be calculated from oracle price
  maxPlayers: number
  description: string
}

// Default lobby tiers (SOL amounts will be calculated dynamically)
export const LOBBY_TIERS: LobbyTier[] = [
  {
    id: 1,
    name: 'Micro Stakes',
    usdAmount: 1.0,
    minWagerLamports: 0n, // Calculated dynamically
    maxPlayers: 100,
    description: '$1 USD equivalent wager',
  },
  {
    id: 2,
    name: 'Low Stakes',
    usdAmount: 10.0,
    minWagerLamports: 0n, // Calculated dynamically
    maxPlayers: 50,
    description: '$10 USD equivalent wager',
  },
]

// Game physics constants
export const GAME_CONFIG = {
  // World settings
  WORLD_WIDTH: 5000,
  WORLD_HEIGHT: 5000,
  WORLD_BORDER_BUFFER: 100,

  // Player settings
  BASE_MASS: 10,
  MIN_MASS: 10,
  MAX_MASS: 22500, // Theoretical max for balanced gameplay
  MASS_TO_RADIUS_FACTOR: 1.0, // radius = sqrt(mass / PI) * factor
  
  // Movement settings
  BASE_SPEED: 100, // Base speed units per second
  SPEED_MASS_EXPONENT: 0.449, // Speed = base / (1 + mass^exponent)
  MIN_SPEED: 10, // Minimum speed regardless of mass

  // Split mechanics
  SPLIT_MASS_RATIO: 0.5, // Each split cell gets 50% of original mass
  SPLIT_VELOCITY: 150, // Initial velocity of split cells
  SPLIT_DISTANCE: 50, // Distance between split cells
  MERGE_COOLDOWN_MS: env.MERGE_COOLDOWN_MS,
  MAX_CELLS_PER_PLAYER: env.MAX_CELLS,

  // Eject mechanics
  EJECT_MASS: 12, // Mass of ejected pellet
  EJECT_VELOCITY: 200, // Velocity of ejected pellet
  EJECT_COOLDOWN_MS: 100, // Minimum time between ejects

  // Decay settings
  DECAY_RATE: 0.002, // Mass lost per second as percentage
  DECAY_MIN_MASS: 50, // Minimum mass before decay starts
  
  // Pellet settings
  PELLET_MASS: 1,
  PELLET_COUNT: 1000, // Total pellets in world
  PELLET_RESPAWN_RATE: 10, // Pellets respawned per second
  
  // Virus settings
  VIRUS_MASS: 100,
  VIRUS_COUNT: 50,
  VIRUS_SPLIT_THRESHOLD: 120, // Mass required to split when hitting virus
  VIRUS_FEED_THRESHOLD: 7, // Pellets needed to pop virus
  VIRUS_POP_MASS_BONUS: 50, // Extra mass gained when virus pops
  VIRUS_SPLIT_COUNT: 7, // Number of pieces when virus is consumed
  VIRUS_SPLIT_VELOCITY: 200, // Initial velocity of virus split pieces

  // Collision settings
  CONSUME_MASS_RATIO: 1.25, // Must be 25% larger to consume
  QUADTREE_MAX_OBJECTS: 10,
  QUADTREE_MAX_LEVELS: 5,

  // Network settings
  TICK_RATE: env.TICK_RATE,
  SNAPSHOT_RATE: 20, // Full snapshots per second
  DELTA_RATE: 60, // Delta updates per second
  
  // Anti-cheat settings
  MAX_INPUT_RATE: 60, // Max inputs per second per client
  INPUT_VALIDATION_WINDOW_MS: 1000, // Time window for input validation
  MAX_VELOCITY_MULTIPLIER: 2.0, // Max allowed velocity vs calculated

  // Input reconciliation settings
  MAX_INPUT_AGE: 5000,     // milliseconds - maximum age of input to accept
  MIN_INPUT_INTERVAL: 25,  // milliseconds - minimum time between inputs (40 FPS)
  POSITION_TOLERANCE: 10,  // pixels - maximum position difference before correction
  
  // Cash-out settings
  CASHOUT_HOLD_MS: env.CASHOUT_HOLD_MS,
  CASHOUT_MIN_MASS: 15, // Minimum mass required to cash out
  CASHOUT_DURATION: 5000, // 5 seconds to complete cashout
  CASHOUT_MOVEMENT_TOLERANCE: 50, // Max distance player can move during cashout
  MIN_GAME_TIME_FOR_CASHOUT: 30000, // 30 seconds minimum game time before cashout
} as const

// Wager to mass conversion
export const WAGER_CONFIG = {
  // Base mass given for minimum wager
  BASE_MASS_PER_USD: 10, // 10 mass per $1 USD
  
  // Oracle price safety margin
  ORACLE_SAFETY_MARGIN_BPS: env.ORACLE_SAFETY_MARGIN_BPS,
  
  // Platform fee
  PLATFORM_FEE_BPS: env.PLATFORM_FEE_BPS,
  
  // Mass to SOL conversion factor (calculated dynamically)
  MASS_TO_SOL_FACTOR: 0.0001, // Will be updated based on oracle price
} as const

// Rate limiting configuration
export const RATE_LIMITS = {
  // Per-socket limits
  MESSAGES_PER_SECOND: 60,
  BURST_LIMIT: 10,
  
  // Per-IP limits
  CONNECTIONS_PER_IP: 5,
  
  // Input-specific limits
  MOVEMENT_INPUTS_PER_SECOND: 30,
  ACTION_INPUTS_PER_SECOND: 10, // Split, eject, cashout
} as const

// Spatial partitioning configuration
export const SPATIAL_CONFIG = {
  QUADTREE_BOUNDS: {
    x: 0,
    y: 0,
    width: GAME_CONFIG.WORLD_WIDTH,
    height: GAME_CONFIG.WORLD_HEIGHT,
  },
  MAX_OBJECTS_PER_NODE: GAME_CONFIG.QUADTREE_MAX_OBJECTS,
  MAX_LEVELS: GAME_CONFIG.QUADTREE_MAX_LEVELS,
} as const
