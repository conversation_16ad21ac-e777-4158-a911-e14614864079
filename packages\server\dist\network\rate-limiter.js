import { RATE_LIMITS } from '../config/lobby.js';
import pino from 'pino';
const logger = pino({ name: 'rate-limiter' });
export class RateLimiter {
    clients = new Map();
    cleanupInterval;
    constructor() {
        // Clean up old client data every minute
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 60000);
    }
    addClient(clientId) {
        this.clients.set(clientId, {
            joinGame: new TokenBucket(RATE_LIMITS.JOIN_GAME_PER_MINUTE, 60000),
            playerInput: new TokenBucket(RATE_LIMITS.INPUT_PER_SECOND, 1000),
            ping: new TokenBucket(RATE_LIMITS.PING_PER_SECOND, 1000),
            lastActivity: Date.now(),
        });
    }
    removeClient(clientId) {
        this.clients.delete(clientId);
    }
    checkLimit(clientId, action) {
        const clientData = this.clients.get(clientId);
        if (!clientData) {
            logger.warn(`Rate limit check for unknown client: ${clientId}`);
            return false;
        }
        clientData.lastActivity = Date.now();
        switch (action) {
            case 'join_game':
                return clientData.joinGame.consume();
            case 'player_input':
                return clientData.playerInput.consume();
            case 'ping':
                return clientData.ping.consume();
            default:
                return false;
        }
    }
    getRemainingTokens(clientId, action) {
        const clientData = this.clients.get(clientId);
        if (!clientData)
            return 0;
        switch (action) {
            case 'join_game':
                return clientData.joinGame.getTokens();
            case 'player_input':
                return clientData.playerInput.getTokens();
            case 'ping':
                return clientData.ping.getTokens();
            default:
                return 0;
        }
    }
    cleanup() {
        const now = Date.now();
        const expireTime = 5 * 60 * 1000; // 5 minutes
        for (const [clientId, data] of this.clients.entries()) {
            if (now - data.lastActivity > expireTime) {
                this.clients.delete(clientId);
            }
        }
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.clients.clear();
    }
}
class TokenBucket {
    tokens;
    maxTokens;
    refillRate;
    lastRefill;
    constructor(maxTokens, refillIntervalMs) {
        this.maxTokens = maxTokens;
        this.tokens = maxTokens;
        this.refillRate = maxTokens / refillIntervalMs; // tokens per millisecond
        this.lastRefill = Date.now();
    }
    consume(tokens = 1) {
        this.refill();
        if (this.tokens >= tokens) {
            this.tokens -= tokens;
            return true;
        }
        return false;
    }
    getTokens() {
        this.refill();
        return Math.floor(this.tokens);
    }
    refill() {
        const now = Date.now();
        const timePassed = now - this.lastRefill;
        if (timePassed > 0) {
            const tokensToAdd = timePassed * this.refillRate;
            this.tokens = Math.min(this.maxTokens, this.tokens + tokensToAdd);
            this.lastRefill = now;
        }
    }
}
// Global rate limiter for IP-based limiting
export class IPRateLimiter {
    ips = new Map();
    cleanupInterval;
    constructor() {
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 60000);
    }
    checkIPLimit(ip, action) {
        let ipData = this.ips.get(ip);
        if (!ipData) {
            ipData = {
                connections: new TokenBucket(RATE_LIMITS.CONNECTIONS_PER_IP, 60000),
                requests: new TokenBucket(RATE_LIMITS.REQUESTS_PER_IP_PER_MINUTE, 60000),
                lastActivity: Date.now(),
            };
            this.ips.set(ip, ipData);
        }
        ipData.lastActivity = Date.now();
        switch (action) {
            case 'connection':
                return ipData.connections.consume();
            case 'request':
                return ipData.requests.consume();
            default:
                return false;
        }
    }
    cleanup() {
        const now = Date.now();
        const expireTime = 10 * 60 * 1000; // 10 minutes
        for (const [ip, data] of this.ips.entries()) {
            if (now - data.lastActivity > expireTime) {
                this.ips.delete(ip);
            }
        }
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.ips.clear();
    }
}
// Anti-spam protection
export class AntiSpam {
    suspiciousIPs = new Set();
    bannedIPs = new Set();
    ipViolations = new Map();
    reportViolation(ip, severity) {
        const currentViolations = this.ipViolations.get(ip) || 0;
        let newViolations = currentViolations;
        switch (severity) {
            case 'low':
                newViolations += 1;
                break;
            case 'medium':
                newViolations += 3;
                break;
            case 'high':
                newViolations += 10;
                break;
        }
        this.ipViolations.set(ip, newViolations);
        // Mark as suspicious after 5 violations
        if (newViolations >= 5) {
            this.suspiciousIPs.add(ip);
            logger.warn(`IP marked as suspicious: ${ip} (${newViolations} violations)`);
        }
        // Ban after 20 violations
        if (newViolations >= 20) {
            this.bannedIPs.add(ip);
            logger.error(`IP banned: ${ip} (${newViolations} violations)`);
        }
    }
    isIPBanned(ip) {
        return this.bannedIPs.has(ip);
    }
    isIPSuspicious(ip) {
        return this.suspiciousIPs.has(ip);
    }
    getViolationCount(ip) {
        return this.ipViolations.get(ip) || 0;
    }
    clearViolations(ip) {
        this.ipViolations.delete(ip);
        this.suspiciousIPs.delete(ip);
        this.bannedIPs.delete(ip);
    }
}
// Singleton instances
export const globalRateLimiter = new RateLimiter();
export const ipRateLimiter = new IPRateLimiter();
export const antiSpam = new AntiSpam();
