export interface ProgressBarProps {
  value: number // 0-1
  max?: number
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'success' | 'warning' | 'danger'
  showLabel?: boolean
  label?: string
  animated?: boolean
  className?: string
}

export class ProgressBar {
  private element: HTMLDivElement
  private props: ProgressBarProps
  private progressElement: HTMLDivElement
  private labelElement?: HTMLSpanElement
  
  constructor(props: ProgressBarProps) {
    this.props = props
    this.element = this.createElement()
  }
  
  private createElement(): HTMLDivElement {
    const container = document.createElement('div')
    container.className = this.getContainerClasses()
    
    // Progress track
    const track = document.createElement('div')
    track.className = 'progress-bar__track'
    
    // Progress fill
    this.progressElement = document.createElement('div')
    this.progressElement.className = 'progress-bar__fill'
    this.updateProgress()
    
    track.appendChild(this.progressElement)
    container.appendChild(track)
    
    // Label
    if (this.props.showLabel) {
      this.labelElement = document.createElement('span')
      this.labelElement.className = 'progress-bar__label'
      this.updateLabel()
      container.appendChild(this.labelElement)
    }
    
    this.ensureStyles()
    return container
  }
  
  private getContainerClasses(): string {
    const { size = 'md', variant = 'primary', animated = false, className = '' } = this.props
    
    return `progress-bar progress-bar--${size} progress-bar--${variant} ${animated ? 'progress-bar--animated' : ''} ${className}`.trim()
  }
  
  private updateProgress(): void {
    const { value, max = 1 } = this.props
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    
    this.progressElement.style.width = `${percentage}%`
    this.progressElement.setAttribute('aria-valuenow', value.toString())
    this.progressElement.setAttribute('aria-valuemax', max.toString())
  }
  
  private updateLabel(): void {
    if (!this.labelElement) return
    
    const { value, max = 1, label } = this.props
    
    if (label) {
      this.labelElement.textContent = label
    } else {
      const percentage = Math.round((value / max) * 100)
      this.labelElement.textContent = `${percentage}%`
    }
  }
  
  private ensureStyles(): void {
    if (document.getElementById('progress-bar-styles')) return
    
    const style = document.createElement('style')
    style.id = 'progress-bar-styles'
    style.textContent = `
      .progress-bar {
        display: flex;
        flex-direction: column;
        gap: var(--space-xs);
      }
      
      .progress-bar__track {
        width: 100%;
        background: var(--color-background-panel);
        border-radius: var(--radius-full);
        overflow: hidden;
        position: relative;
      }
      
      .progress-bar__fill {
        height: 100%;
        background: var(--color-primary);
        border-radius: var(--radius-full);
        transition: width var(--transition-normal);
        position: relative;
        overflow: hidden;
      }
      
      .progress-bar__label {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        text-align: center;
        font-weight: var(--font-weight-medium);
      }
      
      /* Sizes */
      .progress-bar--sm .progress-bar__track {
        height: 4px;
      }
      
      .progress-bar--md .progress-bar__track {
        height: 8px;
      }
      
      .progress-bar--lg .progress-bar__track {
        height: 12px;
      }
      
      /* Variants */
      .progress-bar--primary .progress-bar__fill {
        background: var(--color-primary);
      }
      
      .progress-bar--success .progress-bar__fill {
        background: var(--color-success);
      }
      
      .progress-bar--warning .progress-bar__fill {
        background: var(--color-warning);
      }
      
      .progress-bar--danger .progress-bar__fill {
        background: var(--color-error);
      }
      
      /* Animated variant */
      .progress-bar--animated .progress-bar__fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        animation: shimmer 2s infinite;
      }
      
      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }
      
      /* Pulsing effect for danger variant */
      .progress-bar--danger.progress-bar--animated .progress-bar__fill {
        animation: pulse-danger 1s ease-in-out infinite alternate;
      }
      
      @keyframes pulse-danger {
        0% {
          background: var(--color-error);
        }
        100% {
          background: #DC2626;
        }
      }
      
      /* Accessibility */
      .progress-bar__fill {
        role: progressbar;
        aria-valuemin: 0;
      }
      
      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .progress-bar__track {
          border: 1px solid currentColor;
        }
        
        .progress-bar__fill {
          border: 1px solid transparent;
        }
      }
      
      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        .progress-bar__fill {
          transition: none;
        }
        
        .progress-bar--animated .progress-bar__fill::after {
          animation: none;
        }
        
        .progress-bar--danger.progress-bar--animated .progress-bar__fill {
          animation: none;
        }
      }
    `
    
    document.head.appendChild(style)
  }
  
  /**
   * Update progress bar properties
   */
  update(props: Partial<ProgressBarProps>): void {
    this.props = { ...this.props, ...props }
    
    // Update container classes
    this.element.className = this.getContainerClasses()
    
    // Update progress
    this.updateProgress()
    
    // Update label
    if (this.labelElement) {
      this.updateLabel()
    } else if (this.props.showLabel && !this.labelElement) {
      // Add label if it wasn't there before
      this.labelElement = document.createElement('span')
      this.labelElement.className = 'progress-bar__label'
      this.updateLabel()
      this.element.appendChild(this.labelElement)
    } else if (!this.props.showLabel && this.labelElement) {
      // Remove label if it's no longer needed
      this.labelElement.remove()
      this.labelElement = undefined
    }
  }
  
  /**
   * Set progress value with animation
   */
  setProgress(value: number, animate: boolean = true): void {
    if (!animate) {
      const originalTransition = this.progressElement.style.transition
      this.progressElement.style.transition = 'none'
      this.update({ value })
      // Restore transition after a frame
      requestAnimationFrame(() => {
        this.progressElement.style.transition = originalTransition
      })
    } else {
      this.update({ value })
    }
  }
  
  /**
   * Get current progress value
   */
  getValue(): number {
    return this.props.value
  }
  
  /**
   * Get the DOM element
   */
  getElement(): HTMLDivElement {
    return this.element
  }
  
  /**
   * Destroy the progress bar
   */
  destroy(): void {
    this.element.remove()
  }
}
