{"version": 3, "file": "token-account-layout.js", "sourceRoot": "", "sources": ["../../../src/program/token-account-layout.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAuB;AACvB,4DAA8C;AAC9C,iDAAuC;AACvC,6CAA4C;AAE5C,SAAS,MAAM,CAAC,QAAiB;IAC/B,OAAO,IAAI,aAAa,CACtB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EACpB,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAChC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,EACxB,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,QAAiB;IAClC,OAAO,IAAI,aAAa,CACtB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EACrB,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,mBAAS,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAc,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,EAClC,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAI,MAAiB,EAAE,QAAiB;IACtD,OAAO,IAAI,aAAa,CAAI,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChD,CAAC;AAED,MAAM,aAAoB,SAAQ,sBAAS;IAKzC,YACE,MAAiB,EACjB,OAAuB,EACvB,OAAsB,EACtB,QAAiB;QAEjB,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,CAAS,EAAE,MAAe;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,GAAM,EAAE,CAAS,EAAE,MAAe;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,MAAe;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;CACF;AAED,MAAM,aAAiB,SAAQ,sBAAgB;IAI7C,YAAY,MAAiB,EAAE,QAAiB;QAC9C,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,GAAa,EAAE,CAAS,EAAE,MAAM,GAAG,CAAC;QACzC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACnE;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC3D,IAAI,aAAa,KAAK,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,aAAa,KAAK,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;SAC1C;QACD,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;CACF;AAED,MAAM,GAAI,SAAQ,eAAE;IAClB;;OAEG;IACH,QAAQ;QACN,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAClB,OAAO,CAAC,CAAC;SACV;QACD,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SAClC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAc;QAC9B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SAC5D;QACD,OAAO,IAAI,GAAG,CACZ,CAAC,GAAG,MAAM,CAAC;aACR,OAAO,EAAE;aACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3C,IAAI,CAAC,EAAE,CAAC,EACX,EAAE,CACH,CAAC;IACJ,CAAC;CACF;AAED,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC;IAC/C,SAAS,CAAC,MAAM,CAAC;IACjB,SAAS,CAAC,OAAO,CAAC;IAClB,MAAM,CAAC,QAAQ,CAAC;IAChB,OAAO,CAAC,SAAS,EAAE,EAAE,UAAU,CAAC;IAChC,CAAC,CAAC,CAAS,EAAE,EAAE;QACb,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;QAC1D,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QACnD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,OAAO,CAAC;IACX,OAAO,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC;IAC7B,MAAM,CAAC,iBAAiB,CAAC;IACzB,OAAO,CAAC,SAAS,EAAE,EAAE,gBAAgB,CAAC;CACvC,CAAC,CAAC;AAEH,SAAgB,kBAAkB,CAAC,CAAS;IAC1C,OAAO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC;AAFD,gDAEC"}