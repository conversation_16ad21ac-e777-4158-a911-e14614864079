import { PublicKey } from '@solana/web3.js'
import nacl from 'tweetnacl'
import bs58 from 'bs58'
import jwt from 'jsonwebtoken'
import { validateEnv } from '../config/env'
import { generateSessionToken } from '../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'wallet-auth' })
const env = validateEnv()

export class WalletAuthService {
  private jwtSecret: string
  private sessionTokens = new Map<string, SessionData>()
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    this.jwtSecret = env.JWT_SECRET
    
    // Clean up expired sessions every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions()
    }, 5 * 60 * 1000)
  }

  // Verify wallet signature for authentication
  async verifyWalletSignature(
    walletAddress: string,
    message: string,
    signature: string
  ): Promise<boolean> {
    try {
      // Validate wallet address format
      const publicKey = new PublicKey(walletAddress)
      
      // Decode signature from base58
      const signatureBytes = bs58.decode(signature)
      
      // Encode message as bytes
      const messageBytes = new TextEncoder().encode(message)
      
      // Verify signature
      const isValid = nacl.sign.detached.verify(
        messageBytes,
        signatureBytes,
        publicKey.toBytes()
      )
      
      if (isValid) {
        logger.info(`Wallet signature verified for ${walletAddress}`)
      } else {
        logger.warn(`Invalid wallet signature for ${walletAddress}`)
      }
      
      return isValid
      
    } catch (error) {
      logger.error(error, `Error verifying wallet signature for ${walletAddress}`)
      return false
    }
  }

  // Create authentication challenge message
  createAuthChallenge(walletAddress: string, nonce?: string): string {
    const timestamp = Date.now()
    const challengeNonce = nonce || Math.random().toString(36).substring(2, 15)
    
    return `Sign this message to authenticate with Agar.io Solana:

Wallet: ${walletAddress}
Timestamp: ${timestamp}
Nonce: ${challengeNonce}

This request will not trigger a blockchain transaction or cost any gas fees.`
  }

  // Authenticate wallet and create session
  async authenticateWallet(
    walletAddress: string,
    message: string,
    signature: string,
    wagerAmount: number
  ): Promise<AuthResult> {
    try {
      // Verify signature
      const isValidSignature = await this.verifyWalletSignature(
        walletAddress,
        message,
        signature
      )
      
      if (!isValidSignature) {
        return {
          success: false,
          error: 'Invalid wallet signature',
        }
      }

      // Validate message format and freshness
      const messageValidation = this.validateAuthMessage(message, walletAddress)
      if (!messageValidation.isValid) {
        return {
          success: false,
          error: messageValidation.error || 'Invalid authentication message',
        }
      }

      // Create session token
      const sessionToken = generateSessionToken()
      
      // Create JWT token
      const jwtToken = jwt.sign(
        {
          walletAddress,
          sessionToken,
          wagerAmount,
          timestamp: Date.now(),
        },
        this.jwtSecret,
        {
          expiresIn: '24h',
          issuer: 'agario-solana-server',
          subject: walletAddress,
        }
      )

      // Store session data
      const sessionData: SessionData = {
        walletAddress,
        sessionToken,
        jwtToken,
        wagerAmount,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        isActive: true,
      }
      
      this.sessionTokens.set(sessionToken, sessionData)
      
      logger.info(`Authentication successful for wallet ${walletAddress}`)
      
      return {
        success: true,
        sessionToken,
        jwtToken,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      }
      
    } catch (error) {
      logger.error(error, `Authentication failed for wallet ${walletAddress}`)
      return {
        success: false,
        error: 'Authentication failed',
      }
    }
  }

  // Validate JWT token
  validateJWT(token: string): JWTValidationResult {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any
      
      return {
        isValid: true,
        payload: {
          walletAddress: decoded.walletAddress,
          sessionToken: decoded.sessionToken,
          wagerAmount: decoded.wagerAmount,
          timestamp: decoded.timestamp,
        },
      }
      
    } catch (error) {
      logger.warn(`Invalid JWT token: ${error}`)
      return {
        isValid: false,
        error: 'Invalid or expired token',
      }
    }
  }

  // Validate session token
  validateSessionToken(sessionToken: string): SessionValidationResult {
    const sessionData = this.sessionTokens.get(sessionToken)
    
    if (!sessionData) {
      return {
        isValid: false,
        error: 'Session not found',
      }
    }
    
    if (!sessionData.isActive) {
      return {
        isValid: false,
        error: 'Session is inactive',
      }
    }
    
    // Check if session is expired (24 hours)
    const sessionAge = Date.now() - sessionData.createdAt
    if (sessionAge > 24 * 60 * 60 * 1000) {
      this.invalidateSession(sessionToken)
      return {
        isValid: false,
        error: 'Session expired',
      }
    }
    
    // Update last activity
    sessionData.lastActivity = Date.now()
    
    return {
      isValid: true,
      sessionData,
    }
  }

  // Invalidate session
  invalidateSession(sessionToken: string): void {
    const sessionData = this.sessionTokens.get(sessionToken)
    if (sessionData) {
      sessionData.isActive = false
      logger.info(`Session invalidated for wallet ${sessionData.walletAddress}`)
    }
  }

  // Remove session completely
  removeSession(sessionToken: string): void {
    this.sessionTokens.delete(sessionToken)
  }

  // Get session data
  getSessionData(sessionToken: string): SessionData | null {
    return this.sessionTokens.get(sessionToken) || null
  }

  // Validate authentication message format and freshness
  private validateAuthMessage(message: string, expectedWallet: string): MessageValidation {
    try {
      // Extract wallet address from message
      const walletMatch = message.match(/Wallet: ([A-Za-z0-9]+)/)
      if (!walletMatch || walletMatch[1] !== expectedWallet) {
        return {
          isValid: false,
          error: 'Wallet address mismatch in message',
        }
      }

      // Extract timestamp from message
      const timestampMatch = message.match(/Timestamp: (\d+)/)
      if (!timestampMatch) {
        return {
          isValid: false,
          error: 'No timestamp found in message',
        }
      }

      const messageTimestamp = parseInt(timestampMatch[1])
      const now = Date.now()
      const timeDiff = Math.abs(now - messageTimestamp)

      // Message should be no older than 5 minutes
      if (timeDiff > 5 * 60 * 1000) {
        return {
          isValid: false,
          error: 'Authentication message is too old',
        }
      }

      return { isValid: true }
      
    } catch (error) {
      return {
        isValid: false,
        error: 'Failed to validate message format',
      }
    }
  }

  // Clean up expired sessions
  private cleanupExpiredSessions(): void {
    const now = Date.now()
    const expiredSessions: string[] = []
    
    for (const [sessionToken, sessionData] of this.sessionTokens.entries()) {
      const sessionAge = now - sessionData.createdAt
      const inactivityTime = now - sessionData.lastActivity
      
      // Remove sessions older than 24 hours or inactive for 2 hours
      if (sessionAge > 24 * 60 * 60 * 1000 || inactivityTime > 2 * 60 * 60 * 1000) {
        expiredSessions.push(sessionToken)
      }
    }
    
    for (const sessionToken of expiredSessions) {
      this.sessionTokens.delete(sessionToken)
    }
    
    if (expiredSessions.length > 0) {
      logger.info(`Cleaned up ${expiredSessions.length} expired sessions`)
    }
  }

  // Get session statistics
  getSessionStats(): {
    totalSessions: number
    activeSessions: number
    inactiveSessions: number
  } {
    let activeSessions = 0
    let inactiveSessions = 0
    
    for (const sessionData of this.sessionTokens.values()) {
      if (sessionData.isActive) {
        activeSessions++
      } else {
        inactiveSessions++
      }
    }
    
    return {
      totalSessions: this.sessionTokens.size,
      activeSessions,
      inactiveSessions,
    }
  }

  // Cleanup
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.sessionTokens.clear()
    logger.info('Wallet auth service cleanup completed')
  }
}

// Types
interface SessionData {
  walletAddress: string
  sessionToken: string
  jwtToken: string
  wagerAmount: number
  createdAt: number
  lastActivity: number
  isActive: boolean
}

interface AuthResult {
  success: boolean
  sessionToken?: string
  jwtToken?: string
  expiresAt?: number
  error?: string
}

interface JWTValidationResult {
  isValid: boolean
  payload?: {
    walletAddress: string
    sessionToken: string
    wagerAmount: number
    timestamp: number
  }
  error?: string
}

interface SessionValidationResult {
  isValid: boolean
  sessionData?: SessionData
  error?: string
}

interface MessageValidation {
  isValid: boolean
  error?: string
}

// Singleton instance
export const walletAuthService = new WalletAuthService()
