// Core game entity types
export interface Vector2 {
  x: number
  y: number
}

export interface Bounds {
  x: number
  y: number
  width: number
  height: number
}

// Base entity interface
export interface Entity {
  id: string
  position: Vector2
  radius: number
  mass: number
  type: EntityType
}

export enum EntityType {
  PLAYER_CELL = 'player_cell',
  PELLET = 'pellet',
  VIRUS = 'virus',
  EJECTED_MASS = 'ejected_mass',
}

// Player cell entity
export interface PlayerCell extends Entity {
  type: EntityType.PLAYER_CELL
  playerId: string
  velocity: Vector2
  targetPosition: Vector2
  canMerge: boolean
  mergeTime: number
  splitCooldown: number
  lastSplit: number
}

// Pellet entity (food)
export interface Pellet extends Entity {
  type: EntityType.PELLET
  color: string
  respawnTime?: number
}

// Virus entity
export interface Virus extends Entity {
  type: EntityType.VIRUS
  feedCount: number
  maxFeedCount: number
  popCooldown: number
  lastPop: number
}

// Ejected mass entity
export interface EjectedMass extends Entity {
  type: EntityType.EJECTED_MASS
  velocity: Vector2
  ownerId: string
  decayTime: number
  canBeEaten: boolean
}

// Player state
export interface Player {
  id: string
  sessionToken: string
  socketId: string
  wallet: string
  username?: string
  
  // Game state
  cells: PlayerCell[]
  totalMass: number
  score: number
  
  // Position and movement
  targetPosition: Vector2
  lastInput: number
  inputCount: number
  
  // Wager and value
  wagerAmount: number // SOL
  currentValue: number // SOL
  initialMass: number
  
  // Status
  isAlive: boolean
  canCashout: boolean
  cashoutStartTime?: number
  
  // Anti-cheat
  suspiciousActivity: boolean
  lastValidatedPosition: Vector2
  lastValidatedTime: number
}

// Game world state
export interface GameWorld {
  players: Map<string, Player>
  pellets: Map<string, Pellet>
  viruses: Map<string, Virus>
  ejectedMass: Map<string, EjectedMass>
  
  // Spatial partitioning
  quadTree: QuadTree
  
  // Game statistics
  totalPlayers: number
  totalMass: number
  totalValue: number // SOL
  
  // Timing
  lastTick: number
  tickCount: number
  
  // Configuration
  lobbyTier: number
}

// Input types
export interface PlayerInput {
  playerId: string
  sessionToken: string
  timestamp: number
  sequenceNumber: number
  
  // Movement
  targetX?: number
  targetY?: number
  
  // Actions
  split?: boolean
  eject?: boolean
  cashout?: boolean
  
  // Validation
  signature?: string
}

// Collision detection result
export interface CollisionResult {
  entity1: Entity
  entity2: Entity
  distance: number
  canConsume: boolean
  consumer?: Entity
  consumed?: Entity
}

// Game event types
export enum GameEventType {
  PLAYER_JOINED = 'player_joined',
  PLAYER_LEFT = 'player_left',
  PLAYER_ELIMINATED = 'player_eliminated',
  PLAYER_SPLIT = 'player_split',
  PLAYER_MERGED = 'player_merged',
  PLAYER_EJECTED = 'player_ejected',
  PLAYER_CONSUMED_PELLET = 'player_consumed_pellet',
  PLAYER_CONSUMED_VIRUS = 'player_consumed_virus',
  PLAYER_CONSUMED_PLAYER = 'player_consumed_player',
  PLAYER_CASHOUT_STARTED = 'player_cashout_started',
  PLAYER_CASHOUT_COMPLETED = 'player_cashout_completed',
  PLAYER_CASHOUT_CANCELLED = 'player_cashout_cancelled',
  VIRUS_POPPED = 'virus_popped',
  PELLET_RESPAWNED = 'pellet_respawned',
}

export interface GameEvent {
  type: GameEventType
  timestamp: number
  data: any
  playerId?: string
  sessionId?: string
}

// Network message types
export interface NetworkMessage {
  type: string
  timestamp: number
  data: any
}

// Game state snapshot for clients
export interface GameSnapshot {
  timestamp: number
  tick: number
  
  // Visible entities (within player's view)
  players: Partial<Player>[]
  pellets: Pellet[]
  viruses: Virus[]
  ejectedMass: EjectedMass[]
  
  // Player-specific data
  playerData?: {
    totalMass: number
    score: number
    currentValue: number
    canCashout: boolean
    cashoutProgress?: number
  }
  
  // Lobby information
  lobbyStats: {
    totalPlayers: number
    totalValue: number
  }
}

// Delta update for efficient networking
export interface GameDelta {
  timestamp: number
  tick: number
  
  // Changed entities
  playersChanged: Partial<Player>[]
  pelletsChanged: Pellet[]
  virusesChanged: Virus[]
  ejectedMassChanged: EjectedMass[]
  
  // Removed entities
  playersRemoved: string[]
  pelletsRemoved: string[]
  virusesRemoved: string[]
  ejectedMassRemoved: string[]
  
  // Events
  events: GameEvent[]
}

// Quadtree for spatial partitioning
export interface QuadTreeNode {
  bounds: Bounds
  entities: Entity[]
  children: QuadTreeNode[]
  level: number
  maxObjects: number
  maxLevels: number
}

export interface QuadTree {
  root: QuadTreeNode
  insert(entity: Entity): void
  remove(entity: Entity): void
  retrieve(bounds: Bounds): Entity[]
  clear(): void
  update(entity: Entity): void
}

// Physics calculation results
export interface PhysicsResult {
  position: Vector2
  velocity: Vector2
  mass: number
  radius: number
  speed: number
}

// Validation result for anti-cheat
export interface ValidationResult {
  isValid: boolean
  reason?: string
  suspiciousActivity?: boolean
  correctedPosition?: Vector2
}

// Lobby configuration
export interface LobbyConfig {
  tier: number
  minWager: number // SOL
  maxPlayers: number
  worldBounds: Bounds
  spawnAreas: Bounds[]
}

// Performance metrics
export interface PerformanceMetrics {
  tickTime: number
  entityCount: number
  collisionChecks: number
  networkMessages: number
  memoryUsage: number
  cpuUsage: number
}
