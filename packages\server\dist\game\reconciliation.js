import { GAME_CONFIG } from '../config/lobby.js';
import pino from 'pino';
const logger = pino({ name: 'input-reconciliation' });
export class InputReconciliationSystem {
    playerInputHistory = new Map();
    serverStateHistory = new Map();
    maxHistorySize = 60; // Keep 60 ticks of history (3 seconds at 20 TPS)
    // Process and validate player input
    processPlayerInput(player, input) {
        const playerId = player.id;
        // Get or create input history for player
        let inputHistory = this.playerInputHistory.get(playerId);
        if (!inputHistory) {
            inputHistory = {
                inputs: [],
                lastProcessedSequence: 0,
                suspiciousInputCount: 0,
                lastValidationTime: Date.now(),
            };
            this.playerInputHistory.set(playerId, inputHistory);
        }
        // Validate input sequence
        const sequenceValidation = this.validateInputSequence(input, inputHistory);
        if (!sequenceValidation.isValid) {
            return {
                success: false,
                error: sequenceValidation.error,
                shouldDisconnect: sequenceValidation.severity === 'high',
            };
        }
        // Validate input timing
        const timingValidation = this.validateInputTiming(input, inputHistory);
        if (!timingValidation.isValid) {
            return {
                success: false,
                error: timingValidation.error,
                shouldDisconnect: timingValidation.severity === 'high',
            };
        }
        // Validate input content
        const contentValidation = this.validateInputContent(input, player);
        if (!contentValidation.isValid) {
            return {
                success: false,
                error: contentValidation.error,
                shouldDisconnect: contentValidation.severity === 'high',
            };
        }
        // Store input in history
        this.storeInputInHistory(input, inputHistory);
        // Update player state
        this.updatePlayerFromInput(player, input);
        return {
            success: true,
            processedInput: input,
        };
    }
    // Validate input sequence numbers to prevent replay attacks
    validateInputSequence(input, history) {
        // Check if sequence number is greater than last processed
        if (input.sequenceNumber <= history.lastProcessedSequence) {
            history.suspiciousInputCount++;
            if (history.suspiciousInputCount > 5) {
                return {
                    isValid: false,
                    error: 'Repeated sequence number violations',
                    severity: 'high',
                };
            }
            return {
                isValid: false,
                error: 'Invalid sequence number',
                severity: 'medium',
            };
        }
        // Check for sequence number gaps (possible packet loss)
        const expectedSequence = history.lastProcessedSequence + 1;
        if (input.sequenceNumber > expectedSequence + 5) {
            logger.warn(`Large sequence gap for player ${input.playerId}: expected ${expectedSequence}, got ${input.sequenceNumber}`);
            // Allow but log suspicious activity
        }
        return { isValid: true };
    }
    // Validate input timing to prevent time manipulation
    validateInputTiming(input, history) {
        const now = Date.now();
        const inputAge = now - input.timestamp;
        // Input should not be too old or from the future
        if (inputAge > GAME_CONFIG.MAX_INPUT_AGE || inputAge < -1000) {
            return {
                isValid: false,
                error: 'Input timestamp out of acceptable range',
                severity: 'medium',
            };
        }
        // Check input rate
        const timeSinceLastInput = now - history.lastValidationTime;
        if (timeSinceLastInput < GAME_CONFIG.MIN_INPUT_INTERVAL) {
            history.suspiciousInputCount++;
            if (history.suspiciousInputCount > 10) {
                return {
                    isValid: false,
                    error: 'Input rate too high',
                    severity: 'high',
                };
            }
            return {
                isValid: false,
                error: 'Input rate limit exceeded',
                severity: 'low',
            };
        }
        return { isValid: true };
    }
    // Validate input content for impossible values
    validateInputContent(input, player) {
        // Validate movement targets
        if (input.targetX !== undefined && input.targetY !== undefined) {
            // Check if target position is within reasonable bounds
            if (!this.isValidPosition({ x: input.targetX, y: input.targetY })) {
                return {
                    isValid: false,
                    error: 'Invalid target position',
                    severity: 'medium',
                };
            }
            // Check for impossible movement speed
            if (player.cells.length > 0) {
                const currentPos = player.cells[0].position;
                const targetPos = { x: input.targetX, y: input.targetY };
                const distance = this.calculateDistance(currentPos, targetPos);
                // Calculate maximum possible movement in one tick
                const maxSpeed = this.calculateMaxSpeed(player.totalMass);
                const maxDistance = maxSpeed * (1 / 20); // 20 TPS
                if (distance > maxDistance * 2) { // Allow some tolerance
                    return {
                        isValid: false,
                        error: 'Impossible movement speed',
                        severity: 'high',
                    };
                }
            }
        }
        // Validate actions
        const actionCount = [input.split, input.eject, input.cashout].filter(Boolean).length;
        if (actionCount > 1) {
            return {
                isValid: false,
                error: 'Multiple actions in single input',
                severity: 'medium',
            };
        }
        return { isValid: true };
    }
    // Store input in history for reconciliation
    storeInputInHistory(input, history) {
        history.inputs.push({
            ...input,
            serverTimestamp: Date.now(),
        });
        // Limit history size
        if (history.inputs.length > this.maxHistorySize) {
            history.inputs.shift();
        }
        history.lastProcessedSequence = input.sequenceNumber;
        history.lastValidationTime = Date.now();
    }
    // Update player state from validated input
    updatePlayerFromInput(player, input) {
        // Update target position
        if (input.targetX !== undefined && input.targetY !== undefined) {
            player.targetPosition = { x: input.targetX, y: input.targetY };
        }
        // Update input tracking
        player.lastInput = Date.now();
        player.inputCount = input.sequenceNumber;
    }
    // Create server state snapshot for reconciliation
    createServerSnapshot(player, tick) {
        const playerId = player.id;
        let snapshots = this.serverStateHistory.get(playerId);
        if (!snapshots) {
            snapshots = [];
            this.serverStateHistory.set(playerId, snapshots);
        }
        const snapshot = {
            tick,
            timestamp: Date.now(),
            position: player.cells.length > 0 ? { ...player.cells[0].position } : { x: 0, y: 0 },
            velocity: player.cells.length > 0 ? { ...player.cells[0].velocity } : { x: 0, y: 0 },
            mass: player.totalMass,
            cellCount: player.cells.length,
        };
        snapshots.push(snapshot);
        // Limit history size
        if (snapshots.length > this.maxHistorySize) {
            snapshots.shift();
        }
    }
    // Reconcile client state with server state
    reconcilePlayerState(playerId, clientTick, clientPosition) {
        const snapshots = this.serverStateHistory.get(playerId);
        if (!snapshots)
            return null;
        // Find server snapshot closest to client tick
        const serverSnapshot = this.findSnapshotByTick(snapshots, clientTick);
        if (!serverSnapshot)
            return null;
        // Calculate position difference
        const positionDiff = this.calculateDistance(clientPosition, serverSnapshot.position);
        // If difference is significant, send correction
        if (positionDiff > GAME_CONFIG.POSITION_TOLERANCE) {
            logger.warn(`Position mismatch for player ${playerId}: client=${JSON.stringify(clientPosition)}, server=${JSON.stringify(serverSnapshot.position)}, diff=${positionDiff}`);
            return {
                correctionNeeded: true,
                serverPosition: serverSnapshot.position,
                serverTick: serverSnapshot.tick,
                positionDifference: positionDiff,
            };
        }
        return {
            correctionNeeded: false,
            positionDifference: positionDiff,
        };
    }
    // Find snapshot by tick number
    findSnapshotByTick(snapshots, targetTick) {
        // Find closest snapshot to target tick
        let closest = null;
        let minDiff = Infinity;
        for (const snapshot of snapshots) {
            const diff = Math.abs(snapshot.tick - targetTick);
            if (diff < minDiff) {
                minDiff = diff;
                closest = snapshot;
            }
        }
        return closest;
    }
    // Helper methods
    isValidPosition(pos) {
        return typeof pos.x === 'number' &&
            typeof pos.y === 'number' &&
            !isNaN(pos.x) &&
            !isNaN(pos.y) &&
            isFinite(pos.x) &&
            isFinite(pos.y) &&
            pos.x >= -5000 && pos.x <= 5000 &&
            pos.y >= -5000 && pos.y <= 5000;
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    calculateMaxSpeed(mass) {
        // Speed decreases with mass (Agar.io physics)
        return Math.max(50, 200 - (mass * 0.1));
    }
    // Clean up old data
    cleanup(playerId) {
        this.playerInputHistory.delete(playerId);
        this.serverStateHistory.delete(playerId);
    }
    // Get reconciliation statistics
    getStats(playerId) {
        const inputHistory = this.playerInputHistory.get(playerId);
        if (!inputHistory)
            return null;
        return {
            totalInputs: inputHistory.inputs.length,
            suspiciousInputs: inputHistory.suspiciousInputCount,
            lastSequence: inputHistory.lastProcessedSequence,
            lastValidation: inputHistory.lastValidationTime,
        };
    }
}
// Singleton instance
export const inputReconciliation = new InputReconciliationSystem();
