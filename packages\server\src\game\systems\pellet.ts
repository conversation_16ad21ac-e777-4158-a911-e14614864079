import { GameState } from '../state.js'
import { Pellet, EntityType, Vector2 } from '../types.js'
import { GAME_CONFIG } from '../../config/lobby.js'
import { generateId } from '../../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'pellet-system' })

export class PelletSystem {
  private gameState: GameState
  private lastSpawnTime = 0
  private spawnInterval = 1000 / GAME_CONFIG.PELLET_RESPAWN_RATE // ms between spawns

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  update(deltaTime: number): void {
    const now = Date.now()
    
    // Check if we need to spawn more pellets
    if (now - this.lastSpawnTime >= this.spawnInterval) {
      this.maintainPelletCount()
      this.lastSpawnTime = now
    }

    // Clean up any expired pellets (if we add expiration later)
    this.cleanupExpiredPellets()
  }

  private maintainPelletCount(): void {
    const world = this.gameState.getWorld()
    const currentPelletCount = world.pellets.size
    const targetPelletCount = GAME_CONFIG.PELLET_COUNT

    if (currentPelletCount < targetPelletCount) {
      const pelletsToSpawn = Math.min(
        targetPelletCount - currentPelletCount,
        GAME_CONFIG.PELLET_RESPAWN_RATE // Don't spawn too many at once
      )

      for (let i = 0; i < pelletsToSpawn; i++) {
        this.spawnPellet()
      }
    }
  }

  private spawnPellet(): void {
    const world = this.gameState.getWorld()
    const position = this.findSafePelletPosition()

    const pellet: Pellet = {
      id: generateId(),
      type: EntityType.PELLET,
      position,
      radius: this.massToRadius(GAME_CONFIG.PELLET_MASS),
      mass: GAME_CONFIG.PELLET_MASS,
      color: this.getRandomPelletColor(),
    }

    world.pellets.set(pellet.id, pellet)
    world.quadTree.insert(pellet)
  }

  private findSafePelletPosition(): Vector2 {
    const world = this.gameState.getWorld()
    const bounds = world.quadTree.root.bounds
    const maxAttempts = 10
    const minDistanceFromPlayers = 50

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const x = Math.random() * (bounds.width - 100) + 50
      const y = Math.random() * (bounds.height - 100) + 50

      // Check if position is safe (not too close to players)
      let isSafe = true
      for (const player of world.players.values()) {
        for (const cell of player.cells) {
          const dx = cell.position.x - x
          const dy = cell.position.y - y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < minDistanceFromPlayers) {
            isSafe = false
            break
          }
        }
        if (!isSafe) break
      }

      if (isSafe) {
        return { x, y }
      }
    }

    // Fallback to random position
    return {
      x: Math.random() * (bounds.width - 100) + 50,
      y: Math.random() * (bounds.height - 100) + 50,
    }
  }

  private cleanupExpiredPellets(): void {
    // For now, pellets don't expire
    // This method is here for future expansion
  }

  private getRandomPelletColor(): string {
    const colors = [
      '#FF6B6B', // Red
      '#4ECDC4', // Teal
      '#45B7D1', // Blue
      '#96CEB4', // Green
      '#FFEAA7', // Yellow
      '#DDA0DD', // Plum
      '#98D8C8', // Mint
      '#F39C12', // Orange
      '#E74C3C', // Crimson
      '#3498DB', // Sky Blue
      '#2ECC71', // Emerald
      '#9B59B6', // Purple
      '#F1C40F', // Gold
      '#E67E22', // Carrot
      '#1ABC9C', // Turquoise
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  private massToRadius(mass: number): number {
    return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR
  }

  // Public methods for external access
  spawnPelletAt(position: Vector2): void {
    const world = this.gameState.getWorld()

    const pellet: Pellet = {
      id: generateId(),
      type: EntityType.PELLET,
      position,
      radius: this.massToRadius(GAME_CONFIG.PELLET_MASS),
      mass: GAME_CONFIG.PELLET_MASS,
      color: this.getRandomPelletColor(),
    }

    world.pellets.set(pellet.id, pellet)
    world.quadTree.insert(pellet)
  }

  removePellet(pelletId: string): void {
    const world = this.gameState.getWorld()
    const pellet = world.pellets.get(pelletId)
    
    if (pellet) {
      world.pellets.delete(pelletId)
      world.quadTree.remove(pellet)
    }
  }

  getPelletCount(): number {
    return this.gameState.getWorld().pellets.size
  }
}
