{"name": "@agario-solana/server", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix", "clean": "rm -rf dist node_modules", "test": "vitest"}, "dependencies": {"@coral-xyz/anchor": "^0.28.0", "@prisma/client": "^5.2.0", "@pythnetwork/client": "^2.19.0", "@solana/web3.js": "^1.87.6", "@types/jsonwebtoken": "^9.0.10", "bs58": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pino": "^8.15.0", "pino-pretty": "^10.2.0", "prisma": "^5.2.0", "redis": "^4.6.8", "socket.io": "^4.7.2", "tweetnacl": "^1.0.3", "zod": "^3.22.2"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/pg": "^8.10.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "tsx": "^3.12.7", "typescript": "^5.1.6", "vitest": "^0.34.3"}}