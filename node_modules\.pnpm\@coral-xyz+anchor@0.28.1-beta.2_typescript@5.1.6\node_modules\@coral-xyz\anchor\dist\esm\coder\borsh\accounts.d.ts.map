{"version": 3, "file": "accounts.d.ts", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/accounts.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAGhC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAI5C;;GAEG;AACH,qBAAa,kBAAkB,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,CACvD,YAAW,aAAa;IAExB;;OAEG;IACH,OAAO,CAAC,cAAc,CAAiB;IAEvC;;OAEG;IACH,OAAO,CAAC,GAAG,CAAM;gBAEE,GAAG,EAAE,GAAG;IAad,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAYlE,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;IAShD,SAAS,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;IAYnC,eAAe,CAAC,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,GAAG,CAAC;IAUvD,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,GAAG;IAUhD,IAAI,CAAC,UAAU,EAAE,UAAU,GAAG,MAAM;IAI3C;;;;OAIG;WACW,oBAAoB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;CAOzD"}