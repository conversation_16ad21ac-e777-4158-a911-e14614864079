import { GameState } from '../state.js'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>irus, 
  <PERSON>jectedMass, 
  <PERSON>tityType, 
  CollisionResult,
  GameEventType 
} from '../types.js'
import { GAME_CONFIG } from '../../config/lobby.js'
import { QuadTreeUtils } from '../quadtree.js'
import { generateId } from '../../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'collision-system' })

export class CollisionSystem {
  private gameState: GameState
  private collisionChecks = 0

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  update(deltaTime: number): void {
    this.collisionChecks = 0
    const world = this.gameState.getWorld()

    // Check collisions for all player cells
    for (const player of world.players.values()) {
      if (!player.isAlive) continue

      for (const cell of player.cells) {
        this.checkCellCollisions(cell)
      }
    }

    // Check ejected mass collisions
    for (const ejectedMass of world.ejectedMass.values()) {
      this.checkEjectedMassCollisions(ejectedMass)
    }
  }

  private checkCellCollisions(cell: PlayerCell): void {
    const world = this.gameState.getWorld()
    const searchRadius = cell.radius * 3 // Search area around cell

    // Get nearby entities using quadtree
    const nearbyEntities = QuadTreeUtils.getEntitiesInRadius(
      world.quadTree,
      cell.position.x,
      cell.position.y,
      searchRadius
    )

    for (const entity of nearbyEntities) {
      if (entity.id === cell.id) continue // Skip self

      const collision = this.checkCollision(cell, entity)
      if (collision.canConsume) {
        this.handleConsumption(collision)
      }
    }
  }

  private checkEjectedMassCollisions(ejectedMass: EjectedMass): void {
    const world = this.gameState.getWorld()
    const searchRadius = ejectedMass.radius * 2

    // Get nearby entities
    const nearbyEntities = QuadTreeUtils.getEntitiesInRadius(
      world.quadTree,
      ejectedMass.position.x,
      ejectedMass.position.y,
      searchRadius
    )

    for (const entity of nearbyEntities) {
      if (entity.id === ejectedMass.id) continue
      if (entity.type === EntityType.PLAYER_CELL) {
        const cell = entity as PlayerCell
        
        // Can't eat own ejected mass immediately
        if (cell.playerId === ejectedMass.ownerId && !ejectedMass.canBeEaten) {
          continue
        }

        const collision = this.checkCollision(ejectedMass, entity)
        if (collision.canConsume) {
          this.handleConsumption(collision)
        }
      }
    }
  }

  private checkCollision(entity1: Entity, entity2: Entity): CollisionResult {
    this.collisionChecks++

    const dx = entity1.position.x - entity2.position.x
    const dy = entity1.position.y - entity2.position.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    const combinedRadius = entity1.radius + entity2.radius

    const result: CollisionResult = {
      entity1,
      entity2,
      distance,
      canConsume: false,
    }

    // Check if entities are touching
    if (distance >= combinedRadius) {
      return result
    }

    // Determine consumption rules
    result.canConsume = this.canConsume(entity1, entity2)
    if (result.canConsume) {
      const [consumer, consumed] = this.determineConsumer(entity1, entity2)
      result.consumer = consumer
      result.consumed = consumed
    }

    return result
  }

  private canConsume(entity1: Entity, entity2: Entity): boolean {
    // Player cell consuming pellet
    if (entity1.type === EntityType.PLAYER_CELL && entity2.type === EntityType.PELLET) {
      return true
    }
    if (entity2.type === EntityType.PLAYER_CELL && entity1.type === EntityType.PELLET) {
      return true
    }

    // Player cell consuming ejected mass
    if (entity1.type === EntityType.PLAYER_CELL && entity2.type === EntityType.EJECTED_MASS) {
      const ejected = entity2 as EjectedMass
      const cell = entity1 as PlayerCell
      return ejected.canBeEaten || cell.playerId !== ejected.ownerId
    }
    if (entity2.type === EntityType.PLAYER_CELL && entity1.type === EntityType.EJECTED_MASS) {
      const ejected = entity1 as EjectedMass
      const cell = entity2 as PlayerCell
      return ejected.canBeEaten || cell.playerId !== ejected.ownerId
    }

    // Player cell consuming another player cell
    if (entity1.type === EntityType.PLAYER_CELL && entity2.type === EntityType.PLAYER_CELL) {
      const cell1 = entity1 as PlayerCell
      const cell2 = entity2 as PlayerCell
      
      // Can't consume own cells
      if (cell1.playerId === cell2.playerId) return false
      
      // Must be significantly larger to consume
      return cell1.mass >= cell2.mass * GAME_CONFIG.CONSUME_MASS_RATIO ||
             cell2.mass >= cell1.mass * GAME_CONFIG.CONSUME_MASS_RATIO
    }

    // Player cell hitting virus
    if (entity1.type === EntityType.PLAYER_CELL && entity2.type === EntityType.VIRUS) {
      const cell = entity1 as PlayerCell
      return cell.mass >= GAME_CONFIG.VIRUS_SPLIT_THRESHOLD
    }
    if (entity2.type === EntityType.PLAYER_CELL && entity1.type === EntityType.VIRUS) {
      const cell = entity2 as PlayerCell
      return cell.mass >= GAME_CONFIG.VIRUS_SPLIT_THRESHOLD
    }

    return false
  }

  private determineConsumer(entity1: Entity, entity2: Entity): [Entity, Entity] {
    // Pellets are always consumed
    if (entity1.type === EntityType.PELLET) return [entity2, entity1]
    if (entity2.type === EntityType.PELLET) return [entity1, entity2]

    // Ejected mass is always consumed
    if (entity1.type === EntityType.EJECTED_MASS) return [entity2, entity1]
    if (entity2.type === EntityType.EJECTED_MASS) return [entity1, entity2]

    // Player cells - larger consumes smaller
    if (entity1.type === EntityType.PLAYER_CELL && entity2.type === EntityType.PLAYER_CELL) {
      return entity1.mass > entity2.mass ? [entity1, entity2] : [entity2, entity1]
    }

    // Virus interactions - player cell triggers virus
    if (entity1.type === EntityType.VIRUS) return [entity1, entity2]
    if (entity2.type === EntityType.VIRUS) return [entity2, entity1]

    return [entity1, entity2]
  }

  private handleConsumption(collision: CollisionResult): void {
    if (!collision.consumer || !collision.consumed) return

    const world = this.gameState.getWorld()

    switch (collision.consumed.type) {
      case EntityType.PELLET:
        this.handlePelletConsumption(collision.consumer as PlayerCell, collision.consumed as Pellet)
        break

      case EntityType.EJECTED_MASS:
        this.handleEjectedMassConsumption(collision.consumer as PlayerCell, collision.consumed as EjectedMass)
        break

      case EntityType.PLAYER_CELL:
        this.handlePlayerCellConsumption(collision.consumer as PlayerCell, collision.consumed as PlayerCell)
        break

      case EntityType.VIRUS:
        this.handleVirusCollision(collision.consumed as Virus, collision.consumer as PlayerCell)
        break
    }
  }

  private handlePelletConsumption(cell: PlayerCell, pellet: Pellet): void {
    const world = this.gameState.getWorld()
    
    // Add mass to cell
    cell.mass += pellet.mass
    cell.radius = this.massToRadius(cell.mass)

    // Remove pellet from world
    world.pellets.delete(pellet.id)
    world.quadTree.remove(pellet)

    // Create event
    const player = world.players.get(cell.playerId)
    if (player) {
      // Add event to game state events
      // This would be handled by the GameState class
    }
  }

  private handleEjectedMassConsumption(cell: PlayerCell, ejectedMass: EjectedMass): void {
    const world = this.gameState.getWorld()
    
    // Add mass to cell
    cell.mass += ejectedMass.mass
    cell.radius = this.massToRadius(cell.mass)

    // Remove ejected mass from world
    world.ejectedMass.delete(ejectedMass.id)
    world.quadTree.remove(ejectedMass)
  }

  private handlePlayerCellConsumption(consumerCell: PlayerCell, consumedCell: PlayerCell): void {
    const world = this.gameState.getWorld()
    
    // Add mass to consumer
    consumerCell.mass += consumedCell.mass
    consumerCell.radius = this.massToRadius(consumerCell.mass)

    // Remove consumed cell from world
    world.quadTree.remove(consumedCell)

    // Remove cell from consumed player
    const consumedPlayer = world.players.get(consumedCell.playerId)
    if (consumedPlayer) {
      consumedPlayer.cells = consumedPlayer.cells.filter(c => c.id !== consumedCell.id)
      
      // Check if player is eliminated (no cells left)
      if (consumedPlayer.cells.length === 0) {
        this.eliminatePlayer(consumedPlayer.id, consumerCell.playerId)
      }
    }

    // Create elimination event
    // This would be handled by the GameState class
  }

  private handleVirusCollision(virus: Virus, cell: PlayerCell): void {
    // Import and use VirusSystem to handle the collision
    // For now, we'll handle it directly here
    const world = this.gameState.getWorld()
    const player = world.players.get(cell.playerId)

    if (!player) return

    // Check if cell is large enough to trigger virus
    if (cell.mass < GAME_CONFIG.VIRUS_SPLIT_THRESHOLD) {
      return
    }

    logger.debug(`Player cell ${cell.id} hit virus ${virus.id}`)

    // This would normally call the VirusSystem.handleVirusCollision method
    // For now, we'll implement basic virus collision logic here

    // Split the player cell (simplified version)
    this.splitPlayerCellFromVirus(cell, virus)

    // Feed the virus
    virus.feedCount++
  }

  private splitPlayerCellFromVirus(cell: PlayerCell, virus: Virus): void {
    const world = this.gameState.getWorld()
    const player = world.players.get(cell.playerId)

    if (!player) return

    // Calculate split mass
    const splitMass = cell.mass / GAME_CONFIG.VIRUS_SPLIT_COUNT
    const splitRadius = this.massToRadius(splitMass)

    // Remove original cell
    world.quadTree.remove(cell)
    player.cells = player.cells.filter(c => c.id !== cell.id)

    // Create multiple smaller cells
    for (let i = 0; i < GAME_CONFIG.VIRUS_SPLIT_COUNT; i++) {
      const angle = (Math.PI * 2 * i) / GAME_CONFIG.VIRUS_SPLIT_COUNT
      const distance = virus.radius + splitRadius + 10

      const newCell: PlayerCell = {
        id: generateId(),
        type: EntityType.PLAYER_CELL,
        playerId: cell.playerId,
        position: {
          x: virus.position.x + Math.cos(angle) * distance,
          y: virus.position.y + Math.sin(angle) * distance,
        },
        radius: splitRadius,
        mass: splitMass,
        velocity: {
          x: Math.cos(angle) * GAME_CONFIG.VIRUS_SPLIT_VELOCITY,
          y: Math.sin(angle) * GAME_CONFIG.VIRUS_SPLIT_VELOCITY,
        },
        targetPosition: cell.targetPosition,
        canMerge: false,
        mergeTime: Date.now() + GAME_CONFIG.MERGE_COOLDOWN,
        splitCooldown: Date.now() + GAME_CONFIG.SPLIT_COOLDOWN,
        lastSplit: Date.now(),
      }

      // Ensure new cell is within bounds
      const bounds = world.quadTree.root.bounds
      newCell.position.x = Math.max(newCell.radius, Math.min(bounds.width - newCell.radius, newCell.position.x))
      newCell.position.y = Math.max(newCell.radius, Math.min(bounds.height - newCell.radius, newCell.position.y))

      player.cells.push(newCell)
      world.quadTree.insert(newCell)
    }

    logger.info(`Player ${player.id} split by virus into ${GAME_CONFIG.VIRUS_SPLIT_COUNT} cells`)
  }

  private eliminatePlayer(eliminatedPlayerId: string, eliminatorPlayerId: string): void {
    const world = this.gameState.getWorld()
    const eliminatedPlayer = world.players.get(eliminatedPlayerId)
    const eliminatorPlayer = world.players.get(eliminatorPlayerId)

    if (!eliminatedPlayer || !eliminatorPlayer) return

    // Transfer value to eliminator
    eliminatorPlayer.currentValue += eliminatedPlayer.currentValue

    // Remove eliminated player
    this.gameState.removePlayer(eliminatedPlayerId, 'eliminated', eliminatorPlayerId)

    logger.info(`Player ${eliminatedPlayerId} eliminated by ${eliminatorPlayerId}`)
  }

  private massToRadius(mass: number): number {
    return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR
  }

  getCollisionChecks(): number {
    return this.collisionChecks
  }
}
