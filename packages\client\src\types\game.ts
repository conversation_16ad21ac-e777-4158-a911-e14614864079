// Shared game types between client and server
// These types should match the server types exactly

export interface Vector2 {
  x: number
  y: number
}

export interface Bounds {
  x: number
  y: number
  width: number
  height: number
}

export enum EntityType {
  PLAYER_CELL = 'player_cell',
  PELLET = 'pellet',
  VIRUS = 'virus',
  EJECTED_MASS = 'ejected_mass',
}

export interface Entity {
  id: string
  position: Vector2
  radius: number
  mass: number
  type: EntityType
}

export interface PlayerCell extends Entity {
  type: EntityType.PLAYER_CELL
  playerId: string
  velocity: Vector2
  targetPosition: Vector2
  canMerge: boolean
  mergeTime: number
  splitCooldown: number
  lastSplit: number
}

export interface Pellet extends Entity {
  type: EntityType.PELLET
  color: string
  respawnTime?: number
}

export interface Virus extends Entity {
  type: EntityType.VIRUS
  color: string
  mass: number
  popThreshold: number
  canPop: boolean
}

export interface EjectedMass extends Entity {
  type: EntityType.EJECTED_MASS
  playerId: string
  velocity: Vector2
  decayTime: number
  canConsume: boolean
}

export interface Player {
  id: string
  sessionToken: string
  socketId: string
  wallet: string
  username?: string
  
  // Game state
  cells: PlayerCell[]
  totalMass: number
  score: number
  
  // Position and movement
  targetPosition: Vector2
  lastInput: number
  inputCount: number
  
  // Wager and value
  wagerAmount: number // SOL
  currentValue: number // SOL
  initialMass: number
  
  // Status
  isAlive: boolean
  canCashout: boolean
  cashoutStartTime?: number
  
  // Anti-cheat
  suspiciousActivity: boolean
  lastValidatedPosition: Vector2
  lastValidatedTime: number
}

export interface PlayerInput {
  playerId: string
  sessionToken: string
  timestamp: number
  sequenceNumber: number
  
  // Movement
  targetX?: number
  targetY?: number
  
  // Actions
  split?: boolean
  eject?: boolean
  cashout?: boolean
  
  // Validation
  signature?: string
}

export enum GameEventType {
  PLAYER_JOINED = 'player_joined',
  PLAYER_LEFT = 'player_left',
  PLAYER_ELIMINATED = 'player_eliminated',
  PLAYER_SPLIT = 'player_split',
  PLAYER_MERGED = 'player_merged',
  PLAYER_EJECTED = 'player_ejected',
  PLAYER_CONSUMED_PELLET = 'player_consumed_pellet',
  PLAYER_CONSUMED_VIRUS = 'player_consumed_virus',
  PLAYER_CONSUMED_PLAYER = 'player_consumed_player',
  PLAYER_CASHOUT_STARTED = 'player_cashout_started',
  PLAYER_CASHOUT_COMPLETED = 'player_cashout_completed',
  PLAYER_CASHOUT_CANCELLED = 'player_cashout_cancelled',
  VIRUS_POPPED = 'virus_popped',
  PELLET_RESPAWNED = 'pellet_respawned',
}

export interface GameEvent {
  type: GameEventType
  timestamp: number
  data: any
  playerId?: string
  sessionId?: string
}

export interface GameSnapshot {
  timestamp: number
  tick: number
  
  // Visible entities (within player's view)
  players: Partial<Player>[]
  pellets: Pellet[]
  viruses: Virus[]
  ejectedMass: EjectedMass[]
  
  // Player-specific data
  playerData?: {
    totalMass: number
    score: number
    currentValue: number
    canCashout: boolean
    cashoutProgress?: number
  }
  
  // Lobby information
  lobbyStats: {
    totalPlayers: number
    totalValue: number
  }
}

export interface GameDelta {
  timestamp: number
  tick: number
  
  // Changed entities
  playersChanged: Partial<Player>[]
  pelletsChanged: Pellet[]
  virusesChanged: Virus[]
  ejectedMassChanged: EjectedMass[]
  
  // Removed entities
  playersRemoved: string[]
  pelletsRemoved: string[]
  virusesRemoved: string[]
  ejectedMassRemoved: string[]
  
  // Events
  events: GameEvent[]
}

// Client-specific types
export interface ClientState {
  isConnected: boolean
  playerId?: string
  sessionToken?: string
  currentTick: number
  serverTime: number
  ping: number
  
  // Input state
  inputSequence: number
  pendingInputs: PlayerInput[]
  
  // Game state
  gameSnapshot?: GameSnapshot
  localPlayer?: Player
  
  // UI state
  showHUD: boolean
  showMenu: boolean
  isInGame: boolean
}

export interface RenderState {
  camera: {
    x: number
    y: number
    zoom: number
    targetZoom: number
  }
  viewport: {
    width: number
    height: number
  }
  entities: {
    players: Map<string, PlayerCell[]>
    pellets: Map<string, Pellet>
    viruses: Map<string, Virus>
    ejectedMass: Map<string, EjectedMass>
  }
  effects: ParticleEffect[]
}

export interface ParticleEffect {
  id: string
  type: 'split' | 'eject' | 'consume' | 'explosion'
  position: Vector2
  velocity: Vector2
  life: number
  maxLife: number
  color: string
  size: number
}

// Socket event types
export interface SocketEvents {
  // Client to server
  join_game: (data: JoinGameData) => void
  player_input: (input: PlayerInput) => void
  client_state: (state: ClientStateData) => void
  leave_game: () => void
  ping: () => void
  
  // Server to client
  game_snapshot: (snapshot: GameSnapshot) => void
  game_delta: (delta: GameDelta) => void
  player_joined: (player: Partial<Player>) => void
  player_left: (playerId: string) => void
  game_event: (event: GameEvent) => void
  error: (error: { message: string; code?: string }) => void
  pong: (data: { timestamp: number }) => void
}

export interface JoinGameData {
  wallet: string
  signature: string
  wagerAmount: number
  username?: string
}

export interface ClientStateData {
  tick: number
  position: Vector2
  timestamp: number
}
