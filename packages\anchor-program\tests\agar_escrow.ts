import * as anchor from "@coral-xyz/anchor";
import { Program } from "@coral-xyz/anchor";
import { AgarEscrow } from "../target/types/agar_escrow";
import { PublicKey, Keypair, SystemProgram, LAMPORTS_PER_SOL } from "@solana/web3.js";
import { expect } from "chai";

describe("agar_escrow", () => {
  // Configure the client to use the local cluster.
  anchor.setProvider(anchor.AnchorProvider.env());

  const program = anchor.workspace.AgarEscrow as Program<AgarEscrow>;
  const provider = anchor.getProvider();

  // Test accounts
  let authority: Keypair;
  let user1: Keypair;
  let user2: Keypair;
  let globalConfigPda: PublicKey;
  let treasuryPda: PublicKey;
  let lobby1Pda: PublicKey;
  let lobby2Pda: PublicKey;

  // Test constants
  const PLATFORM_FEE_BPS = 500; // 5%
  const ORACLE_SAFETY_MARGIN_BPS = 50; // 0.5%
  const TIER_1 = 1;
  const TIER_2 = 2;
  const MIN_WAGER_TIER_1 = 0.1 * LAMPORTS_PER_SOL; // 0.1 SOL
  const MIN_WAGER_TIER_2 = 1.0 * LAMPORTS_PER_SOL; // 1.0 SOL

  before(async () => {
    // Generate test keypairs
    authority = Keypair.generate();
    user1 = Keypair.generate();
    user2 = Keypair.generate();

    // Airdrop SOL to test accounts
    await provider.connection.requestAirdrop(authority.publicKey, 10 * LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(user1.publicKey, 5 * LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(user2.publicKey, 5 * LAMPORTS_PER_SOL);

    // Wait for airdrops to confirm
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Derive PDAs
    [globalConfigPda] = PublicKey.findProgramAddressSync(
      [Buffer.from("global_config")],
      program.programId
    );

    [treasuryPda] = PublicKey.findProgramAddressSync(
      [Buffer.from("treasury")],
      program.programId
    );

    [lobby1Pda] = PublicKey.findProgramAddressSync(
      [Buffer.from("lobby"), Buffer.from([TIER_1])],
      program.programId
    );

    [lobby2Pda] = PublicKey.findProgramAddressSync(
      [Buffer.from("lobby"), Buffer.from([TIER_2])],
      program.programId
    );
  });

  it("Initializes global configuration", async () => {
    const tx = await program.methods
      .initializeGlobal(PLATFORM_FEE_BPS, ORACLE_SAFETY_MARGIN_BPS)
      .accounts({
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([authority])
      .rpc();

    console.log("Initialize global tx:", tx);

    // Verify global config
    const globalConfig = await program.account.globalConfig.fetch(globalConfigPda);
    expect(globalConfig.authority.toString()).to.equal(authority.publicKey.toString());
    expect(globalConfig.platformFeeBps).to.equal(PLATFORM_FEE_BPS);
    expect(globalConfig.oracleSafetyMarginBps).to.equal(ORACLE_SAFETY_MARGIN_BPS);
    expect(globalConfig.paused).to.be.false;
  });

  it("Initializes treasury", async () => {
    const tx = await program.methods
      .initializeTreasury()
      .accounts({
        treasury: treasuryPda,
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([authority])
      .rpc();

    console.log("Initialize treasury tx:", tx);

    // Verify treasury exists
    const treasuryInfo = await provider.connection.getAccountInfo(treasuryPda);
    expect(treasuryInfo).to.not.be.null;
  });

  it("Creates lobbies", async () => {
    // Create tier 1 lobby
    const tx1 = await program.methods
      .createLobby(TIER_1, new anchor.BN(MIN_WAGER_TIER_1))
      .accounts({
        lobby: lobby1Pda,
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([authority])
      .rpc();

    console.log("Create lobby 1 tx:", tx1);

    // Create tier 2 lobby
    const tx2 = await program.methods
      .createLobby(TIER_2, new anchor.BN(MIN_WAGER_TIER_2))
      .accounts({
        lobby: lobby2Pda,
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([authority])
      .rpc();

    console.log("Create lobby 2 tx:", tx2);

    // Verify lobbies
    const lobby1 = await program.account.lobby.fetch(lobby1Pda);
    expect(lobby1.tier).to.equal(TIER_1);
    expect(lobby1.minWagerLamports.toString()).to.equal(MIN_WAGER_TIER_1.toString());
    expect(lobby1.totalPool.toString()).to.equal("0");
    expect(lobby1.activePlayers).to.equal(0);

    const lobby2 = await program.account.lobby.fetch(lobby2Pda);
    expect(lobby2.tier).to.equal(TIER_2);
    expect(lobby2.minWagerLamports.toString()).to.equal(MIN_WAGER_TIER_2.toString());
  });

  it("Handles user deposits", async () => {
    const depositAmount = 2 * LAMPORTS_PER_SOL;

    const [userStatePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("user_state"), user1.publicKey.toBuffer()],
      program.programId
    );

    const tx = await program.methods
      .deposit(new anchor.BN(depositAmount))
      .accounts({
        userState: userStatePda,
        treasury: treasuryPda,
        globalConfig: globalConfigPda,
        user: user1.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([user1])
      .rpc();

    console.log("Deposit tx:", tx);

    // Verify user state
    const userState = await program.account.userState.fetch(userStatePda);
    expect(userState.wallet.toString()).to.equal(user1.publicKey.toString());
    expect(userState.depositedBalance.toString()).to.equal(depositAmount.toString());
    expect(userState.totalDeposited.toString()).to.equal(depositAmount.toString());
    expect(userState.totalWithdrawn.toString()).to.equal("0");

    // Verify treasury balance increased
    const treasuryInfo = await provider.connection.getAccountInfo(treasuryPda);
    expect(treasuryInfo.lamports).to.be.greaterThan(depositAmount - 10000); // Account for rent
  });

  it("Handles lobby entry", async () => {
    const wagerAmount = MIN_WAGER_TIER_1;
    const sessionToken = Array.from(crypto.getRandomValues(new Uint8Array(32)));

    const [userStatePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("user_state"), user1.publicKey.toBuffer()],
      program.programId
    );

    const [sessionPda] = PublicKey.findProgramAddressSync(
      [Buffer.from("session"), user1.publicKey.toBuffer(), lobby1Pda.toBuffer()],
      program.programId
    );

    const tx = await program.methods
      .enterLobby(new anchor.BN(wagerAmount), sessionToken)
      .accounts({
        session: sessionPda,
        userState: userStatePda,
        lobby: lobby1Pda,
        globalConfig: globalConfigPda,
        user: user1.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([user1])
      .rpc();

    console.log("Enter lobby tx:", tx);

    // Verify session created
    const session = await program.account.session.fetch(sessionPda);
    expect(session.user.toString()).to.equal(user1.publicKey.toString());
    expect(session.lobby.toString()).to.equal(lobby1Pda.toString());
    expect(session.wagerAmount.toString()).to.equal(wagerAmount.toString());
    expect(session.initialMass).to.be.greaterThan(100); // Base mass + wager bonus

    // Verify lobby updated
    const lobby = await program.account.lobby.fetch(lobby1Pda);
    expect(lobby.totalPool.toString()).to.equal(wagerAmount.toString());
    expect(lobby.activePlayers).to.equal(1);

    // Verify user balance decreased
    const userState = await program.account.userState.fetch(userStatePda);
    expect(userState.depositedBalance.toString()).to.equal((2 * LAMPORTS_PER_SOL - wagerAmount).toString());
  });

  it("Handles withdrawal of unused funds", async () => {
    const withdrawAmount = 0.5 * LAMPORTS_PER_SOL;

    const [userStatePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("user_state"), user1.publicKey.toBuffer()],
      program.programId
    );

    const userBalanceBefore = await provider.connection.getBalance(user1.publicKey);

    const tx = await program.methods
      .withdrawUnused(new anchor.BN(withdrawAmount))
      .accounts({
        userState: userStatePda,
        treasury: treasuryPda,
        globalConfig: globalConfigPda,
        user: user1.publicKey,
      })
      .signers([user1])
      .rpc();

    console.log("Withdraw unused tx:", tx);

    // Verify user balance increased (minus tx fees)
    const userBalanceAfter = await provider.connection.getBalance(user1.publicKey);
    expect(userBalanceAfter).to.be.greaterThan(userBalanceBefore + withdrawAmount - 10000);

    // Verify user state updated
    const userState = await program.account.userState.fetch(userStatePda);
    expect(userState.totalWithdrawn.toString()).to.equal(withdrawAmount.toString());
  });

  it("Handles admin pause functionality", async () => {
    // Pause the system
    const tx1 = await program.methods
      .setPause(true)
      .accounts({
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
      })
      .signers([authority])
      .rpc();

    console.log("Set pause tx:", tx1);

    // Verify paused
    const globalConfig = await program.account.globalConfig.fetch(globalConfigPda);
    expect(globalConfig.paused).to.be.true;

    // Try to deposit while paused (should fail)
    const [userStatePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("user_state"), user2.publicKey.toBuffer()],
      program.programId
    );

    try {
      await program.methods
        .deposit(new anchor.BN(LAMPORTS_PER_SOL))
        .accounts({
          userState: userStatePda,
          treasury: treasuryPda,
          globalConfig: globalConfigPda,
          user: user2.publicKey,
          systemProgram: SystemProgram.programId,
        })
        .signers([user2])
        .rpc();
      
      expect.fail("Should have failed due to pause");
    } catch (error) {
      expect(error.message).to.include("GlobalPaused");
    }

    // Unpause
    const tx2 = await program.methods
      .setPause(false)
      .accounts({
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
      })
      .signers([authority])
      .rpc();

    console.log("Unset pause tx:", tx2);
  });

  it("Handles fee updates", async () => {
    const newPlatformFee = 750; // 7.5%
    const newOracleMargin = 100; // 1%

    const tx = await program.methods
      .updateFees(newPlatformFee, newOracleMargin)
      .accounts({
        globalConfig: globalConfigPda,
        authority: authority.publicKey,
      })
      .signers([authority])
      .rpc();

    console.log("Update fees tx:", tx);

    // Verify fees updated
    const globalConfig = await program.account.globalConfig.fetch(globalConfigPda);
    expect(globalConfig.platformFeeBps).to.equal(newPlatformFee);
    expect(globalConfig.oracleSafetyMarginBps).to.equal(newOracleMargin);
  });
});
