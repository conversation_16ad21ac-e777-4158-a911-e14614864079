import pino from 'pino'
import { validateEnv } from '../config/env.js'

const env = validateEnv()

// Create base logger configuration
const loggerConfig: pino.LoggerOptions = {
  name: 'agario-solana-server',
  level: env.NODE_ENV === 'development' ? 'debug' : 'info',
  
  // Production logging configuration
  ...(env.NODE_ENV === 'production' ? {
    // Structured JSON logging for production
    serializers: pino.stdSerializers,
    redact: {
      paths: ['req.headers.authorization', 'wallet.privateKey', 'signature'],
      censor: '[REDACTED]',
    },
    
    // Performance optimizations
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label) => ({ level: label }),
    },
  } : {
    // Pretty printing for development
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname',
        singleLine: true,
      },
    },
  }),
}

// Create base logger
export const logger = pino(loggerConfig)

// Create specialized loggers for different components
export const gameLogger = logger.child({ component: 'game' })
export const networkLogger = logger.child({ component: 'network' })
export const solanaLogger = logger.child({ component: 'solana' })
export const authLogger = logger.child({ component: 'auth' })
export const dbLogger = logger.child({ component: 'database' })

// Error categorization
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NETWORK = 'network',
  DATABASE = 'database',
  SOLANA = 'solana',
  GAME_LOGIC = 'game_logic',
  ANTI_CHEAT = 'anti_cheat',
  SYSTEM = 'system',
  UNKNOWN = 'unknown',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Structured error logging
export interface ErrorContext {
  category: ErrorCategory
  severity: ErrorSeverity
  playerId?: string
  walletAddress?: string
  sessionToken?: string
  lobbyTier?: number
  ip?: string
  userAgent?: string
  additionalData?: Record<string, any>
}

// Enhanced error logging function
export function logError(
  error: Error | string,
  context: ErrorContext,
  logger: pino.Logger = gameLogger
): void {
  const errorMessage = error instanceof Error ? error.message : error
  const errorStack = error instanceof Error ? error.stack : undefined
  
  const logData = {
    error: errorMessage,
    stack: errorStack,
    category: context.category,
    severity: context.severity,
    playerId: context.playerId,
    walletAddress: context.walletAddress,
    sessionToken: context.sessionToken,
    lobbyTier: context.lobbyTier,
    ip: context.ip,
    userAgent: context.userAgent,
    timestamp: Date.now(),
    ...context.additionalData,
  }

  // Log at appropriate level based on severity
  switch (context.severity) {
    case ErrorSeverity.CRITICAL:
      logger.fatal(logData, `CRITICAL ERROR: ${errorMessage}`)
      break
    case ErrorSeverity.HIGH:
      logger.error(logData, `HIGH SEVERITY: ${errorMessage}`)
      break
    case ErrorSeverity.MEDIUM:
      logger.warn(logData, `MEDIUM SEVERITY: ${errorMessage}`)
      break
    case ErrorSeverity.LOW:
    default:
      logger.info(logData, `LOW SEVERITY: ${errorMessage}`)
      break
  }
}

// Performance metrics logging
export interface PerformanceMetrics {
  operation: string
  duration: number
  success: boolean
  entityCount?: number
  playerCount?: number
  memoryUsage?: NodeJS.MemoryUsage
  additionalMetrics?: Record<string, number>
}

export function logPerformance(
  metrics: PerformanceMetrics,
  logger: pino.Logger = gameLogger
): void {
  logger.info({
    type: 'performance',
    operation: metrics.operation,
    duration: metrics.duration,
    success: metrics.success,
    entityCount: metrics.entityCount,
    playerCount: metrics.playerCount,
    memoryUsage: metrics.memoryUsage,
    timestamp: Date.now(),
    ...metrics.additionalMetrics,
  }, `Performance: ${metrics.operation} (${metrics.duration}ms)`)
}

// Security event logging
export interface SecurityEvent {
  type: 'auth_failure' | 'rate_limit' | 'suspicious_activity' | 'anti_cheat' | 'unauthorized_access'
  severity: ErrorSeverity
  playerId?: string
  walletAddress?: string
  ip?: string
  userAgent?: string
  details: string
  additionalData?: Record<string, any>
}

export function logSecurityEvent(
  event: SecurityEvent,
  logger: pino.Logger = authLogger
): void {
  const logData = {
    type: 'security_event',
    eventType: event.type,
    severity: event.severity,
    playerId: event.playerId,
    walletAddress: event.walletAddress,
    ip: event.ip,
    userAgent: event.userAgent,
    details: event.details,
    timestamp: Date.now(),
    ...event.additionalData,
  }

  switch (event.severity) {
    case ErrorSeverity.CRITICAL:
      logger.fatal(logData, `SECURITY CRITICAL: ${event.details}`)
      break
    case ErrorSeverity.HIGH:
      logger.error(logData, `SECURITY HIGH: ${event.details}`)
      break
    case ErrorSeverity.MEDIUM:
      logger.warn(logData, `SECURITY MEDIUM: ${event.details}`)
      break
    case ErrorSeverity.LOW:
    default:
      logger.info(logData, `SECURITY LOW: ${event.details}`)
      break
  }
}

// Game event logging
export interface GameEvent {
  type: 'player_join' | 'player_leave' | 'player_death' | 'cashout_start' | 'cashout_complete' | 'split' | 'merge' | 'consume'
  playerId: string
  walletAddress?: string
  lobbyTier: number
  details?: string
  gameData?: Record<string, any>
}

export function logGameEvent(
  event: GameEvent,
  logger: pino.Logger = gameLogger
): void {
  logger.info({
    type: 'game_event',
    eventType: event.type,
    playerId: event.playerId,
    walletAddress: event.walletAddress,
    lobbyTier: event.lobbyTier,
    details: event.details,
    timestamp: Date.now(),
    ...event.gameData,
  }, `Game Event: ${event.type} - ${event.details || 'No details'}`)
}

// Transaction logging
export interface TransactionEvent {
  type: 'session_start' | 'cashout_request' | 'cashout_complete' | 'refund'
  transactionId?: string
  walletAddress: string
  amount: number
  solPrice?: number
  status: 'pending' | 'success' | 'failed'
  error?: string
  additionalData?: Record<string, any>
}

export function logTransaction(
  event: TransactionEvent,
  logger: pino.Logger = solanaLogger
): void {
  logger.info({
    type: 'transaction',
    eventType: event.type,
    transactionId: event.transactionId,
    walletAddress: event.walletAddress,
    amount: event.amount,
    solPrice: event.solPrice,
    status: event.status,
    error: event.error,
    timestamp: Date.now(),
    ...event.additionalData,
  }, `Transaction: ${event.type} - ${event.status} (${event.amount} SOL)`)
}

// Metrics collection for monitoring
export class MetricsCollector {
  private metrics = new Map<string, number>()
  private counters = new Map<string, number>()
  private timers = new Map<string, number>()

  // Increment a counter
  increment(name: string, value: number = 1): void {
    const current = this.counters.get(name) || 0
    this.counters.set(name, current + value)
  }

  // Set a gauge value
  gauge(name: string, value: number): void {
    this.metrics.set(name, value)
  }

  // Start a timer
  startTimer(name: string): void {
    this.timers.set(name, Date.now())
  }

  // End a timer and record duration
  endTimer(name: string): number {
    const startTime = this.timers.get(name)
    if (!startTime) return 0
    
    const duration = Date.now() - startTime
    this.timers.delete(name)
    this.gauge(`${name}_duration`, duration)
    
    return duration
  }

  // Get all metrics
  getAllMetrics(): Record<string, number> {
    return {
      ...Object.fromEntries(this.metrics),
      ...Object.fromEntries(this.counters),
    }
  }

  // Log metrics periodically
  logMetrics(logger: pino.Logger = gameLogger): void {
    const allMetrics = this.getAllMetrics()
    
    logger.info({
      type: 'metrics',
      metrics: allMetrics,
      timestamp: Date.now(),
    }, 'System Metrics')
  }

  // Reset all metrics
  reset(): void {
    this.metrics.clear()
    this.counters.clear()
    this.timers.clear()
  }
}

// Global metrics collector
export const metrics = new MetricsCollector()

// Start periodic metrics logging
if (env.NODE_ENV === 'production') {
  setInterval(() => {
    metrics.logMetrics()
  }, 60000) // Log metrics every minute
}

// Graceful shutdown logging
export function setupGracefulShutdown(): void {
  const shutdown = (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`)
    
    // Log final metrics
    metrics.logMetrics()
    
    // Flush logs
    logger.flush()
    
    process.exit(0)
  }

  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGINT', () => shutdown('SIGINT'))
}

// Export default logger
export default logger
