import { Button } from './Button'
import { Panel } from './Panel'
import { useWalletStore } from '../../store/wallet-store'

export interface DepositFlowOptions {
  onComplete: (amount: number) => void
  onCancel: () => void
  minAmount?: number
  maxAmount?: number
}

export class DepositFlow {
  private element: HTMLElement
  private panel: Panel
  private amountInput: HTMLInputElement
  private balanceDisplay: HTMLElement
  private depositButton: Button
  private cancelButton: Button
  private options: DepositFlowOptions
  private currentBalance: number = 0
  private solPrice: number = 100 // Mock SOL price in USD

  constructor(options: DepositFlowOptions) {
    this.options = options
    this.createElement()
    this.setupEventListeners()
    this.updateBalance()
  }

  private createElement(): void {
    this.element = document.createElement('div')
    this.element.className = 'deposit-flow-overlay'
    
    this.panel = new Panel({
      title: 'Deposit SOL',
      className: 'deposit-flow-panel'
    })

    // Create form content
    const formContent = document.createElement('div')
    formContent.className = 'deposit-flow-content'
    
    // Balance display
    const balanceSection = document.createElement('div')
    balanceSection.className = 'balance-section'
    
    const balanceLabel = document.createElement('div')
    balanceLabel.className = 'balance-label'
    balanceLabel.textContent = 'Current Balance'
    
    this.balanceDisplay = document.createElement('div')
    this.balanceDisplay.className = 'balance-display'
    
    balanceSection.appendChild(balanceLabel)
    balanceSection.appendChild(this.balanceDisplay)
    
    // Amount input
    const inputGroup = document.createElement('div')
    inputGroup.className = 'input-group'
    
    const label = document.createElement('label')
    label.textContent = 'Deposit Amount'
    label.htmlFor = 'amount-input'
    label.className = 'input-label'
    
    const inputWrapper = document.createElement('div')
    inputWrapper.className = 'input-wrapper'
    
    this.amountInput = document.createElement('input')
    this.amountInput.type = 'number'
    this.amountInput.id = 'amount-input'
    this.amountInput.className = 'amount-input'
    this.amountInput.placeholder = '0.00'
    this.amountInput.min = (this.options.minAmount || 0.01).toString()
    this.amountInput.max = (this.options.maxAmount || 100).toString()
    this.amountInput.step = '0.01'
    
    const solLabel = document.createElement('span')
    solLabel.className = 'currency-label'
    solLabel.textContent = 'SOL'
    
    inputWrapper.appendChild(this.amountInput)
    inputWrapper.appendChild(solLabel)
    
    const usdDisplay = document.createElement('div')
    usdDisplay.className = 'usd-display'
    usdDisplay.id = 'usd-equivalent'
    usdDisplay.textContent = '≈ $0.00 USD'
    
    const helpText = document.createElement('div')
    helpText.className = 'input-help'
    helpText.textContent = `Min: ${this.options.minAmount || 0.01} SOL • Max: ${this.options.maxAmount || 100} SOL`
    
    inputGroup.appendChild(label)
    inputGroup.appendChild(inputWrapper)
    inputGroup.appendChild(usdDisplay)
    inputGroup.appendChild(helpText)
    
    // Quick amount buttons
    const quickAmounts = document.createElement('div')
    quickAmounts.className = 'quick-amounts'
    
    const quickAmountLabel = document.createElement('div')
    quickAmountLabel.className = 'quick-amount-label'
    quickAmountLabel.textContent = 'Quick amounts:'
    
    const quickButtons = document.createElement('div')
    quickButtons.className = 'quick-buttons'
    
    const amounts = [0.1, 0.5, 1.0, 2.0]
    amounts.forEach(amount => {
      const btn = document.createElement('button')
      btn.className = 'quick-amount-btn'
      btn.textContent = `${amount} SOL`
      btn.onclick = () => {
        this.amountInput.value = amount.toString()
        this.updateUsdDisplay()
        this.validateInput()
      }
      quickButtons.appendChild(btn)
    })
    
    quickAmounts.appendChild(quickAmountLabel)
    quickAmounts.appendChild(quickButtons)
    
    // Buttons
    const buttonGroup = document.createElement('div')
    buttonGroup.className = 'button-group'
    
    this.cancelButton = new Button({
      text: 'Cancel',
      variant: 'secondary',
      onClick: this.handleCancel
    })
    
    this.depositButton = new Button({
      text: 'Deposit SOL',
      variant: 'primary',
      onClick: this.handleDeposit,
      disabled: true
    })
    
    buttonGroup.appendChild(this.cancelButton.getElement())
    buttonGroup.appendChild(this.depositButton.getElement())
    
    formContent.appendChild(balanceSection)
    formContent.appendChild(inputGroup)
    formContent.appendChild(quickAmounts)
    formContent.appendChild(buttonGroup)
    
    this.panel.setContent(formContent)
    this.element.appendChild(this.panel.getElement())
    
    this.addStyles()
  }

  private addStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .deposit-flow-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(11, 15, 20, 0.8);
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 1rem;
      }
      
      .deposit-flow-panel {
        width: 100%;
        max-width: 450px;
        animation: slideIn 0.3s ease-out;
      }
      
      .deposit-flow-content {
        padding: 1.5rem;
      }
      
      .balance-section {
        background: var(--bg-secondary);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        text-align: center;
      }
      
      .balance-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
      }
      
      .balance-display {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }
      
      .input-group {
        margin-bottom: 1.5rem;
      }
      
      .input-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
      }
      
      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
      }
      
      .amount-input {
        flex: 1;
        padding: 0.75rem;
        padding-right: 3rem;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        color: var(--text-primary);
        font-size: 1rem;
        transition: all 0.2s ease;
      }
      
      .amount-input:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
      
      .currency-label {
        position: absolute;
        right: 0.75rem;
        color: var(--text-secondary);
        font-weight: 500;
        pointer-events: none;
      }
      
      .usd-display {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
      }
      
      .input-help {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
      }
      
      .quick-amounts {
        margin-bottom: 1.5rem;
      }
      
      .quick-amount-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
      }
      
      .quick-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }
      
      .quick-amount-btn {
        padding: 0.5rem 0.75rem;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        color: var(--text-primary);
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .quick-amount-btn:hover {
        background: var(--bg-hover);
        border-color: var(--accent-color);
      }
      
      .button-group {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
      }
      
      .button-group button {
        min-width: 100px;
      }
      
      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @media (max-width: 480px) {
        .deposit-flow-content {
          padding: 1rem;
        }
        
        .button-group {
          flex-direction: column;
        }
        
        .button-group button {
          width: 100%;
        }
        
        .quick-buttons {
          justify-content: center;
        }
      }
    `
    document.head.appendChild(style)
  }

  private setupEventListeners(): void {
    // Input validation and USD display update
    this.amountInput.addEventListener('input', () => {
      this.updateUsdDisplay()
      this.validateInput()
    })
    
    // Focus input when shown
    setTimeout(() => {
      this.amountInput.focus()
    }, 100)
  }

  private updateUsdDisplay(): void {
    const amount = parseFloat(this.amountInput.value) || 0
    const usdValue = amount * this.solPrice
    const usdDisplay = document.getElementById('usd-equivalent')
    if (usdDisplay) {
      usdDisplay.textContent = `≈ $${usdValue.toFixed(2)} USD`
    }
  }

  private validateInput(): boolean {
    const amount = parseFloat(this.amountInput.value) || 0
    const minAmount = this.options.minAmount || 0.01
    const maxAmount = this.options.maxAmount || 100
    
    const isValid = amount >= minAmount && amount <= maxAmount && amount <= this.currentBalance
    
    this.depositButton.setDisabled(!isValid)
    
    return isValid
  }

  private updateBalance(): void {
    const walletStore = useWalletStore.getState()
    this.currentBalance = walletStore.balance || 0
    
    if (this.balanceDisplay) {
      this.balanceDisplay.textContent = `${this.currentBalance.toFixed(4)} SOL`
    }
  }

  private handleDeposit = (): void => {
    const amount = parseFloat(this.amountInput.value) || 0
    
    if (this.validateInput()) {
      this.options.onComplete(amount)
    }
  }

  private handleCancel = (): void => {
    this.options.onCancel()
  }

  public show(): void {
    document.body.appendChild(this.element)
    this.updateBalance()
    setTimeout(() => {
      this.amountInput.focus()
    }, 100)
  }

  public hide(): void {
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
  }

  public destroy(): void {
    this.hide()
    this.panel.destroy()
    this.depositButton.destroy()
    this.cancelButton.destroy()
  }

  public getElement(): HTMLElement {
    return this.element
  }
}
