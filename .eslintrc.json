{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "ignorePatterns": ["dist", ".eslintrc.cjs", "node_modules"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "no-console": ["warn", {"allow": ["warn", "error"]}]}}