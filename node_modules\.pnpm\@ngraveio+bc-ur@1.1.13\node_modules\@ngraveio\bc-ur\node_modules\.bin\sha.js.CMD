@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\sha.js@2.4.12\node_modules\sha.js\node_modules;C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\sha.js@2.4.12\node_modules;C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\sha.js@2.4.12\node_modules\sha.js\node_modules;C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\sha.js@2.4.12\node_modules;C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\sha.js@2.4.12\node_modules\sha.js\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\sha.js@2.4.12\node_modules\sha.js\bin.js" %*
)
