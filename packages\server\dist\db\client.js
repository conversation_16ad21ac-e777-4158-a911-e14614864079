import { PrismaClient } from '@prisma/client';
import { env, isDevelopment } from '../config/env.js';
import pino from 'pino';
const logger = pino({ name: 'db' });
// Create Prisma client with appropriate configuration
export const prisma = new PrismaClient({
    log: isDevelopment()
        ? [
            { emit: 'event', level: 'query' },
            { emit: 'event', level: 'error' },
            { emit: 'event', level: 'info' },
            { emit: 'event', level: 'warn' },
        ]
        : [
            { emit: 'event', level: 'error' },
            { emit: 'event', level: 'warn' },
        ],
    datasources: {
        db: {
            url: env.DATABASE_URL,
        },
    },
});
// Log database queries in development
if (isDevelopment()) {
    prisma.$on('query', (e) => {
        logger.debug({
            query: e.query,
            params: e.params,
            duration: e.duration,
        }, 'Database query executed');
    });
}
// Log database errors
prisma.$on('error', (e) => {
    logger.error(e, 'Database error occurred');
});
// Log database warnings
prisma.$on('warn', (e) => {
    logger.warn(e, 'Database warning');
});
// Log database info
prisma.$on('info', (e) => {
    logger.info(e, 'Database info');
});
// Graceful shutdown
process.on('beforeExit', async () => {
    logger.info('Disconnecting from database...');
    await prisma.$disconnect();
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received, disconnecting from database...');
    await prisma.$disconnect();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, disconnecting from database...');
    await prisma.$disconnect();
    process.exit(0);
});
export default prisma;
