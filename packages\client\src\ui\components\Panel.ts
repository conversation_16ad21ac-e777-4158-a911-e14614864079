export interface PanelProps {
  title?: string
  variant?: 'default' | 'glass' | 'solid'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  closable?: boolean
  draggable?: boolean
  resizable?: boolean
  className?: string
  onClose?: () => void
}

export class Panel {
  private element: HTMLDivElement
  private props: PanelProps
  private headerElement?: HTMLDivElement
  private contentElement: HTMLDivElement
  private isDragging: boolean = false
  private dragOffset: { x: number; y: number } = { x: 0, y: 0 }
  
  constructor(props: PanelProps) {
    this.props = props
    this.element = this.createElement()
    this.setupEventListeners()
  }
  
  private createElement(): HTMLDivElement {
    const panel = document.createElement('div')
    panel.className = this.getPanelClasses()
    
    // Header (if title or closable)
    if (this.props.title || this.props.closable) {
      this.headerElement = this.createHeader()
      panel.appendChild(this.headerElement)
    }
    
    // Content area
    this.contentElement = document.createElement('div')
    this.contentElement.className = 'panel__content'
    panel.appendChild(this.contentElement)
    
    // Apply positioning
    this.applyPosition(panel)
    
    this.ensureStyles()
    return panel
  }
  
  private createHeader(): HTMLDivElement {
    const header = document.createElement('div')
    header.className = 'panel__header'
    
    if (this.props.draggable) {
      header.classList.add('panel__header--draggable')
    }
    
    // Title
    if (this.props.title) {
      const title = document.createElement('h3')
      title.className = 'panel__title'
      title.textContent = this.props.title
      header.appendChild(title)
    }
    
    // Close button
    if (this.props.closable) {
      const closeButton = document.createElement('button')
      closeButton.className = 'panel__close'
      closeButton.innerHTML = '×'
      closeButton.setAttribute('aria-label', 'Close panel')
      closeButton.addEventListener('click', () => {
        if (this.props.onClose) {
          this.props.onClose()
        }
      })
      header.appendChild(closeButton)
    }
    
    return header
  }
  
  private getPanelClasses(): string {
    const { variant = 'default', size = 'md', draggable = false, className = '' } = this.props
    
    return `panel panel--${variant} panel--${size} ${draggable ? 'panel--draggable' : ''} ${className}`.trim()
  }
  
  private applyPosition(panel: HTMLDivElement): void {
    const { position = 'center' } = this.props
    
    panel.classList.add(`panel--${position}`)
    
    // Set initial position for draggable panels
    if (this.props.draggable) {
      panel.style.position = 'absolute'
      
      switch (position) {
        case 'top-left':
          panel.style.top = '20px'
          panel.style.left = '20px'
          break
        case 'top-right':
          panel.style.top = '20px'
          panel.style.right = '20px'
          break
        case 'bottom-left':
          panel.style.bottom = '20px'
          panel.style.left = '20px'
          break
        case 'bottom-right':
          panel.style.bottom = '20px'
          panel.style.right = '20px'
          break
        case 'center':
          panel.style.top = '50%'
          panel.style.left = '50%'
          panel.style.transform = 'translate(-50%, -50%)'
          break
      }
    }
  }
  
  private setupEventListeners(): void {
    if (this.props.draggable && this.headerElement) {
      this.headerElement.addEventListener('mousedown', this.handleDragStart)
      document.addEventListener('mousemove', this.handleDragMove)
      document.addEventListener('mouseup', this.handleDragEnd)
    }
  }
  
  private handleDragStart = (e: MouseEvent): void => {
    if (!this.props.draggable) return
    
    this.isDragging = true
    this.element.classList.add('panel--dragging')
    
    const rect = this.element.getBoundingClientRect()
    this.dragOffset = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    }
    
    e.preventDefault()
  }
  
  private handleDragMove = (e: MouseEvent): void => {
    if (!this.isDragging) return
    
    const x = e.clientX - this.dragOffset.x
    const y = e.clientY - this.dragOffset.y
    
    // Constrain to viewport
    const maxX = window.innerWidth - this.element.offsetWidth
    const maxY = window.innerHeight - this.element.offsetHeight
    
    const constrainedX = Math.max(0, Math.min(x, maxX))
    const constrainedY = Math.max(0, Math.min(y, maxY))
    
    this.element.style.left = `${constrainedX}px`
    this.element.style.top = `${constrainedY}px`
    this.element.style.right = 'auto'
    this.element.style.bottom = 'auto'
    this.element.style.transform = 'none'
  }
  
  private handleDragEnd = (): void => {
    if (!this.isDragging) return
    
    this.isDragging = false
    this.element.classList.remove('panel--dragging')
  }
  
  private ensureStyles(): void {
    if (document.getElementById('panel-styles')) return
    
    const style = document.createElement('style')
    style.id = 'panel-styles'
    style.textContent = `
      .panel {
        background: var(--color-background-panel);
        border: 1px solid var(--color-border);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
        z-index: 1000;
      }
      
      .panel--glass {
        background: rgba(15, 23, 42, 0.8);
        backdrop-filter: blur(12px);
        border: 1px solid rgba(148, 163, 184, 0.1);
      }
      
      .panel--solid {
        background: var(--color-background-primary);
        border: 1px solid var(--color-border);
      }
      
      /* Sizes */
      .panel--sm {
        min-width: 200px;
        max-width: 300px;
      }
      
      .panel--md {
        min-width: 300px;
        max-width: 500px;
      }
      
      .panel--lg {
        min-width: 500px;
        max-width: 800px;
      }
      
      .panel--xl {
        min-width: 800px;
        max-width: 1200px;
      }
      
      /* Positioning */
      .panel--top-left {
        position: fixed;
        top: var(--space-lg);
        left: var(--space-lg);
      }
      
      .panel--top-right {
        position: fixed;
        top: var(--space-lg);
        right: var(--space-lg);
      }
      
      .panel--bottom-left {
        position: fixed;
        bottom: var(--space-lg);
        left: var(--space-lg);
      }
      
      .panel--bottom-right {
        position: fixed;
        bottom: var(--space-lg);
        right: var(--space-lg);
      }
      
      .panel--center {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      
      /* Header */
      .panel__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--space-md) var(--space-lg);
        border-bottom: 1px solid var(--color-border);
        background: rgba(148, 163, 184, 0.05);
      }
      
      .panel__header--draggable {
        cursor: move;
        user-select: none;
      }
      
      .panel__title {
        margin: 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
      }
      
      .panel__close {
        background: none;
        border: none;
        color: var(--color-text-secondary);
        font-size: 1.5rem;
        line-height: 1;
        cursor: pointer;
        padding: var(--space-xs);
        border-radius: var(--radius-sm);
        transition: all var(--transition-fast);
      }
      
      .panel__close:hover {
        background: var(--color-background-hover);
        color: var(--color-text-primary);
      }
      
      .panel__close:focus-visible {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
      }
      
      /* Content */
      .panel__content {
        padding: var(--space-lg);
      }
      
      /* Dragging state */
      .panel--dragging {
        z-index: 1001;
        box-shadow: var(--shadow-xl);
      }
      
      .panel--draggable {
        transition: box-shadow var(--transition-fast);
      }
      
      /* Responsive */
      @media (max-width: 768px) {
        .panel {
          max-width: calc(100vw - 2rem);
          max-height: calc(100vh - 2rem);
        }
        
        .panel--center {
          position: fixed;
          top: 1rem;
          left: 1rem;
          right: 1rem;
          bottom: auto;
          transform: none;
          max-height: calc(100vh - 2rem);
          overflow-y: auto;
        }
        
        .panel__content {
          padding: var(--space-md);
        }
      }
      
      /* Animation */
      .panel {
        animation: panel-enter 0.2s ease-out;
      }
      
      @keyframes panel-enter {
        0% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.95);
        }
        100% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      
      .panel--top-left,
      .panel--top-right,
      .panel--bottom-left,
      .panel--bottom-right {
        animation: panel-slide-in 0.2s ease-out;
      }
      
      @keyframes panel-slide-in {
        0% {
          opacity: 0;
          transform: translateY(-10px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `
    
    document.head.appendChild(style)
  }
  
  /**
   * Add content to the panel
   */
  setContent(content: HTMLElement | string): void {
    if (typeof content === 'string') {
      this.contentElement.innerHTML = content
    } else {
      this.contentElement.innerHTML = ''
      this.contentElement.appendChild(content)
    }
  }
  
  /**
   * Update panel properties
   */
  update(props: Partial<PanelProps>): void {
    this.props = { ...this.props, ...props }
    
    // Recreate the panel with new props
    const newElement = this.createElement()
    const content = this.contentElement.innerHTML
    
    this.element.parentNode?.replaceChild(newElement, this.element)
    this.element = newElement
    this.contentElement = this.element.querySelector('.panel__content') as HTMLDivElement
    this.headerElement = this.element.querySelector('.panel__header') as HTMLDivElement | undefined
    
    this.setContent(content)
    this.setupEventListeners()
  }
  
  /**
   * Show the panel
   */
  show(): void {
    this.element.style.display = 'block'
  }
  
  /**
   * Hide the panel
   */
  hide(): void {
    this.element.style.display = 'none'
  }
  
  /**
   * Get the DOM element
   */
  getElement(): HTMLDivElement {
    return this.element
  }
  
  /**
   * Get the content element
   */
  getContentElement(): HTMLDivElement {
    return this.contentElement
  }
  
  /**
   * Destroy the panel
   */
  destroy(): void {
    if (this.props.draggable) {
      document.removeEventListener('mousemove', this.handleDragMove)
      document.removeEventListener('mouseup', this.handleDragEnd)
    }
    this.element.remove()
  }
}
