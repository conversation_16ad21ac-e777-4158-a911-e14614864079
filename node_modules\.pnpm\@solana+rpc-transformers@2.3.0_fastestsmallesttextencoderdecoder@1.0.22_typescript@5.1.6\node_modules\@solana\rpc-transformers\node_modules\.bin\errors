#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/bin/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/bin/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/@solana+errors@2.3.0_typescript@5.1.6/node_modules:/mnt/c/Users/<USER>/Documents/Code/agario gamble/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/bin/cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../../@solana+errors@2.3.0_typescript@5.1.6/node_modules/@solana/errors/bin/cli.mjs" "$@"
fi
