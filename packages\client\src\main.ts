import './styles/global.css'
import './styles/tokens.css'

// Remove loading screen
const loadingElement = document.getElementById('loading')
if (loadingElement) {
  loadingElement.remove()
}

// Initialize the application
async function initApp() {
  try {
    console.log('🚀 Initializing Agar.io Solana Clone...')

    // TODO: Initialize game engine, wallet connections, etc.
    const root = document.getElementById('root')
    if (root) {
      root.innerHTML = `
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100vh;
          flex-direction: column;
          text-align: center;
          padding: 2rem;
        ">
          <h1 style="
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #38BDF8, #A78BFA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          ">
            Agar.io Solana
          </h1>
          <p style="
            font-size: 1.25rem;
            color: #94A3B8;
            margin-bottom: 2rem;
            max-width: 600px;
          ">
            The classic cell-eating game with Solana wagering. Eat, grow, and cash out your winnings!
          </p>
          <div style="
            padding: 1rem 2rem;
            background: #0E141B;
            border: 1px solid #CBD5E1;
            border-radius: 8px;
            color: #38BDF8;
            font-weight: 500;
          ">
            🚧 Phase 1: Project Setup Complete - Game Engine Coming Soon!
          </div>
        </div>
      `
    }

    console.log('✅ Application initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
  }
}

// Start the application
initApp()
