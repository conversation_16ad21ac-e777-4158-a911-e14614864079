import './styles/global.css'
import './styles/tokens.css'
import { GameEngine } from './core/engine.js'
import { InputManager } from './core/input.js'
import { SocketClient } from './net/socket-client.js'
import { GameHUD } from './ui/components/GameHUD.js'
import { WalletConnect } from './ui/components/WalletConnect.js'
import { LobbyBrowser } from './ui/components/LobbyBrowser.js'
import { UsernameForm } from './ui/components/UsernameForm'
import { DepositFlow } from './ui/components/DepositFlow'
import { Onboarding } from './ui/components/Onboarding'
import { useGameStore } from './store/game-store.js'
import { useWalletStore } from './store/wallet-store.js'

// Remove loading screen
const loadingElement = document.getElementById('loading')
if (loadingElement) {
  loadingElement.remove()
}

class GameApplication {
  private canvas: HTMLCanvasElement
  private engine: GameEngine
  private inputManager: InputManager
  private socketClient: SocketClient
  private gameHUD: GameHUD
  private walletConnect: WalletConnect | null = null
  private lobbyBrowser: LobbyBrowser | null = null
  private usernameForm: UsernameForm | null = null
  private depositFlow: DepositFlow | null = null
  private onboarding: Onboarding | null = null

  private gameState: 'menu' | 'connecting' | 'lobby' | 'playing' = 'menu'

  constructor() {
    try {
      console.log('🎮 Initializing GameApplication...')

      this.setupDOM()
      this.canvas = document.getElementById('game-canvas') as HTMLCanvasElement

      if (!this.canvas) {
        throw new Error('Canvas element not found')
      }

      // Initialize core systems
      console.log('🔧 Initializing core systems...')
      this.engine = new GameEngine(this.canvas)
      this.inputManager = new InputManager(this.canvas)
      this.socketClient = new SocketClient()

      // Initialize UI
      console.log('🎨 Initializing UI components...')
      this.gameHUD = new GameHUD({
        onCashOut: this.handleCashOut,
        onSettings: this.handleSettings
      })

      // Setup connections
      this.inputManager.setCamera(this.engine['camera'])

      // Setup event listeners
      console.log('📡 Setting up event listeners...')
      this.setupEventListeners()

      // Start the application
      console.log('🚀 Starting application...')
      this.start()

      console.log('✅ GameApplication initialized successfully!')
    } catch (error) {
      console.error('❌ Failed to initialize GameApplication:', error)
      // Show error message to user
      const root = document.getElementById('root')
      if (root) {
        root.innerHTML = `
          <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            text-align: center;
            padding: 2rem;
            color: #ef4444;
          ">
            <h1>❌ Application Error</h1>
            <p>Failed to initialize the game application.</p>
            <p><small>${error instanceof Error ? error.message : 'Unknown error'}</small></p>
            <button onclick="location.reload()" style="
              margin-top: 1rem;
              padding: 0.5rem 1rem;
              background: #3b82f6;
              color: white;
              border: none;
              border-radius: 0.375rem;
              cursor: pointer;
            ">Reload Page</button>
          </div>
        `
      }
    }
  }

  private setupDOM(): void {
    const app = document.querySelector<HTMLDivElement>('#root')!
    app.innerHTML = `
      <div class="game-container">
        <canvas id="game-canvas"></canvas>
        <div id="ui-container"></div>
      </div>

      <div class="game-menu" id="game-menu">
        <div class="game-menu-content">
          <h1 class="game-title">Agar.io Gamble</h1>
          <p class="game-subtitle">Compete for SOL in the ultimate cell-eating battle</p>

          <div class="menu-actions">
            <button id="connect-wallet-btn" class="btn btn--primary btn--lg">
              Connect Wallet
            </button>
            <button id="play-guest-btn" class="btn btn--secondary btn--lg">
              Play as Guest
            </button>
            <button id="set-username-btn" class="btn btn--tertiary btn--sm">
              Set Username
            </button>
            <button id="show-tutorial-btn" class="btn btn--tertiary btn--sm">
              Show Tutorial
            </button>
          </div>

          <div class="game-features">
            <div class="feature">
              <div class="feature-icon">💰</div>
              <div class="feature-text">
                <h3>Wager SOL</h3>
                <p>Bet Solana tokens and win big</p>
              </div>
            </div>
            <div class="feature">
              <div class="feature-icon">⚡</div>
              <div class="feature-text">
                <h3>Real-time Action</h3>
                <p>Fast-paced multiplayer gameplay</p>
              </div>
            </div>
            <div class="feature">
              <div class="feature-icon">🏆</div>
              <div class="feature-text">
                <h3>Compete & Win</h3>
                <p>Climb the leaderboard</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `

    this.ensureStyles()
  }

  private setupEventListeners(): void {
    // Menu buttons
    document.getElementById('connect-wallet-btn')?.addEventListener('click', this.showWalletConnect)
    document.getElementById('play-guest-btn')?.addEventListener('click', this.playAsGuest)
    document.getElementById('set-username-btn')?.addEventListener('click', this.showUsernameForm)
    document.getElementById('show-tutorial-btn')?.addEventListener('click', this.showOnboarding)

    // Socket events
    this.socketClient.on('connected', this.handleSocketConnected)
    this.socketClient.on('disconnected', this.handleSocketDisconnected)
    this.socketClient.on('game_snapshot', this.handleGameSnapshot)

    // Store subscriptions
    useWalletStore.subscribe(
      (state) => state.connected,
      (connected) => {
        if (connected) {
          this.onWalletConnected()
        }
      }
    )

    // Window events
    window.addEventListener('beforeunload', this.cleanup)
    window.addEventListener('resize', this.handleResize)
  }

  private start(): void {
    console.log('🎮 Starting Agar.io Gamble...')

    // Check if user needs onboarding
    const hasCompletedOnboarding = localStorage.getItem('onboarding_completed')

    if (!hasCompletedOnboarding) {
      this.showOnboarding()
    } else {
      // Show menu initially
      this.showMenu()
    }

    // Setup canvas
    this.handleResize()
  }

  private showMenu(): void {
    this.gameState = 'menu'
    const menu = document.getElementById('game-menu')
    const canvas = document.getElementById('game-canvas')

    if (menu) menu.style.display = 'flex'
    if (canvas) canvas.style.display = 'none'

    this.gameHUD.hide()
  }

  private hideMenu(): void {
    const menu = document.getElementById('game-menu')
    const canvas = document.getElementById('game-canvas')

    if (menu) menu.style.display = 'none'
    if (canvas) canvas.style.display = 'block'
  }

  private showWalletConnect = (): void => {
    if (this.walletConnect) {
      this.walletConnect.destroy()
    }

    this.walletConnect = new WalletConnect({
      onConnect: this.handleWalletConnect,
      onDisconnect: this.handleWalletDisconnect,
      onClose: this.handleWalletConnectClose
    })

    this.walletConnect.show()
  }

  private playAsGuest = (): void => {
    // For now, just show lobby browser
    this.showLobbyBrowser()
  }

  private showLobbyBrowser(): void {
    if (this.lobbyBrowser) {
      this.lobbyBrowser.destroy()
    }

    this.lobbyBrowser = new LobbyBrowser({
      onJoinLobby: this.handleJoinLobby,
      onCreateLobby: this.handleCreateLobby,
      onRefresh: this.handleRefreshLobbies,
      onClose: this.handleLobbyBrowserClose
    })

    this.lobbyBrowser.show()
  }

  private handleWalletConnect = (walletName: string): void => {
    console.log('Wallet connected:', walletName)
  }

  private handleWalletDisconnect = (): void => {
    console.log('Wallet disconnected')
  }

  private handleWalletConnectClose = (): void => {
    if (this.walletConnect) {
      this.walletConnect.hide()
    }
  }

  private showUsernameForm = (): void => {
    if (this.usernameForm) {
      this.usernameForm.destroy()
    }

    this.usernameForm = new UsernameForm({
      onSubmit: this.handleUsernameSubmit,
      onCancel: this.handleUsernameCancel,
      initialUsername: localStorage.getItem('username') || ''
    })

    this.usernameForm.show()
  }

  private handleUsernameSubmit = (username: string): void => {
    console.log('Username submitted:', username)
    localStorage.setItem('username', username)

    if (this.usernameForm) {
      this.usernameForm.hide()
    }

    // Continue to lobby or deposit flow
    this.showLobbyBrowser()
  }

  private handleUsernameCancel = (): void => {
    if (this.usernameForm) {
      this.usernameForm.hide()
    }
  }

  private showDepositFlow = (): void => {
    if (this.depositFlow) {
      this.depositFlow.destroy()
    }

    this.depositFlow = new DepositFlow({
      onComplete: this.handleDepositComplete,
      onCancel: this.handleDepositCancel,
      minAmount: 0.01,
      maxAmount: 10
    })

    this.depositFlow.show()
  }

  private handleDepositComplete = (amount: number): void => {
    console.log('Deposit completed:', amount, 'SOL')

    if (this.depositFlow) {
      this.depositFlow.hide()
    }

    // Continue to lobby
    this.showLobbyBrowser()
  }

  private handleDepositCancel = (): void => {
    if (this.depositFlow) {
      this.depositFlow.hide()
    }
  }

  private showOnboarding = (): void => {
    if (this.onboarding) {
      this.onboarding.destroy()
    }

    this.onboarding = new Onboarding({
      onComplete: this.handleOnboardingComplete,
      onSkip: this.handleOnboardingSkip
    })

    this.onboarding.show()
  }

  private handleOnboardingComplete = (): void => {
    console.log('Onboarding completed')
    localStorage.setItem('onboarding_completed', 'true')

    if (this.onboarding) {
      this.onboarding.hide()
    }

    // Show main menu
    this.showMenu()
  }

  private handleOnboardingSkip = (): void => {
    console.log('Onboarding skipped')
    localStorage.setItem('onboarding_completed', 'true')

    if (this.onboarding) {
      this.onboarding.hide()
    }

    // Show main menu
    this.showMenu()
  }

  private onWalletConnected(): void {
    // Close wallet connect modal and show lobby browser
    if (this.walletConnect) {
      this.walletConnect.hide()
    }
    this.showLobbyBrowser()
  }

  private handleJoinLobby = async (lobbyId: string): Promise<void> => {
    console.log('Joining lobby:', lobbyId)

    try {
      this.gameState = 'connecting'

      // Hide lobby browser
      if (this.lobbyBrowser) {
        this.lobbyBrowser.hide()
      }

      // Connect to server
      await this.socketClient.connect()

      // Join the lobby
      this.socketClient.joinGame({
        lobbyId,
        username: 'Player', // TODO: Get from user input
        wagerAmount: 0.01 // TODO: Get from lobby selection
      })

    } catch (error) {
      console.error('Failed to join lobby:', error)
      // Show error and return to lobby browser
      this.showLobbyBrowser()
    }
  }

  private handleCreateLobby = (): void => {
    console.log('Create lobby requested')
    // TODO: Implement lobby creation
  }

  private handleRefreshLobbies = (): void => {
    console.log('Refreshing lobbies')
    // TODO: Fetch fresh lobby data
  }

  private handleLobbyBrowserClose = (): void => {
    if (this.lobbyBrowser) {
      this.lobbyBrowser.hide()
    }
    this.showMenu()
  }

  private handleSocketConnected = (): void => {
    console.log('Connected to game server')
    this.gameState = 'lobby'
  }

  private handleSocketDisconnected = (reason: string): void => {
    console.log('Disconnected from server:', reason)
    this.gameState = 'menu'
    this.showMenu()
  }

  private handleGameSnapshot = (snapshot: any): void => {
    // Game has started
    if (this.gameState !== 'playing') {
      this.startGame()
    }
  }

  private startGame(): void {
    console.log('🎮 Starting game...')

    this.gameState = 'playing'
    this.hideMenu()

    // Show game UI
    this.gameHUD.show()
    document.getElementById('ui-container')?.appendChild(this.gameHUD.getElement())

    // Start game engine
    this.engine.start()
  }

  private handleCashOut = (): void => {
    console.log('Cash out requested')
    // TODO: Implement cash out logic
  }

  private handleSettings = (): void => {
    console.log('Settings requested')
    // TODO: Implement settings panel
  }

  private handleResize = (): void => {
    const canvas = this.canvas
    const container = canvas.parentElement

    if (container) {
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight
      canvas.style.width = container.clientWidth + 'px'
      canvas.style.height = container.clientHeight + 'px'
    }

    // Notify engine of resize
    this.engine['renderer'].resize()
  }

  private cleanup = (): void => {
    console.log('🧹 Cleaning up...')

    this.engine.destroy()
    this.inputManager.destroy()
    this.socketClient.disconnect()
    this.gameHUD.destroy()

    if (this.walletConnect) {
      this.walletConnect.destroy()
    }

    if (this.lobbyBrowser) {
      this.lobbyBrowser.destroy()
    }
  }

  private ensureStyles(): void {
    if (document.getElementById('app-styles')) return

    const style = document.createElement('style')
    style.id = 'app-styles'
    style.textContent = `
      .game-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--color-background-primary);
      }

      #game-canvas {
        display: block;
        width: 100%;
        height: 100%;
        background: var(--color-background-primary);
      }

      #ui-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 100;
      }

      .game-menu {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }

      .game-menu-content {
        text-align: center;
        max-width: 600px;
        padding: var(--space-xl);
      }

      .game-title {
        font-size: 4rem;
        font-weight: var(--font-weight-bold);
        color: var(--color-primary);
        margin-bottom: var(--space-md);
        text-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
      }

      .game-subtitle {
        font-size: 1.25rem;
        color: var(--color-text-secondary);
        margin-bottom: var(--space-xl);
        line-height: 1.6;
      }

      .menu-actions {
        display: flex;
        flex-direction: column;
        gap: var(--space-md);
        margin-bottom: var(--space-xl);
      }

      .menu-actions .btn {
        min-width: 200px;
        justify-content: center;
      }

      .menu-actions .btn--sm {
        min-width: 150px;
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
      }

      .btn--tertiary {
        background: transparent;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
      }

      .btn--tertiary:hover {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border-color: var(--accent-color);
      }

      .game-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: var(--space-lg);
        margin-top: var(--space-xl);
      }

      .feature {
        display: flex;
        align-items: center;
        gap: var(--space-md);
        padding: var(--space-lg);
        background: rgba(148, 163, 184, 0.05);
        border-radius: var(--radius-lg);
        border: 1px solid rgba(148, 163, 184, 0.1);
      }

      .feature-icon {
        font-size: 2rem;
        flex-shrink: 0;
      }

      .feature-text h3 {
        margin: 0 0 var(--space-xs) 0;
        color: var(--color-text-primary);
        font-size: 1rem;
        font-weight: var(--font-weight-semibold);
      }

      .feature-text p {
        margin: 0;
        color: var(--color-text-secondary);
        font-size: 0.875rem;
        line-height: 1.4;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .game-title {
          font-size: 2.5rem;
        }

        .game-subtitle {
          font-size: 1rem;
        }

        .game-features {
          grid-template-columns: 1fr;
        }

        .feature {
          flex-direction: column;
          text-align: center;
        }
      }
    `

    document.head.appendChild(style)
  }
}

// Start the application
new GameApplication()
