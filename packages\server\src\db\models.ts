import { Prisma } from '@prisma/client'
import prisma from './client.js'

// Type definitions for database models
export type User = Prisma.UserGetPayload<{}>
export type GameSession = Prisma.GameSessionGetPayload<{}>
export type Transaction = Prisma.TransactionGetPayload<{}>
export type AuditLog = Prisma.AuditLogGetPayload<{}>
export type SystemConfig = Prisma.SystemConfigGetPayload<{}>
export type LobbyState = Prisma.LobbyStateGetPayload<{}>
export type PriceHistory = Prisma.PriceHistoryGetPayload<{}>

// Extended types with relations
export type UserWithSessions = Prisma.UserGetPayload<{
  include: {
    sessions: true
    transactions: true
  }
}>

export type GameSessionWithUser = Prisma.GameSessionGetPayload<{
  include: {
    user: true
    transactions: true
  }
}>

// User repository functions
export const UserRepository = {
  async findByWallet(wallet: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { wallet },
    })
  },

  async createUser(data: {
    wallet: string
    username?: string
    ipAddress?: string
    countryCode?: string
  }): Promise<User> {
    return prisma.user.create({
      data: {
        wallet: data.wallet,
        username: data.username,
        ipAddress: data.ipAddress,
        countryCode: data.countryCode,
      },
    })
  },

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    return prisma.user.update({
      where: { id },
      data,
    })
  },

  async getUserWithSessions(wallet: string): Promise<UserWithSessions | null> {
    return prisma.user.findUnique({
      where: { wallet },
      include: {
        sessions: {
          where: { status: 'active' },
          orderBy: { startedAt: 'desc' },
        },
        transactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    })
  },
}

// Game session repository functions
export const GameSessionRepository = {
  async createSession(data: {
    userId: string
    lobbyTier: number
    sessionToken: string
    wagerAmount: string
    initialMass: number
  }): Promise<GameSession> {
    return prisma.gameSession.create({
      data: {
        userId: data.userId,
        lobbyTier: data.lobbyTier,
        sessionToken: data.sessionToken,
        wagerAmount: data.wagerAmount,
        initialMass: data.initialMass,
        currentMass: data.initialMass,
        currentValue: data.wagerAmount,
      },
    })
  },

  async findBySessionToken(sessionToken: string): Promise<GameSessionWithUser | null> {
    return prisma.gameSession.findUnique({
      where: { sessionToken },
      include: {
        user: true,
        transactions: true,
      },
    })
  },

  async updateSession(id: string, data: Partial<GameSession>): Promise<GameSession> {
    return prisma.gameSession.update({
      where: { id },
      data,
    })
  },

  async endSession(id: string, data: {
    status: string
    endedAt: Date
    eliminatedBy?: string
    cashoutAmount?: string
  }): Promise<GameSession> {
    return prisma.gameSession.update({
      where: { id },
      data: {
        status: data.status,
        endedAt: data.endedAt,
        eliminatedBy: data.eliminatedBy,
        cashoutAmount: data.cashoutAmount,
      },
    })
  },

  async getActiveSessions(lobbyTier?: number): Promise<GameSession[]> {
    return prisma.gameSession.findMany({
      where: {
        status: 'active',
        ...(lobbyTier && { lobbyTier }),
      },
      include: {
        user: {
          select: {
            id: true,
            wallet: true,
            username: true,
          },
        },
      },
    })
  },
}

// Transaction repository functions
export const TransactionRepository = {
  async createTransaction(data: {
    userId: string
    sessionId?: string
    type: string
    amount: string
    feeAmount?: string
    solPriceUsd?: string
  }): Promise<Transaction> {
    return prisma.transaction.create({
      data: {
        userId: data.userId,
        sessionId: data.sessionId,
        type: data.type,
        amount: data.amount,
        feeAmount: data.feeAmount || '0',
        solPriceUsd: data.solPriceUsd,
      },
    })
  },

  async updateTransaction(id: string, data: {
    signature?: string
    blockHeight?: bigint
    confirmations?: number
    status?: string
  }): Promise<Transaction> {
    return prisma.transaction.update({
      where: { id },
      data,
    })
  },

  async findBySignature(signature: string): Promise<Transaction | null> {
    return prisma.transaction.findUnique({
      where: { signature },
    })
  },

  async getUserTransactions(userId: string, limit = 50): Promise<Transaction[]> {
    return prisma.transaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })
  },
}

// Audit log repository functions
export const AuditLogRepository = {
  async createLog(data: {
    userId?: string
    sessionId?: string
    eventType: string
    eventData: any
    severity?: string
    ipAddress?: string
    userAgent?: string
  }): Promise<AuditLog> {
    return prisma.auditLog.create({
      data: {
        userId: data.userId,
        sessionId: data.sessionId,
        eventType: data.eventType,
        eventData: data.eventData,
        severity: data.severity || 'info',
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    })
  },

  async getLogsByUser(userId: string, limit = 100): Promise<AuditLog[]> {
    return prisma.auditLog.findMany({
      where: { userId },
      orderBy: { timestamp: 'desc' },
      take: limit,
    })
  },

  async getLogsByType(eventType: string, limit = 100): Promise<AuditLog[]> {
    return prisma.auditLog.findMany({
      where: { eventType },
      orderBy: { timestamp: 'desc' },
      take: limit,
    })
  },
}

// System configuration repository functions
export const SystemConfigRepository = {
  async getValue(key: string): Promise<string | null> {
    const config = await prisma.systemConfig.findUnique({
      where: { key },
    })
    return config?.value || null
  },

  async setValue(key: string, value: string, type = 'string', updatedBy?: string): Promise<SystemConfig> {
    return prisma.systemConfig.upsert({
      where: { key },
      update: {
        value,
        type,
        updatedBy,
      },
      create: {
        key,
        value,
        type,
        updatedBy,
      },
    })
  },

  async getAllConfig(): Promise<SystemConfig[]> {
    return prisma.systemConfig.findMany({
      orderBy: { key: 'asc' },
    })
  },
}

// Lobby state repository functions
export const LobbyStateRepository = {
  async getLobbyState(lobbyTier: number): Promise<LobbyState | null> {
    return prisma.lobbyState.findUnique({
      where: { id: lobbyTier },
    })
  },

  async updateLobbyState(lobbyTier: number, data: Partial<LobbyState>): Promise<LobbyState> {
    return prisma.lobbyState.upsert({
      where: { id: lobbyTier },
      update: {
        ...data,
        lastActivity: new Date(),
      },
      create: {
        id: lobbyTier,
        ...data,
        lastActivity: new Date(),
      },
    })
  },

  async getAllLobbyStates(): Promise<LobbyState[]> {
    return prisma.lobbyState.findMany({
      orderBy: { id: 'asc' },
    })
  },
}

// Price history repository functions
export const PriceHistoryRepository = {
  async recordPrice(data: {
    solPriceUsd: string
    confidence: string
    publishTime: Date
    oracleAccount: string
    slot: bigint
  }): Promise<PriceHistory> {
    return prisma.priceHistory.create({
      data,
    })
  },

  async getLatestPrice(): Promise<PriceHistory | null> {
    return prisma.priceHistory.findFirst({
      orderBy: { publishTime: 'desc' },
    })
  },

  async getPriceHistory(hours = 24): Promise<PriceHistory[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000)
    return prisma.priceHistory.findMany({
      where: {
        publishTime: {
          gte: since,
        },
      },
      orderBy: { publishTime: 'desc' },
    })
  },
}
