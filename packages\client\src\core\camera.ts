import { Vector2 } from '../types/game.js'

export interface CameraConfig {
  minZoom: number
  maxZoom: number
  zoomSpeed: number
  followSpeed: number
  smoothing: number
}

export class Camera {
  public position: Vector2 = { x: 0, y: 0 }
  public zoom: number = 1
  public targetZoom: number = 1
  public targetPosition: Vector2 = { x: 0, y: 0 }
  
  private config: CameraConfig
  private viewportWidth: number = 0
  private viewportHeight: number = 0
  
  constructor(config: Partial<CameraConfig> = {}) {
    this.config = {
      minZoom: 0.1,
      maxZoom: 3.0,
      zoomSpeed: 5.0,
      followSpeed: 8.0,
      smoothing: 0.1,
      ...config
    }
  }
  
  /**
   * Update camera position and zoom with smooth interpolation
   */
  update(deltaTime: number): void {
    // Smooth zoom interpolation
    const zoomDiff = this.targetZoom - this.zoom
    if (Math.abs(zoomDiff) > 0.001) {
      this.zoom += zoomDiff * this.config.zoomSpeed * deltaTime
    } else {
      this.zoom = this.targetZoom
    }
    
    // Smooth position interpolation
    const positionDiff = {
      x: this.targetPosition.x - this.position.x,
      y: this.targetPosition.y - this.position.y
    }
    
    const distance = Math.sqrt(positionDiff.x * positionDiff.x + positionDiff.y * positionDiff.y)
    if (distance > 0.1) {
      this.position.x += positionDiff.x * this.config.followSpeed * deltaTime
      this.position.y += positionDiff.y * this.config.followSpeed * deltaTime
    } else {
      this.position.x = this.targetPosition.x
      this.position.y = this.targetPosition.y
    }
  }
  
  /**
   * Set camera target position
   */
  setTarget(x: number, y: number): void {
    this.targetPosition.x = x
    this.targetPosition.y = y
  }
  
  /**
   * Set camera zoom level
   */
  setZoom(zoom: number): void {
    this.targetZoom = Math.max(this.config.minZoom, Math.min(this.config.maxZoom, zoom))
  }
  
  /**
   * Adjust zoom by a factor
   */
  adjustZoom(factor: number): void {
    this.setZoom(this.targetZoom * factor)
  }
  
  /**
   * Set viewport dimensions
   */
  setViewport(width: number, height: number): void {
    this.viewportWidth = width
    this.viewportHeight = height
  }
  
  /**
   * Convert world coordinates to screen coordinates
   */
  worldToScreen(worldPos: Vector2): Vector2 {
    return {
      x: (worldPos.x - this.position.x) * this.zoom + this.viewportWidth / 2,
      y: (worldPos.y - this.position.y) * this.zoom + this.viewportHeight / 2
    }
  }
  
  /**
   * Convert screen coordinates to world coordinates
   */
  screenToWorld(screenPos: Vector2): Vector2 {
    return {
      x: (screenPos.x - this.viewportWidth / 2) / this.zoom + this.position.x,
      y: (screenPos.y - this.viewportHeight / 2) / this.zoom + this.position.y
    }
  }
  
  /**
   * Get the visible world bounds
   */
  getVisibleBounds(): { 
    left: number
    right: number
    top: number
    bottom: number
    width: number
    height: number
  } {
    const halfWidth = (this.viewportWidth / 2) / this.zoom
    const halfHeight = (this.viewportHeight / 2) / this.zoom
    
    return {
      left: this.position.x - halfWidth,
      right: this.position.x + halfWidth,
      top: this.position.y - halfHeight,
      bottom: this.position.y + halfHeight,
      width: halfWidth * 2,
      height: halfHeight * 2
    }
  }
  
  /**
   * Check if a point is visible in the camera view
   */
  isPointVisible(point: Vector2, margin: number = 0): boolean {
    const bounds = this.getVisibleBounds()
    return (
      point.x >= bounds.left - margin &&
      point.x <= bounds.right + margin &&
      point.y >= bounds.top - margin &&
      point.y <= bounds.bottom + margin
    )
  }
  
  /**
   * Check if a circle is visible in the camera view
   */
  isCircleVisible(center: Vector2, radius: number): boolean {
    return this.isPointVisible(center, radius)
  }
  
  /**
   * Calculate optimal zoom based on player mass
   */
  calculateOptimalZoom(playerMass: number): number {
    // Zoom out as player gets bigger
    const baseZoom = 1.0
    const massScale = Math.sqrt(playerMass / 100) // Normalize to base mass of 100
    const optimalZoom = baseZoom / Math.max(1, massScale * 0.5)
    
    return Math.max(this.config.minZoom, Math.min(this.config.maxZoom, optimalZoom))
  }
  
  /**
   * Follow a player with automatic zoom adjustment
   */
  followPlayer(playerPosition: Vector2, playerMass: number): void {
    this.setTarget(playerPosition.x, playerPosition.y)
    this.setZoom(this.calculateOptimalZoom(playerMass))
  }
  
  /**
   * Apply camera transform to canvas context
   */
  applyTransform(ctx: CanvasRenderingContext2D): void {
    ctx.save()
    
    // Translate to center of viewport
    ctx.translate(this.viewportWidth / 2, this.viewportHeight / 2)
    
    // Apply zoom
    ctx.scale(this.zoom, this.zoom)
    
    // Translate to camera position (negative because we're moving the world)
    ctx.translate(-this.position.x, -this.position.y)
  }
  
  /**
   * Restore canvas context transform
   */
  restoreTransform(ctx: CanvasRenderingContext2D): void {
    ctx.restore()
  }
  
  /**
   * Get camera state for debugging
   */
  getState(): {
    position: Vector2
    zoom: number
    targetPosition: Vector2
    targetZoom: number
    visibleBounds: ReturnType<Camera['getVisibleBounds']>
  } {
    return {
      position: { ...this.position },
      zoom: this.zoom,
      targetPosition: { ...this.targetPosition },
      targetZoom: this.targetZoom,
      visibleBounds: this.getVisibleBounds()
    }
  }
}
