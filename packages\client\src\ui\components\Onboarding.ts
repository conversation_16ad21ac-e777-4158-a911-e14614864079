import { Button } from './Button'
import { Panel } from './Panel'

export interface OnboardingOptions {
  onComplete: () => void
  onSkip: () => void
}

interface OnboardingStep {
  title: string
  content: string
  icon: string
}

export class Onboarding {
  private element: HTMLElement
  private panel: Panel
  private currentStep: number = 0
  private nextButton: Button
  private skipButton: Button
  private prevButton: Button
  private options: OnboardingOptions
  private stepIndicator: HTMLElement
  private stepContent: HTMLElement

  private steps: OnboardingStep[] = [
    {
      title: 'Welcome to Agar.io Solana!',
      content: 'Experience the classic cell-eating game with real SOL wagering. Grow your cell, consume others, and cash out your winnings!',
      icon: '🎮'
    },
    {
      title: 'How to Play',
      content: 'Move your mouse to control your cell. Eat smaller cells and pellets to grow. Avoid larger cells that can consume you. Use spacebar to split and W to eject mass.',
      icon: '🕹️'
    },
    {
      title: 'SOL Wagering',
      content: 'Connect your Solana wallet to wager SOL in competitive matches. Your winnings are based on your final mass and survival time.',
      icon: '💰'
    },
    {
      title: 'Cash Out Anytime',
      content: 'Hold Q for 5 seconds to cash out your current winnings and leave the game. Your SOL will be transferred back to your wallet.',
      icon: '💸'
    },
    {
      title: 'Ready to Play!',
      content: 'You\'re all set! Connect your wallet to start wagering, or play as a guest to practice. Good luck and have fun!',
      icon: '🚀'
    }
  ]

  constructor(options: OnboardingOptions) {
    this.options = options
    this.createElement()
    this.setupEventListeners()
    this.updateStep()
  }

  private createElement(): void {
    this.element = document.createElement('div')
    this.element.className = 'onboarding-overlay'
    
    this.panel = new Panel({
      title: 'Getting Started',
      className: 'onboarding-panel'
    })

    // Create onboarding content
    const onboardingContent = document.createElement('div')
    onboardingContent.className = 'onboarding-content'
    
    // Step indicator
    this.stepIndicator = document.createElement('div')
    this.stepIndicator.className = 'step-indicator'
    
    // Step content
    this.stepContent = document.createElement('div')
    this.stepContent.className = 'step-content'
    
    // Navigation buttons
    const buttonGroup = document.createElement('div')
    buttonGroup.className = 'button-group'
    
    this.skipButton = new Button({
      text: 'Skip Tutorial',
      variant: 'secondary',
      onClick: this.handleSkip
    })
    
    const navButtons = document.createElement('div')
    navButtons.className = 'nav-buttons'
    
    this.prevButton = new Button({
      text: 'Previous',
      variant: 'secondary',
      onClick: this.handlePrevious,
      disabled: true
    })
    
    this.nextButton = new Button({
      text: 'Next',
      variant: 'primary',
      onClick: this.handleNext
    })
    
    navButtons.appendChild(this.prevButton.getElement())
    navButtons.appendChild(this.nextButton.getElement())
    
    buttonGroup.appendChild(this.skipButton.getElement())
    buttonGroup.appendChild(navButtons)
    
    onboardingContent.appendChild(this.stepIndicator)
    onboardingContent.appendChild(this.stepContent)
    onboardingContent.appendChild(buttonGroup)
    
    this.panel.setContent(onboardingContent)
    this.element.appendChild(this.panel.getElement())
    
    this.addStyles()
  }

  private addStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .onboarding-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(11, 15, 20, 0.9);
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 1rem;
      }
      
      .onboarding-panel {
        width: 100%;
        max-width: 500px;
        animation: slideIn 0.3s ease-out;
      }
      
      .onboarding-content {
        padding: 1.5rem;
      }
      
      .step-indicator {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
      }
      
      .step-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: var(--border-color);
        transition: all 0.3s ease;
      }
      
      .step-dot.active {
        background: var(--accent-color);
        transform: scale(1.2);
      }
      
      .step-dot.completed {
        background: #10b981;
      }
      
      .step-content {
        text-align: center;
        margin-bottom: 2rem;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      
      .step-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }
      
      .step-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
      }
      
      .step-text {
        font-size: 1rem;
        color: var(--text-secondary);
        line-height: 1.6;
        max-width: 400px;
        margin: 0 auto;
      }
      
      .button-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .nav-buttons {
        display: flex;
        gap: 0.75rem;
      }
      
      .nav-buttons button {
        min-width: 80px;
      }
      
      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      .step-content-enter {
        animation: fadeIn 0.3s ease-out;
      }
      
      @media (max-width: 480px) {
        .onboarding-content {
          padding: 1rem;
        }
        
        .button-group {
          flex-direction: column;
          gap: 1rem;
        }
        
        .nav-buttons {
          width: 100%;
          justify-content: space-between;
        }
        
        .nav-buttons button {
          flex: 1;
        }
        
        .step-content {
          min-height: 150px;
        }
        
        .step-icon {
          font-size: 2.5rem;
        }
        
        .step-title {
          font-size: 1.25rem;
        }
      }
    `
    document.head.appendChild(style)
  }

  private setupEventListeners(): void {
    // Keyboard navigation
    document.addEventListener('keydown', this.handleKeydown)
  }

  private handleKeydown = (e: KeyboardEvent): void => {
    switch (e.key) {
      case 'ArrowRight':
      case 'Enter':
        if (this.currentStep < this.steps.length - 1) {
          this.handleNext()
        } else {
          this.handleComplete()
        }
        break
      case 'ArrowLeft':
        if (this.currentStep > 0) {
          this.handlePrevious()
        }
        break
      case 'Escape':
        this.handleSkip()
        break
    }
  }

  private updateStep(): void {
    // Update step indicator
    this.stepIndicator.innerHTML = ''
    for (let i = 0; i < this.steps.length; i++) {
      const dot = document.createElement('div')
      dot.className = 'step-dot'
      if (i < this.currentStep) {
        dot.classList.add('completed')
      } else if (i === this.currentStep) {
        dot.classList.add('active')
      }
      this.stepIndicator.appendChild(dot)
    }
    
    // Update step content
    const step = this.steps[this.currentStep]
    this.stepContent.innerHTML = `
      <div class="step-icon">${step.icon}</div>
      <div class="step-title">${step.title}</div>
      <div class="step-text">${step.content}</div>
    `
    this.stepContent.classList.add('step-content-enter')
    
    // Update buttons
    this.prevButton.setDisabled(this.currentStep === 0)
    
    if (this.currentStep === this.steps.length - 1) {
      this.nextButton.setText('Get Started!')
    } else {
      this.nextButton.setText('Next')
    }
  }

  private handleNext = (): void => {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++
      this.updateStep()
    } else {
      this.handleComplete()
    }
  }

  private handlePrevious = (): void => {
    if (this.currentStep > 0) {
      this.currentStep--
      this.updateStep()
    }
  }

  private handleSkip = (): void => {
    this.options.onSkip()
  }

  private handleComplete = (): void => {
    this.options.onComplete()
  }

  public show(): void {
    document.body.appendChild(this.element)
  }

  public hide(): void {
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
  }

  public destroy(): void {
    document.removeEventListener('keydown', this.handleKeydown)
    this.hide()
    this.panel.destroy()
    this.nextButton.destroy()
    this.skipButton.destroy()
    this.prevButton.destroy()
  }

  public getElement(): HTMLElement {
    return this.element
  }
}
