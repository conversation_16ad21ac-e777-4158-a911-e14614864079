{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/program/namespace/index.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAKlC,OAAO,kBAA4C,MAAM,kBAAkB,CAAC;AAC5E,OAAO,kBAA4C,MAAM,kBAAkB,CAAC;AAC5E,OAAO,UAA4B,MAAM,UAAU,CAAC;AACpD,OAAO,cAAoC,MAAM,cAAc,CAAC;AAChE,OAAO,eAAsC,MAAM,eAAe,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,qBAAqB,EAAoB,MAAM,WAAW,CAAC;AACpE,OAAO,WAA8B,MAAM,SAAS,CAAC;AAOrD,OAAO,EAAoB,aAAa,EAAkB,MAAM,cAAc,CAAC;AAG/E,OAAO,EAAE,qBAAqB,EAAoB,MAAM,WAAW,CAAC;AAGpE,MAAM,CAAC,OAAO,OAAO,gBAAgB;IACnC;;OAEG;IACI,MAAM,CAAC,KAAK,CACjB,GAAQ,EACR,KAAY,EACZ,SAAoB,EACpB,QAAkB,EAClB,iBAE2C;QAU3C,MAAM,GAAG,GAAiB,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAAsB,EAAE,CAAC;QACvC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,IAAI,GAAkB,EAAE,CAAC;QAE/B,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QAEtC,MAAM,OAAO,GAA0B,GAAG,CAAC,QAAQ;YACjD,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC;YACvD,CAAC,CAAE,EAA4B,CAAC;QAElC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CACrC,KAAK,EACL,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EACpD,SAAS,CACV,CAAC;YACF,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CACxC,KAAK,EACL,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,SAAS,EACT,GAAG,CACJ,CAAC;YACF,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;YACxE,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAC5C,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,GAAG,CAAC,KAAK,IAAI,EAAE,EACf,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAC9C,CAAC;YACF,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEnC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC3B,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;YAC3B,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAwB;YACxB,WAAwC;YACxC,WAAwC;YACxC,OAAO;YACP,QAAkC;YAClC,OAAgC;YAChC,IAA0B;SAC3B,CAAC;IACJ,CAAC;CACF"}