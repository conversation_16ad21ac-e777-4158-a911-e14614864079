#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Paths
const sourceIdlPath = path.join(__dirname, '../target/idl/agar_escrow.json');
const targetIdlPath = path.join(__dirname, '../../../docs/idl/agar_escrow.json');

// Ensure target directory exists
const targetDir = path.dirname(targetIdlPath);
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy IDL file if it exists
if (fs.existsSync(sourceIdlPath)) {
  fs.copyFileSync(sourceIdlPath, targetIdlPath);
  console.log('✅ IDL copied to docs/idl/agar_escrow.json');
} else {
  console.log('⚠️  IDL file not found. Run "anchor build" first.');
}
