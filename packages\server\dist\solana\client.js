import { Connection, Keypair } from '@solana/web3.js';
import { AnchorProvider, Wallet } from '@coral-xyz/anchor';
import { PythSolanaReceiver } from '@pythnetwork/pyth-solana-receiver';
import { validateEnv } from '../config/env.js';
import pino from 'pino';
const logger = pino({ name: 'solana-client' });
const env = validateEnv();
export class SolanaClient {
    connection;
    provider;
    program = null;
    pythReceiver = null;
    wallet;
    isInitialized = false;
    constructor() {
        // Initialize connection
        this.connection = new Connection(env.SOLANA_RPC_URL, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 60000,
        });
        // Initialize wallet from private key
        const keypair = Keypair.fromSecretKey(new Uint8Array(JSON.parse(env.SOLANA_PRIVATE_KEY)));
        this.wallet = new Wallet(keypair);
        // Initialize provider
        this.provider = new AnchorProvider(this.connection, this.wallet, {
            commitment: 'confirmed',
            preflightCommitment: 'confirmed',
        });
        logger.info(`Solana client initialized with RPC: ${env.SOLANA_RPC_URL}`);
        logger.info(`Wallet public key: ${this.wallet.publicKey.toString()}`);
    }
    async initialize() {
        try {
            // Load the Anchor program
            await this.loadProgram();
            // Initialize Pyth price feeds
            await this.initializePyth();
            // Verify connection
            await this.verifyConnection();
            this.isInitialized = true;
            logger.info('Solana client fully initialized');
        }
        catch (error) {
            logger.error(error, 'Failed to initialize Solana client');
            throw error;
        }
    }
    async loadProgram() {
        try {
            // TODO: Load the actual Anchor program
            // For now, we'll create a placeholder
            // const idl = await Program.fetchIdl(new PublicKey(env.PROGRAM_ID), this.provider)
            // this.program = new Program(idl, new PublicKey(env.PROGRAM_ID), this.provider)
            logger.info('Anchor program loaded successfully');
        }
        catch (error) {
            logger.error(error, 'Failed to load Anchor program');
            throw error;
        }
    }
    async initializePyth() {
        try {
            this.pythReceiver = new PythSolanaReceiver({
                connection: this.connection,
                wallet: this.wallet,
            });
            logger.info('Pyth price feeds initialized');
        }
        catch (error) {
            logger.error(error, 'Failed to initialize Pyth');
            throw error;
        }
    }
    async verifyConnection() {
        try {
            const slot = await this.connection.getSlot();
            const balance = await this.connection.getBalance(this.wallet.publicKey);
            logger.info(`Connected to Solana at slot ${slot}`);
            logger.info(`Wallet balance: ${balance / 1e9} SOL`);
            if (balance === 0) {
                logger.warn('Wallet has zero balance - transactions will fail');
            }
        }
        catch (error) {
            logger.error(error, 'Failed to verify Solana connection');
            throw error;
        }
    }
    // Get SOL/USD price from Pyth
    async getSOLPrice() {
        if (!this.pythReceiver) {
            throw new Error('Pyth not initialized');
        }
        try {
            // TODO: Implement actual Pyth price fetching
            // For now, return a mock price
            const mockPrice = 100 + Math.random() * 20; // $100-120
            logger.debug(`SOL price: $${mockPrice.toFixed(2)}`);
            return mockPrice;
        }
        catch (error) {
            logger.error(error, 'Failed to get SOL price');
            throw error;
        }
    }
    // Convert SOL amount to USD
    async solToUSD(solAmount) {
        const solPrice = await this.getSOLPrice();
        return solAmount * solPrice;
    }
    // Convert USD amount to SOL
    async usdToSOL(usdAmount) {
        const solPrice = await this.getSOLPrice();
        return usdAmount / solPrice;
    }
    // Create a new game session account
    async createGameSession(playerWallet, wagerAmount) {
        if (!this.program) {
            throw new Error('Program not loaded');
        }
        try {
            // TODO: Implement actual program instruction
            // For now, return a mock transaction signature
            const mockTxSignature = 'mock_tx_' + Date.now();
            logger.info(`Created game session for ${playerWallet.toString()} with wager ${wagerAmount} SOL`);
            return mockTxSignature;
        }
        catch (error) {
            logger.error(error, 'Failed to create game session');
            throw error;
        }
    }
    // Process cashout transaction
    async processCashout(playerWallet, sessionId, finalAmount) {
        if (!this.program) {
            throw new Error('Program not loaded');
        }
        try {
            // TODO: Implement actual cashout transaction
            // For now, return a mock transaction signature
            const mockTxSignature = 'cashout_tx_' + Date.now();
            logger.info(`Processed cashout for ${playerWallet.toString()}: ${finalAmount} SOL`);
            return mockTxSignature;
        }
        catch (error) {
            logger.error(error, 'Failed to process cashout');
            throw error;
        }
    }
    // Validate a transaction signature
    async validateTransaction(signature) {
        try {
            const status = await this.connection.getSignatureStatus(signature);
            return status.value?.confirmationStatus === 'confirmed' ||
                status.value?.confirmationStatus === 'finalized';
        }
        catch (error) {
            logger.error(error, `Failed to validate transaction ${signature}`);
            return false;
        }
    }
    // Get account balance
    async getBalance(publicKey) {
        try {
            const balance = await this.connection.getBalance(publicKey);
            return balance / 1e9; // Convert lamports to SOL
        }
        catch (error) {
            logger.error(error, `Failed to get balance for ${publicKey.toString()}`);
            return 0;
        }
    }
    // Health check
    async healthCheck() {
        try {
            await this.connection.getSlot();
            return true;
        }
        catch (error) {
            logger.error(error, 'Solana health check failed');
            return false;
        }
    }
    // Get connection info
    getConnectionInfo() {
        return {
            rpcUrl: env.SOLANA_RPC_URL,
            commitment: 'confirmed',
            walletAddress: this.wallet.publicKey.toString(),
            isInitialized: this.isInitialized,
        };
    }
    // Cleanup
    async cleanup() {
        // Close any open connections or subscriptions
        logger.info('Solana client cleanup completed');
    }
}
// Singleton instance
export const solanaClient = new SolanaClient();
