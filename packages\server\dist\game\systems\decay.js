import { GAME_CONFIG } from '../../config/lobby.js';
import pino from 'pino';
const logger = pino({ name: 'decay-system' });
export class DecaySystem {
    gameState;
    constructor(gameState) {
        this.gameState = gameState;
    }
    update(deltaTime) {
        const world = this.gameState.getWorld();
        const dt = deltaTime / 1000; // Convert to seconds
        // Apply decay to all player cells
        for (const player of world.players.values()) {
            if (!player.isAlive)
                continue;
            for (const cell of player.cells) {
                this.applyCellDecay(cell, dt);
            }
            // Update player's total mass after decay
            this.updatePlayerMassAfterDecay(player);
        }
    }
    applyCellDecay(cell, deltaTime) {
        // Only apply decay if cell is above minimum mass
        if (cell.mass <= GAME_CONFIG.DECAY_MIN_MASS) {
            return;
        }
        // Calculate decay amount based on current mass
        const decayAmount = cell.mass * GAME_CONFIG.DECAY_RATE * deltaTime;
        // Apply decay
        cell.mass = Math.max(GAME_CONFIG.DECAY_MIN_MASS, cell.mass - decayAmount);
        // Update radius based on new mass
        cell.radius = this.massToRadius(cell.mass);
        // Update quadtree position (radius might have changed)
        const world = this.gameState.getWorld();
        world.quadTree.update(cell);
    }
    updatePlayerMassAfterDecay(player) {
        // Recalculate total mass from all cells
        let totalMass = 0;
        for (const cell of player.cells) {
            totalMass += cell.mass;
        }
        player.totalMass = totalMass;
        // Update current value based on new mass
        const massRatio = totalMass / player.initialMass;
        player.currentValue = player.wagerAmount * massRatio;
        // Update cashout eligibility
        player.canCashout = totalMass >= GAME_CONFIG.CASHOUT_MIN_MASS && !player.cashoutStartTime;
    }
    massToRadius(mass) {
        return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR;
    }
}
