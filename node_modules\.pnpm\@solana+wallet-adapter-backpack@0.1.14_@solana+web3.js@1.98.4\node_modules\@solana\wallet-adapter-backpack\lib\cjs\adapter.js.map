{"version": 3, "file": "adapter.js", "sourceRoot": "", "sources": ["../../src/adapter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,qEAcqC;AAErC,6CAA4C;AAiC/B,QAAA,kBAAkB,GAAG,UAAoC,CAAC;AAEvE,MAAa,qBAAsB,SAAQ,oDAA8B;IAerE,YAAY,SAAsC,EAAE;QAChD,KAAK,EAAE,CAAC;QAfZ,SAAI,GAAG,0BAAkB,CAAC;QAC1B,QAAG,GAAG,sBAAsB,CAAC;QAC7B,SAAI,GACA,w+EAAw+E,CAAC;QACp+E,iCAA4B,GAAG,IAAI,CAAC;QAKrC,gBAAW,GACf,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW;YAC5D,CAAC,CAAC,sCAAgB,CAAC,WAAW;YAC9B,CAAC,CAAC,sCAAgB,CAAC,WAAW,CAAC;QAmK/B,kBAAa,GAAG,GAAG,EAAE;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAE7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,6CAAuB,EAAE,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC3B;QACL,CAAC,CAAC;QA1KE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,WAAW,KAAK,sCAAgB,CAAC,WAAW,EAAE;YACnD,IAAA,mDAA6B,EAAC,GAAG,EAAE;;gBAC/B,IAAI,MAAA,MAAM,CAAC,QAAQ,0CAAE,UAAU,EAAE;oBAC7B,IAAI,CAAC,WAAW,GAAG,sCAAgB,CAAC,SAAS,CAAC;oBAC9C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAChD,OAAO,IAAI,CAAC;iBACf;gBACD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;;QACT,OAAO,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,WAAW,CAAA,CAAC;IACvC,CAAC;IAED,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEK,OAAO;;YACT,IAAI;gBACA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAC9C,IAAI,IAAI,CAAC,WAAW,KAAK,sCAAgB,CAAC,SAAS;oBAAE,MAAM,IAAI,yCAAmB,EAAE,CAAC;gBAErF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBAExB,oEAAoE;gBACpE,MAAM,MAAM,GAAG,MAAM,CAAC,QAAS,CAAC;gBAEhC,IAAI;oBACA,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;iBAC1B;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,2CAAqB,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC1D;gBAED,IAAI,CAAC,MAAM,CAAC,SAAS;oBAAE,MAAM,IAAI,wCAAkB,EAAE,CAAC;gBAEtD,IAAI,SAAoB,CAAC;gBACzB,IAAI;oBACA,SAAS,GAAG,IAAI,mBAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;iBACzD;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,0CAAoB,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBACzD;gBAED,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAE5C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;gBACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAE5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACnC;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,KAAK,CAAC;aACf;oBAAS;gBACN,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;aAC5B;QACL,CAAC;KAAA;IAEK,UAAU;;YACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAE7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBAEvB,IAAI;oBACA,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;iBAC7B;gBAAC,OAAO,KAAU,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,8CAAwB,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;iBAC3E;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;KAAA;IAEK,eAAe,CACjB,WAAwB,EACxB,UAAsB,EACtB,UAAkC,EAAE;;YAEpC,IAAI;gBACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC5B,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,6CAAuB,EAAE,CAAC;gBAEjD,MAAM,EAAE,OAAO,KAAqB,OAAO,EAAvB,WAAW,UAAK,OAAO,EAArC,WAA2B,CAAU,CAAC;gBAE5C,IAAI;oBACA,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC3F;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,gDAA0B,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC/D;aACJ;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEK,eAAe,CAAwB,WAAc;;YACvD,IAAI;gBACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC5B,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,6CAAuB,EAAE,CAAC;gBAEjD,IAAI;oBACA,OAAO,CAAC,MAAM,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAM,CAAC;iBAC3E;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,gDAA0B,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC/D;aACJ;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEK,mBAAmB,CAAwB,YAAiB;;YAC9D,IAAI;gBACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC5B,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,6CAAuB,EAAE,CAAC;gBAEjD,IAAI;oBACA,OAAO,CAAC,MAAM,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAQ,CAAC;iBAClF;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,gDAA0B,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC/D;aACJ;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEK,WAAW,CAAC,OAAmB;;YACjC,IAAI;gBACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC5B,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,6CAAuB,EAAE,CAAC;gBAEjD,IAAI;oBACA,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC5D;gBAAC,OAAO,KAAU,EAAE;oBACjB,MAAM,IAAI,4CAAsB,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC;iBAC3D;aACJ;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;CAcJ;AA5LD,sDA4LC"}