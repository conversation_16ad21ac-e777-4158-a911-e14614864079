{"version": 3, "file": "adapter.js", "sourceRoot": "", "sources": ["../../src/adapter.ts"], "names": [], "mappings": "AACA,OAAO,EACH,8BAA8B,EAC9B,6BAA6B,EAC7B,kBAAkB,EAClB,qBAAqB,EACrB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,mBAAmB,EACnB,oBAAoB,EACpB,gBAAgB,EAChB,0BAA0B,EAC1B,sBAAsB,EACtB,0BAA0B,GAC7B,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAiC5C,MAAM,CAAC,MAAM,kBAAkB,GAAG,UAAoC,CAAC;AAEvE,MAAM,OAAO,qBAAsB,SAAQ,8BAA8B;IAerE,YAAY,SAAsC,EAAE;QAChD,KAAK,EAAE,CAAC;QAfZ,SAAI,GAAG,kBAAkB,CAAC;QAC1B,QAAG,GAAG,sBAAsB,CAAC;QAC7B,SAAI,GACA,w+EAAw+E,CAAC;QACp+E,iCAA4B,GAAG,IAAI,CAAC;QAKrC,gBAAW,GACf,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW;YAC5D,CAAC,CAAC,gBAAgB,CAAC,WAAW;YAC9B,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAmK/B,kBAAa,GAAG,GAAG,EAAE;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAE7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,uBAAuB,EAAE,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC3B;QACL,CAAC,CAAC;QA1KE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,WAAW,KAAK,gBAAgB,CAAC,WAAW,EAAE;YACnD,6BAA6B,CAAC,GAAG,EAAE;gBAC/B,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE;oBAC7B,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC;oBAC9C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAChD,OAAO,IAAI,CAAC;iBACf;gBACD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI;YACA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC9C,IAAI,IAAI,CAAC,WAAW,KAAK,gBAAgB,CAAC,SAAS;gBAAE,MAAM,IAAI,mBAAmB,EAAE,CAAC;YAErF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,oEAAoE;YACpE,MAAM,MAAM,GAAG,MAAM,CAAC,QAAS,CAAC;YAEhC,IAAI;gBACA,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;aAC1B;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC1D;YAED,IAAI,CAAC,MAAM,CAAC,SAAS;gBAAE,MAAM,IAAI,kBAAkB,EAAE,CAAC;YAEtD,IAAI,SAAoB,CAAC;YACzB,IAAI;gBACA,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;aACzD;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACzD;YAED,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAE5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SACnC;QAAC,OAAO,KAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;gBAAS;YACN,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC5B;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAE7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAEvB,IAAI;gBACA,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;aAC7B;YAAC,OAAO,KAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,wBAAwB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;aAC3E;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,WAAwB,EACxB,UAAsB,EACtB,UAAkC,EAAE;QAEpC,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YAEjD,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;YAE5C,IAAI;gBACA,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC3F;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,0BAA0B,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC/D;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAwB,WAAc;QACvD,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YAEjD,IAAI;gBACA,OAAO,CAAC,MAAM,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAM,CAAC;aAC3E;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,0BAA0B,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC/D;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAwB,YAAiB;QAC9D,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YAEjD,IAAI;gBACA,OAAO,CAAC,MAAM,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAQ,CAAC;aAClF;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,0BAA0B,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC/D;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAmB;QACjC,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,uBAAuB,EAAE,CAAC;YAEjD,IAAI;gBACA,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAAC,OAAO,KAAU,EAAE;gBACjB,MAAM,IAAI,sBAAsB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aAC3D;SACJ;QAAC,OAAO,KAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;CAcJ"}