import pino from 'pino';
import { validateEnv } from '../config/env.js';
const env = validateEnv();
// Create base logger configuration
const loggerConfig = {
    name: 'agario-solana-server',
    level: env.LOG_LEVEL,
    // Production logging configuration
    ...(env.NODE_ENV === 'production' ? {
        // Structured JSON logging for production
        serializers: pino.stdSerializers,
        redact: {
            paths: ['req.headers.authorization', 'wallet.privateKey', 'signature'],
            censor: '[REDACTED]',
        },
        // Performance optimizations
        timestamp: pino.stdTimeFunctions.isoTime,
        formatters: {
            level: (label) => ({ level: label }),
        },
    } : {
        // Pretty printing for development
        transport: {
            target: 'pino-pretty',
            options: {
                colorize: true,
                translateTime: 'HH:MM:ss Z',
                ignore: 'pid,hostname',
                singleLine: true,
            },
        },
    }),
};
// Create base logger
export const logger = pino(loggerConfig);
// Create specialized loggers for different components
export const gameLogger = logger.child({ component: 'game' });
export const networkLogger = logger.child({ component: 'network' });
export const solanaLogger = logger.child({ component: 'solana' });
export const authLogger = logger.child({ component: 'auth' });
export const dbLogger = logger.child({ component: 'database' });
// Error categorization
export var ErrorCategory;
(function (ErrorCategory) {
    ErrorCategory["VALIDATION"] = "validation";
    ErrorCategory["AUTHENTICATION"] = "authentication";
    ErrorCategory["AUTHORIZATION"] = "authorization";
    ErrorCategory["NETWORK"] = "network";
    ErrorCategory["DATABASE"] = "database";
    ErrorCategory["SOLANA"] = "solana";
    ErrorCategory["GAME_LOGIC"] = "game_logic";
    ErrorCategory["ANTI_CHEAT"] = "anti_cheat";
    ErrorCategory["SYSTEM"] = "system";
    ErrorCategory["UNKNOWN"] = "unknown";
})(ErrorCategory || (ErrorCategory = {}));
// Error severity levels
export var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "low";
    ErrorSeverity["MEDIUM"] = "medium";
    ErrorSeverity["HIGH"] = "high";
    ErrorSeverity["CRITICAL"] = "critical";
})(ErrorSeverity || (ErrorSeverity = {}));
// Enhanced error logging function
export function logError(error, context, logger = gameLogger) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    const logData = {
        error: errorMessage,
        stack: errorStack,
        category: context.category,
        severity: context.severity,
        playerId: context.playerId,
        walletAddress: context.walletAddress,
        sessionToken: context.sessionToken,
        lobbyTier: context.lobbyTier,
        ip: context.ip,
        userAgent: context.userAgent,
        timestamp: Date.now(),
        ...context.additionalData,
    };
    // Log at appropriate level based on severity
    switch (context.severity) {
        case ErrorSeverity.CRITICAL:
            logger.fatal(logData, `CRITICAL ERROR: ${errorMessage}`);
            break;
        case ErrorSeverity.HIGH:
            logger.error(logData, `HIGH SEVERITY: ${errorMessage}`);
            break;
        case ErrorSeverity.MEDIUM:
            logger.warn(logData, `MEDIUM SEVERITY: ${errorMessage}`);
            break;
        case ErrorSeverity.LOW:
        default:
            logger.info(logData, `LOW SEVERITY: ${errorMessage}`);
            break;
    }
}
export function logPerformance(metrics, logger = gameLogger) {
    logger.info({
        type: 'performance',
        operation: metrics.operation,
        duration: metrics.duration,
        success: metrics.success,
        entityCount: metrics.entityCount,
        playerCount: metrics.playerCount,
        memoryUsage: metrics.memoryUsage,
        timestamp: Date.now(),
        ...metrics.additionalMetrics,
    }, `Performance: ${metrics.operation} (${metrics.duration}ms)`);
}
export function logSecurityEvent(event, logger = authLogger) {
    const logData = {
        type: 'security_event',
        eventType: event.type,
        severity: event.severity,
        playerId: event.playerId,
        walletAddress: event.walletAddress,
        ip: event.ip,
        userAgent: event.userAgent,
        details: event.details,
        timestamp: Date.now(),
        ...event.additionalData,
    };
    switch (event.severity) {
        case ErrorSeverity.CRITICAL:
            logger.fatal(logData, `SECURITY CRITICAL: ${event.details}`);
            break;
        case ErrorSeverity.HIGH:
            logger.error(logData, `SECURITY HIGH: ${event.details}`);
            break;
        case ErrorSeverity.MEDIUM:
            logger.warn(logData, `SECURITY MEDIUM: ${event.details}`);
            break;
        case ErrorSeverity.LOW:
        default:
            logger.info(logData, `SECURITY LOW: ${event.details}`);
            break;
    }
}
export function logGameEvent(event, logger = gameLogger) {
    logger.info({
        type: 'game_event',
        eventType: event.type,
        playerId: event.playerId,
        walletAddress: event.walletAddress,
        lobbyTier: event.lobbyTier,
        details: event.details,
        timestamp: Date.now(),
        ...event.gameData,
    }, `Game Event: ${event.type} - ${event.details || 'No details'}`);
}
export function logTransaction(event, logger = solanaLogger) {
    logger.info({
        type: 'transaction',
        eventType: event.type,
        transactionId: event.transactionId,
        walletAddress: event.walletAddress,
        amount: event.amount,
        solPrice: event.solPrice,
        status: event.status,
        error: event.error,
        timestamp: Date.now(),
        ...event.additionalData,
    }, `Transaction: ${event.type} - ${event.status} (${event.amount} SOL)`);
}
// Metrics collection for monitoring
export class MetricsCollector {
    metrics = new Map();
    counters = new Map();
    timers = new Map();
    // Increment a counter
    increment(name, value = 1) {
        const current = this.counters.get(name) || 0;
        this.counters.set(name, current + value);
    }
    // Set a gauge value
    gauge(name, value) {
        this.metrics.set(name, value);
    }
    // Start a timer
    startTimer(name) {
        this.timers.set(name, Date.now());
    }
    // End a timer and record duration
    endTimer(name) {
        const startTime = this.timers.get(name);
        if (!startTime)
            return 0;
        const duration = Date.now() - startTime;
        this.timers.delete(name);
        this.gauge(`${name}_duration`, duration);
        return duration;
    }
    // Get all metrics
    getAllMetrics() {
        return {
            ...Object.fromEntries(this.metrics),
            ...Object.fromEntries(this.counters),
        };
    }
    // Log metrics periodically
    logMetrics(logger = gameLogger) {
        const allMetrics = this.getAllMetrics();
        logger.info({
            type: 'metrics',
            metrics: allMetrics,
            timestamp: Date.now(),
        }, 'System Metrics');
    }
    // Reset all metrics
    reset() {
        this.metrics.clear();
        this.counters.clear();
        this.timers.clear();
    }
}
// Global metrics collector
export const metrics = new MetricsCollector();
// Start periodic metrics logging
if (env.NODE_ENV === 'production') {
    setInterval(() => {
        metrics.logMetrics();
    }, 60000); // Log metrics every minute
}
// Graceful shutdown logging
export function setupGracefulShutdown() {
    const shutdown = (signal) => {
        logger.info(`Received ${signal}, shutting down gracefully...`);
        // Log final metrics
        metrics.logMetrics();
        // Flush logs
        logger.flush();
        process.exit(0);
    };
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
}
// Export default logger
export default logger;
