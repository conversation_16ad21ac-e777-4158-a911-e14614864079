import { GameState } from './state.js'
import { GameSystems } from './systems/index.js'
import { PlayerInput, LobbyConfig, PerformanceMetrics } from './types.js'
import { GAME_CONFIG } from '../config/lobby.js'
import { inputReconciliation } from './reconciliation.js'
import pino from 'pino'

const logger = pino({ name: 'game-loop' })

export class GameLoop {
  private gameState: GameState
  private gameSystems: GameSystems
  private isRunning = false
  private tickInterval: NodeJS.Timeout | null = null
  private lastTickTime = 0
  private tickDuration = 1000 / GAME_CONFIG.TICK_RATE
  private performanceMetrics: PerformanceMetrics = {
    tickTime: 0,
    entityCount: 0,
    collisionChecks: 0,
    networkMessages: 0,
    memoryUsage: 0,
    cpuUsage: 0,
  }

  constructor(lobbyTier: number, config: LobbyConfig) {
    this.gameState = new GameState(lobbyTier, config)
    this.gameSystems = new GameSystems(this.gameState)
    
    logger.info(`Game loop initialized for lobby tier ${lobbyTier}`)
  }

  // Start the game loop
  start(): void {
    if (this.isRunning) {
      logger.warn('Game loop is already running')
      return
    }

    this.isRunning = true
    this.lastTickTime = Date.now()
    
    // Use setInterval for consistent timing
    this.tickInterval = setInterval(() => {
      this.tick()
    }, this.tickDuration)

    logger.info(`Game loop started with ${GAME_CONFIG.TICK_RATE} TPS`)
  }

  // Stop the game loop
  stop(): void {
    if (!this.isRunning) {
      logger.warn('Game loop is not running')
      return
    }

    this.isRunning = false
    
    if (this.tickInterval) {
      clearInterval(this.tickInterval)
      this.tickInterval = null
    }

    logger.info('Game loop stopped')
  }

  // Main game tick
  private tick(): void {
    const tickStart = Date.now()
    const deltaTime = tickStart - this.lastTickTime
    this.lastTickTime = tickStart

    try {
      // Update game state tick counter
      this.gameState.updateTick()

      // Run all game systems
      this.gameSystems.update(deltaTime)

      // Create server snapshots for reconciliation
      this.createReconciliationSnapshots()

      // Update performance metrics
      this.updatePerformanceMetrics(tickStart)

      // Log performance warnings if tick takes too long
      const tickTime = Date.now() - tickStart
      if (tickTime > this.tickDuration * 0.8) {
        logger.warn(`Slow tick detected: ${tickTime}ms (target: ${this.tickDuration}ms)`)
      }

    } catch (error) {
      logger.error(error, 'Error during game tick')
    }
  }

  // Process player input
  processInput(input: PlayerInput): boolean {
    try {
      return this.gameSystems.processInput(input)
    } catch (error) {
      logger.error(error, `Error processing input from player ${input.playerId}`)
      return false
    }
  }

  // Get game state
  getGameState(): GameState {
    return this.gameState
  }

  // Get performance metrics
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics }
  }

  // Check if loop is running
  isGameRunning(): boolean {
    return this.isRunning
  }

  // Get current tick rate
  getCurrentTickRate(): number {
    return this.performanceMetrics.tickTime > 0 ? 1000 / this.performanceMetrics.tickTime : 0
  }

  // Create reconciliation snapshots for all players
  private createReconciliationSnapshots(): void {
    const players = this.gameState.getPlayers()
    const currentTick = this.gameState.getCurrentTick()

    for (const player of players) {
      if (player.isAlive) {
        inputReconciliation.createServerSnapshot(player, currentTick)
      }
    }
  }

  // Update performance metrics
  private updatePerformanceMetrics(tickStart: number): void {
    const world = this.gameState.getWorld()

    this.performanceMetrics = {
      tickTime: Date.now() - tickStart,
      entityCount: world.players.size + world.pellets.size + world.viruses.size + world.ejectedMass.size,
      collisionChecks: this.gameSystems.getCollisionChecks(),
      networkMessages: 0, // Will be updated by network layer
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
      cpuUsage: process.cpuUsage().user / 1000, // Convert to milliseconds
    }
  }

  // Graceful shutdown
  async shutdown(): Promise<void> {
    logger.info('Shutting down game loop...')
    
    this.stop()
    
    // Save any final state if needed
    // await this.saveGameState()
    
    logger.info('Game loop shutdown complete')
  }
}

// Game loop manager for multiple lobbies
export class GameLoopManager {
  private gameLoops = new Map<number, GameLoop>()
  private configs = new Map<number, LobbyConfig>()

  constructor() {
    logger.info('Game loop manager initialized')
  }

  // Create a new game loop for a lobby tier
  createLobby(lobbyTier: number, config: LobbyConfig): GameLoop {
    if (this.gameLoops.has(lobbyTier)) {
      logger.warn(`Lobby ${lobbyTier} already exists`)
      return this.gameLoops.get(lobbyTier)!
    }

    const gameLoop = new GameLoop(lobbyTier, config)
    this.gameLoops.set(lobbyTier, gameLoop)
    this.configs.set(lobbyTier, config)
    
    // Start the game loop
    gameLoop.start()
    
    logger.info(`Created and started lobby ${lobbyTier}`)
    return gameLoop
  }

  // Get game loop for a lobby tier
  getLobby(lobbyTier: number): GameLoop | undefined {
    return this.gameLoops.get(lobbyTier)
  }

  // Get all active lobbies
  getAllLobbies(): Map<number, GameLoop> {
    return new Map(this.gameLoops)
  }

  // Remove a lobby
  removeLobby(lobbyTier: number): void {
    const gameLoop = this.gameLoops.get(lobbyTier)
    if (gameLoop) {
      gameLoop.stop()
      this.gameLoops.delete(lobbyTier)
      this.configs.delete(lobbyTier)
      logger.info(`Removed lobby ${lobbyTier}`)
    }
  }

  // Get performance metrics for all lobbies
  getAllPerformanceMetrics(): Map<number, PerformanceMetrics> {
    const metrics = new Map<number, PerformanceMetrics>()
    
    for (const [tier, gameLoop] of this.gameLoops) {
      metrics.set(tier, gameLoop.getPerformanceMetrics())
    }
    
    return metrics
  }

  // Shutdown all lobbies
  async shutdown(): Promise<void> {
    logger.info('Shutting down all game loops...')
    
    const shutdownPromises: Promise<void>[] = []
    
    for (const gameLoop of this.gameLoops.values()) {
      shutdownPromises.push(gameLoop.shutdown())
    }
    
    await Promise.all(shutdownPromises)
    
    this.gameLoops.clear()
    this.configs.clear()
    
    logger.info('All game loops shut down')
  }
}

// Singleton instance
export const gameLoopManager = new GameLoopManager()
