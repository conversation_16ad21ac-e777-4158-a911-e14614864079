import { Vector2, PlayerInput } from '../types/game.js'
import { useGameStore } from '../store/game-store.js'

export interface InputState {
  // Mouse state
  mousePosition: Vector2
  mouseWorldPosition: Vector2
  isMouseDown: boolean
  
  // Keyboard state
  keys: Set<string>
  
  // Action states
  isSplitting: boolean
  isEjecting: boolean
  isCashingOut: boolean
  cashoutStartTime?: number
  
  // Input timing
  lastInputTime: number
  inputCooldowns: Map<string, number>
}

export interface InputConfig {
  // Key bindings
  splitKey: string
  ejectKey: string
  cashoutKey: string
  
  // Cooldowns (in milliseconds)
  splitCooldown: number
  ejectCooldown: number
  cashoutHoldTime: number
  
  // Input validation
  maxInputRate: number // inputs per second
  inputBufferSize: number
}

export class InputManager {
  private canvas: HTMLCanvasElement
  private config: InputConfig
  private state: InputState
  private camera: any // Will be injected
  
  // Event listeners
  private eventListeners: Array<{ element: EventTarget; event: string; handler: EventListener }> = []
  
  constructor(canvas: HTMLCanvasElement, config: Partial<InputConfig> = {}) {
    this.canvas = canvas
    this.config = {
      splitKey: 'Space',
      ejectKey: 'KeyW',
      cashoutKey: 'KeyQ',
      splitCooldown: 100,
      ejectCooldown: 50,
      cashoutHoldTime: 5000,
      maxInputRate: 30,
      inputBufferSize: 10,
      ...config
    }
    
    this.state = {
      mousePosition: { x: 0, y: 0 },
      mouseWorldPosition: { x: 0, y: 0 },
      isMouseDown: false,
      keys: new Set(),
      isSplitting: false,
      isEjecting: false,
      isCashingOut: false,
      lastInputTime: 0,
      inputCooldowns: new Map()
    }
    
    this.setupEventListeners()
  }
  
  /**
   * Set camera reference for world coordinate conversion
   */
  setCamera(camera: any): void {
    this.camera = camera
  }
  
  private setupEventListeners(): void {
    // Mouse events
    this.addEventListener(this.canvas, 'mousemove', this.handleMouseMove)
    this.addEventListener(this.canvas, 'mousedown', this.handleMouseDown)
    this.addEventListener(this.canvas, 'mouseup', this.handleMouseUp)
    this.addEventListener(this.canvas, 'contextmenu', this.handleContextMenu)
    
    // Keyboard events
    this.addEventListener(window, 'keydown', this.handleKeyDown)
    this.addEventListener(window, 'keyup', this.handleKeyUp)
    
    // Touch events for mobile support
    this.addEventListener(this.canvas, 'touchstart', this.handleTouchStart)
    this.addEventListener(this.canvas, 'touchmove', this.handleTouchMove)
    this.addEventListener(this.canvas, 'touchend', this.handleTouchEnd)
    
    // Prevent default behaviors
    this.addEventListener(this.canvas, 'selectstart', (e) => e.preventDefault())
    this.addEventListener(this.canvas, 'dragstart', (e) => e.preventDefault())
  }
  
  private addEventListener(element: EventTarget, event: string, handler: EventListener): void {
    element.addEventListener(event, handler)
    this.eventListeners.push({ element, event, handler })
  }
  
  private handleMouseMove = (event: MouseEvent): void => {
    const rect = this.canvas.getBoundingClientRect()
    this.state.mousePosition = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
    
    // Convert to world coordinates if camera is available
    if (this.camera) {
      this.state.mouseWorldPosition = this.camera.screenToWorld(this.state.mousePosition)
    }
    
    this.sendMovementInput()
  }
  
  private handleMouseDown = (event: MouseEvent): void => {
    event.preventDefault()
    this.state.isMouseDown = true
    
    // Handle different mouse buttons
    switch (event.button) {
      case 0: // Left click - could be used for special actions
        break
      case 2: // Right click - split
        this.handleSplit()
        break
    }
  }
  
  private handleMouseUp = (event: MouseEvent): void => {
    event.preventDefault()
    this.state.isMouseDown = false
  }
  
  private handleContextMenu = (event: MouseEvent): void => {
    event.preventDefault() // Prevent right-click menu
  }
  
  private handleKeyDown = (event: KeyboardEvent): void => {
    // Prevent default for game keys
    if ([this.config.splitKey, this.config.ejectKey, this.config.cashoutKey].includes(event.code)) {
      event.preventDefault()
    }
    
    this.state.keys.add(event.code)
    
    // Handle specific actions
    switch (event.code) {
      case this.config.splitKey:
        this.handleSplit()
        break
      case this.config.ejectKey:
        this.handleEject()
        break
      case this.config.cashoutKey:
        this.handleCashoutStart()
        break
    }
  }
  
  private handleKeyUp = (event: KeyboardEvent): void => {
    this.state.keys.delete(event.code)
    
    // Handle key releases
    switch (event.code) {
      case this.config.cashoutKey:
        this.handleCashoutEnd()
        break
    }
  }
  
  private handleTouchStart = (event: TouchEvent): void => {
    event.preventDefault()
    const touch = event.touches[0]
    if (touch) {
      const rect = this.canvas.getBoundingClientRect()
      this.state.mousePosition = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      }
      
      if (this.camera) {
        this.state.mouseWorldPosition = this.camera.screenToWorld(this.state.mousePosition)
      }
      
      this.sendMovementInput()
    }
  }
  
  private handleTouchMove = (event: TouchEvent): void => {
    event.preventDefault()
    const touch = event.touches[0]
    if (touch) {
      const rect = this.canvas.getBoundingClientRect()
      this.state.mousePosition = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      }
      
      if (this.camera) {
        this.state.mouseWorldPosition = this.camera.screenToWorld(this.state.mousePosition)
      }
      
      this.sendMovementInput()
    }
  }
  
  private handleTouchEnd = (event: TouchEvent): void => {
    event.preventDefault()
    // Handle touch gestures for actions
    if (event.touches.length === 0) {
      // Single tap could trigger split
      this.handleSplit()
    }
  }
  
  private handleSplit(): void {
    if (this.isActionOnCooldown('split')) return
    
    this.state.isSplitting = true
    this.setActionCooldown('split', this.config.splitCooldown)
    this.sendActionInput('split')
    
    // Reset split state after a short delay
    setTimeout(() => {
      this.state.isSplitting = false
    }, 50)
  }
  
  private handleEject(): void {
    if (this.isActionOnCooldown('eject')) return
    
    this.state.isEjecting = true
    this.setActionCooldown('eject', this.config.ejectCooldown)
    this.sendActionInput('eject')
    
    // Reset eject state after a short delay
    setTimeout(() => {
      this.state.isEjecting = false
    }, 50)
  }
  
  private handleCashoutStart(): void {
    if (this.state.isCashingOut) return
    
    this.state.isCashingOut = true
    this.state.cashoutStartTime = Date.now()
    
    // Start cashout timer
    setTimeout(() => {
      if (this.state.isCashingOut && this.state.keys.has(this.config.cashoutKey)) {
        this.sendActionInput('cashout')
      }
    }, this.config.cashoutHoldTime)
  }
  
  private handleCashoutEnd(): void {
    this.state.isCashingOut = false
    this.state.cashoutStartTime = undefined
  }
  
  private sendMovementInput(): void {
    if (!this.canSendInput()) return
    
    const gameStore = useGameStore.getState()
    if (!gameStore.playerId || !gameStore.sessionToken) return
    
    const input: PlayerInput = {
      playerId: gameStore.playerId,
      sessionToken: gameStore.sessionToken,
      timestamp: Date.now(),
      sequenceNumber: gameStore.getNextInputSequence(),
      targetX: this.state.mouseWorldPosition.x,
      targetY: this.state.mouseWorldPosition.y
    }
    
    gameStore.addPendingInput(input)
    this.state.lastInputTime = Date.now()
    
    // Send to server via socket (will be implemented in network layer)
    this.emitInput(input)
  }
  
  private sendActionInput(action: 'split' | 'eject' | 'cashout'): void {
    if (!this.canSendInput()) return
    
    const gameStore = useGameStore.getState()
    if (!gameStore.playerId || !gameStore.sessionToken) return
    
    const input: PlayerInput = {
      playerId: gameStore.playerId,
      sessionToken: gameStore.sessionToken,
      timestamp: Date.now(),
      sequenceNumber: gameStore.getNextInputSequence(),
      targetX: this.state.mouseWorldPosition.x,
      targetY: this.state.mouseWorldPosition.y,
      [action]: true
    }
    
    gameStore.addPendingInput(input)
    this.state.lastInputTime = Date.now()
    
    // Send to server via socket
    this.emitInput(input)
  }
  
  private emitInput(input: PlayerInput): void {
    // This will be implemented when we create the socket client
    // For now, just log the input
    console.log('Input:', input)
  }
  
  private canSendInput(): boolean {
    const now = Date.now()
    const timeSinceLastInput = now - this.state.lastInputTime
    const minInterval = 1000 / this.config.maxInputRate
    
    return timeSinceLastInput >= minInterval
  }
  
  private isActionOnCooldown(action: string): boolean {
    const cooldownEnd = this.state.inputCooldowns.get(action)
    return cooldownEnd ? Date.now() < cooldownEnd : false
  }
  
  private setActionCooldown(action: string, duration: number): void {
    this.state.inputCooldowns.set(action, Date.now() + duration)
  }
  
  /**
   * Get current input state
   */
  getState(): InputState {
    return { ...this.state }
  }
  
  /**
   * Get cashout progress (0-1)
   */
  getCashoutProgress(): number {
    if (!this.state.isCashingOut || !this.state.cashoutStartTime) return 0
    
    const elapsed = Date.now() - this.state.cashoutStartTime
    return Math.min(elapsed / this.config.cashoutHoldTime, 1)
  }
  
  /**
   * Check if a key is currently pressed
   */
  isKeyPressed(key: string): boolean {
    return this.state.keys.has(key)
  }
  
  /**
   * Update input configuration
   */
  updateConfig(config: Partial<InputConfig>): void {
    this.config = { ...this.config, ...config }
  }
  
  /**
   * Cleanup event listeners
   */
  destroy(): void {
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    this.eventListeners = []
  }
}
