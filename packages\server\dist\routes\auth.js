import { Router } from 'express';
import { walletAuthService } from '../auth/wallet-auth.js';
import { authMiddleware } from '../auth/middleware.js';
import { solanaClient } from '../solana/client.js';
import { oracleService } from '../solana/oracle.js';
import { PublicKey } from '@solana/web3.js';
import pino from 'pino';
const logger = pino({ name: 'auth-routes' });
const router = Router();
// Apply rate limiting to all auth routes
router.use(authMiddleware.rateLimitSensitive);
router.use(authMiddleware.validateOrigin);
// POST /auth/challenge - Get authentication challenge
router.post('/challenge', async (req, res) => {
    try {
        const { walletAddress } = req.body;
        if (!walletAddress || typeof walletAddress !== 'string') {
            res.status(400).json({ error: 'Wallet address is required' });
            return;
        }
        // Validate wallet address format
        try {
            new PublicKey(walletAddress);
        }
        catch (error) {
            res.status(400).json({ error: 'Invalid wallet address format' });
            return;
        }
        // Generate challenge message
        const challenge = walletAuthService.createAuthChallenge(walletAddress);
        logger.info(`Generated auth challenge for wallet ${walletAddress}`);
        res.json({
            challenge,
            walletAddress,
            timestamp: Date.now(),
        });
    }
    catch (error) {
        logger.error(error, 'Error generating auth challenge');
        res.status(500).json({ error: 'Internal server error' });
    }
});
// POST /auth/login - Authenticate with wallet signature
router.post('/login', async (req, res) => {
    try {
        const { walletAddress, message, signature, wagerAmount } = req.body;
        // Validate required fields
        if (!walletAddress || !message || !signature || typeof wagerAmount !== 'number') {
            res.status(400).json({ error: 'Missing required fields' });
            return;
        }
        // Validate wager amount
        if (wagerAmount <= 0 || wagerAmount > 1000) {
            res.status(400).json({ error: 'Invalid wager amount (must be between 0 and 1000 SOL)' });
            return;
        }
        // Check wallet balance (optional validation)
        try {
            const publicKey = new PublicKey(walletAddress);
            const balance = await solanaClient.getBalance(publicKey);
            if (balance < wagerAmount) {
                res.status(400).json({
                    error: 'Insufficient wallet balance',
                    required: wagerAmount,
                    available: balance,
                });
                return;
            }
        }
        catch (balanceError) {
            logger.warn(balanceError, `Could not check balance for wallet ${walletAddress}`);
            // Continue without balance check
        }
        // Authenticate wallet
        const authResult = await walletAuthService.authenticateWallet(walletAddress, message, signature, wagerAmount);
        if (!authResult.success) {
            res.status(401).json({ error: authResult.error });
            return;
        }
        // Get current SOL price for display
        let solPrice = 0;
        try {
            const priceData = await oracleService.getSOLPrice();
            solPrice = priceData.price;
        }
        catch (priceError) {
            logger.warn(priceError, 'Could not get SOL price for login response');
        }
        logger.info(`Successful login for wallet ${walletAddress}`);
        res.json({
            success: true,
            sessionToken: authResult.sessionToken,
            jwtToken: authResult.jwtToken,
            expiresAt: authResult.expiresAt,
            walletAddress,
            wagerAmount,
            solPrice,
            message: 'Authentication successful',
        });
    }
    catch (error) {
        logger.error(error, 'Error during wallet authentication');
        res.status(500).json({ error: 'Authentication failed' });
    }
});
// POST /auth/refresh - Refresh JWT token
router.post('/refresh', authMiddleware.authenticateSession, async (req, res) => {
    try {
        const session = req.session;
        if (!session || !session.isActive) {
            res.status(401).json({ error: 'Invalid session' });
            return;
        }
        // Create new JWT token
        const jwt = require('jsonwebtoken');
        const newJwtToken = jwt.sign({
            walletAddress: session.walletAddress,
            sessionToken: session.sessionToken,
            wagerAmount: session.wagerAmount,
            timestamp: Date.now(),
        }, process.env.JWT_SECRET, {
            expiresIn: '24h',
            issuer: 'agario-solana-server',
            subject: session.walletAddress,
        });
        // Update session data
        session.jwtToken = newJwtToken;
        session.lastActivity = Date.now();
        logger.info(`JWT token refreshed for wallet ${session.walletAddress}`);
        res.json({
            success: true,
            jwtToken: newJwtToken,
            expiresAt: Date.now() + (24 * 60 * 60 * 1000),
            sessionToken: session.sessionToken,
        });
    }
    catch (error) {
        logger.error(error, 'Error refreshing JWT token');
        res.status(500).json({ error: 'Token refresh failed' });
    }
});
// POST /auth/logout - Logout and invalidate session
router.post('/logout', authMiddleware.authenticateSession, async (req, res) => {
    try {
        const session = req.session;
        if (session) {
            walletAuthService.invalidateSession(session.sessionToken);
            logger.info(`User logged out: ${session.walletAddress}`);
        }
        res.json({
            success: true,
            message: 'Logged out successfully',
        });
    }
    catch (error) {
        logger.error(error, 'Error during logout');
        res.status(500).json({ error: 'Logout failed' });
    }
});
// GET /auth/session - Get current session info
router.get('/session', authMiddleware.authenticateSession, async (req, res) => {
    try {
        const session = req.session;
        if (!session) {
            res.status(401).json({ error: 'No active session' });
            return;
        }
        // Get current SOL price
        let solPrice = 0;
        try {
            const priceData = await oracleService.getSOLPrice();
            solPrice = priceData.price;
        }
        catch (priceError) {
            logger.warn(priceError, 'Could not get SOL price for session info');
        }
        res.json({
            walletAddress: session.walletAddress,
            sessionToken: session.sessionToken,
            wagerAmount: session.wagerAmount,
            createdAt: session.createdAt,
            lastActivity: session.lastActivity,
            isActive: session.isActive,
            solPrice,
        });
    }
    catch (error) {
        logger.error(error, 'Error getting session info');
        res.status(500).json({ error: 'Failed to get session info' });
    }
});
// GET /auth/health - Authentication system health check
router.get('/health', async (req, res) => {
    try {
        const healthCheck = authMiddleware.authHealthCheck();
        res.json({
            status: healthCheck.status,
            timestamp: Date.now(),
            details: healthCheck.details,
        });
    }
    catch (error) {
        logger.error(error, 'Error in auth health check');
        res.status(500).json({
            status: 'unhealthy',
            error: 'Health check failed',
        });
    }
});
// Admin routes
router.use('/admin', authMiddleware.authenticateJWT, authMiddleware.requireAdmin);
// GET /auth/admin/sessions - Get all active sessions (admin only)
router.get('/admin/sessions', async (req, res) => {
    try {
        const stats = walletAuthService.getSessionStats();
        res.json({
            stats,
            timestamp: Date.now(),
        });
    }
    catch (error) {
        logger.error(error, 'Error getting session stats');
        res.status(500).json({ error: 'Failed to get session stats' });
    }
});
// POST /auth/admin/invalidate - Invalidate a session (admin only)
router.post('/admin/invalidate', async (req, res) => {
    try {
        const { sessionToken } = req.body;
        if (!sessionToken) {
            res.status(400).json({ error: 'Session token is required' });
            return;
        }
        walletAuthService.invalidateSession(sessionToken);
        logger.info(`Admin invalidated session: ${sessionToken}`);
        res.json({
            success: true,
            message: 'Session invalidated',
        });
    }
    catch (error) {
        logger.error(error, 'Error invalidating session');
        res.status(500).json({ error: 'Failed to invalidate session' });
    }
});
export default router;
