import { Camera } from './camera.js'
import { Vector2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ParticleEffect } from '../types/game.js'

export interface RendererConfig {
  backgroundColor: string
  gridColor: string
  gridSize: number
  showGrid: boolean
  showDebugInfo: boolean
  antialiasing: boolean
  pixelRatio: number
}

export class Renderer {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private camera: Camera
  private config: RendererConfig
  private lastFrameTime: number = 0
  private frameCount: number = 0
  private fps: number = 0
  
  // Color palettes
  private playerColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  
  constructor(canvas: HTMLCanvasElement, camera: Camera, config: Partial<RendererConfig> = {}) {
    this.canvas = canvas
    this.camera = camera
    
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get 2D rendering context')
    }
    this.ctx = ctx
    
    this.config = {
      backgroundColor: '#0F172A',
      gridColor: '#1E293B',
      gridSize: 50,
      showGrid: true,
      showDebugInfo: false,
      antialiasing: true,
      pixelRatio: window.devicePixelRatio || 1,
      ...config
    }
    
    this.setupCanvas()
  }
  
  private setupCanvas(): void {
    const { pixelRatio } = this.config
    
    // Set canvas size accounting for device pixel ratio
    const rect = this.canvas.getBoundingClientRect()
    this.canvas.width = rect.width * pixelRatio
    this.canvas.height = rect.height * pixelRatio
    
    // Scale context to match device pixel ratio
    this.ctx.scale(pixelRatio, pixelRatio)
    
    // Set canvas CSS size
    this.canvas.style.width = rect.width + 'px'
    this.canvas.style.height = rect.height + 'px'
    
    // Configure rendering quality
    this.ctx.imageSmoothingEnabled = this.config.antialiasing
    this.ctx.imageSmoothingQuality = 'high'
    
    // Update camera viewport
    this.camera.setViewport(rect.width, rect.height)
  }
  
  /**
   * Handle canvas resize
   */
  resize(): void {
    this.setupCanvas()
  }
  
  /**
   * Clear the canvas
   */
  clear(): void {
    const { width, height } = this.canvas.getBoundingClientRect()
    this.ctx.clearRect(0, 0, width, height)
    
    // Fill background
    this.ctx.fillStyle = this.config.backgroundColor
    this.ctx.fillRect(0, 0, width, height)
  }
  
  /**
   * Render the game world
   */
  render(
    players: Map<string, PlayerCell[]>,
    pellets: Map<string, Pellet>,
    viruses: Map<string, Virus>,
    ejectedMass: Map<string, EjectedMass>,
    effects: ParticleEffect[],
    deltaTime: number
  ): void {
    // Update FPS counter
    this.updateFPS(deltaTime)
    
    // Clear canvas
    this.clear()
    
    // Apply camera transform
    this.camera.applyTransform(this.ctx)
    
    // Render grid
    if (this.config.showGrid) {
      this.renderGrid()
    }
    
    // Render entities (back to front)
    this.renderPellets(pellets)
    this.renderEjectedMass(ejectedMass)
    this.renderViruses(viruses)
    this.renderPlayers(players)
    this.renderParticleEffects(effects)
    
    // Restore camera transform
    this.camera.restoreTransform(this.ctx)
    
    // Render UI elements (not affected by camera)
    if (this.config.showDebugInfo) {
      this.renderDebugInfo()
    }
  }
  
  private renderGrid(): void {
    const bounds = this.camera.getVisibleBounds()
    const { gridSize, gridColor } = this.config
    
    this.ctx.strokeStyle = gridColor
    this.ctx.lineWidth = 1 / this.camera.zoom
    this.ctx.globalAlpha = 0.3
    
    // Vertical lines
    const startX = Math.floor(bounds.left / gridSize) * gridSize
    for (let x = startX; x <= bounds.right; x += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(x, bounds.top)
      this.ctx.lineTo(x, bounds.bottom)
      this.ctx.stroke()
    }
    
    // Horizontal lines
    const startY = Math.floor(bounds.top / gridSize) * gridSize
    for (let y = startY; y <= bounds.bottom; y += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(bounds.left, y)
      this.ctx.lineTo(bounds.right, y)
      this.ctx.stroke()
    }
    
    this.ctx.globalAlpha = 1
  }
  
  private renderPellets(pellets: Map<string, Pellet>): void {
    this.ctx.globalAlpha = 0.8
    
    pellets.forEach(pellet => {
      if (!this.camera.isCircleVisible(pellet.position, pellet.radius)) return
      
      this.ctx.fillStyle = pellet.color
      this.ctx.beginPath()
      this.ctx.arc(pellet.position.x, pellet.position.y, pellet.radius, 0, Math.PI * 2)
      this.ctx.fill()
    })
    
    this.ctx.globalAlpha = 1
  }
  
  private renderEjectedMass(ejectedMass: Map<string, EjectedMass>): void {
    ejectedMass.forEach(mass => {
      if (!this.camera.isCircleVisible(mass.position, mass.radius)) return
      
      // Get player color based on playerId
      const colorIndex = this.hashString(mass.playerId) % this.playerColors.length
      const color = this.playerColors[colorIndex]
      
      this.ctx.fillStyle = color
      this.ctx.globalAlpha = 0.7
      this.ctx.beginPath()
      this.ctx.arc(mass.position.x, mass.position.y, mass.radius, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.globalAlpha = 1
    })
  }
  
  private renderViruses(viruses: Map<string, Virus>): void {
    viruses.forEach(virus => {
      if (!this.camera.isCircleVisible(virus.position, virus.radius)) return
      
      // Draw spiky virus shape
      this.ctx.fillStyle = virus.color
      this.ctx.strokeStyle = '#16A085'
      this.ctx.lineWidth = 2 / this.camera.zoom
      
      const spikes = 12
      const innerRadius = virus.radius * 0.7
      const outerRadius = virus.radius
      
      this.ctx.beginPath()
      for (let i = 0; i < spikes * 2; i++) {
        const angle = (i * Math.PI) / spikes
        const radius = i % 2 === 0 ? outerRadius : innerRadius
        const x = virus.position.x + Math.cos(angle) * radius
        const y = virus.position.y + Math.sin(angle) * radius
        
        if (i === 0) {
          this.ctx.moveTo(x, y)
        } else {
          this.ctx.lineTo(x, y)
        }
      }
      this.ctx.closePath()
      this.ctx.fill()
      this.ctx.stroke()
    })
  }
  
  private renderPlayers(players: Map<string, PlayerCell[]>): void {
    players.forEach((cells, playerId) => {
      const colorIndex = this.hashString(playerId) % this.playerColors.length
      const color = this.playerColors[colorIndex]
      
      cells.forEach(cell => {
        if (!this.camera.isCircleVisible(cell.position, cell.radius)) return
        
        // Draw cell body
        this.ctx.fillStyle = color
        this.ctx.strokeStyle = this.darkenColor(color, 0.2)
        this.ctx.lineWidth = 3 / this.camera.zoom
        
        this.ctx.beginPath()
        this.ctx.arc(cell.position.x, cell.position.y, cell.radius, 0, Math.PI * 2)
        this.ctx.fill()
        this.ctx.stroke()
        
        // Draw cell outline for better visibility
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
        this.ctx.lineWidth = 1 / this.camera.zoom
        this.ctx.stroke()
      })
    })
  }
  
  private renderParticleEffects(effects: ParticleEffect[]): void {
    effects.forEach(effect => {
      if (!this.camera.isPointVisible(effect.position, effect.size)) return
      
      const alpha = effect.life / effect.maxLife
      this.ctx.globalAlpha = alpha
      
      switch (effect.type) {
        case 'split':
          this.renderSplitEffect(effect)
          break
        case 'eject':
          this.renderEjectEffect(effect)
          break
        case 'consume':
          this.renderConsumeEffect(effect)
          break
        case 'explosion':
          this.renderExplosionEffect(effect)
          break
      }
    })
    
    this.ctx.globalAlpha = 1
  }
  
  private renderSplitEffect(effect: ParticleEffect): void {
    this.ctx.strokeStyle = effect.color
    this.ctx.lineWidth = 2 / this.camera.zoom
    
    const size = effect.size * (1 - effect.life / effect.maxLife)
    this.ctx.beginPath()
    this.ctx.arc(effect.position.x, effect.position.y, size, 0, Math.PI * 2)
    this.ctx.stroke()
  }
  
  private renderEjectEffect(effect: ParticleEffect): void {
    this.ctx.fillStyle = effect.color
    const size = effect.size * (effect.life / effect.maxLife)
    
    this.ctx.beginPath()
    this.ctx.arc(effect.position.x, effect.position.y, size, 0, Math.PI * 2)
    this.ctx.fill()
  }
  
  private renderConsumeEffect(effect: ParticleEffect): void {
    this.ctx.strokeStyle = effect.color
    this.ctx.lineWidth = 3 / this.camera.zoom
    
    const progress = 1 - effect.life / effect.maxLife
    const size = effect.size * (1 + progress * 0.5)
    
    this.ctx.beginPath()
    this.ctx.arc(effect.position.x, effect.position.y, size, 0, Math.PI * 2)
    this.ctx.stroke()
  }
  
  private renderExplosionEffect(effect: ParticleEffect): void {
    this.ctx.strokeStyle = effect.color
    this.ctx.lineWidth = 2 / this.camera.zoom
    
    const progress = 1 - effect.life / effect.maxLife
    const size = effect.size * progress * 2
    
    // Draw expanding rings
    for (let i = 0; i < 3; i++) {
      const ringSize = size * (1 - i * 0.3)
      if (ringSize > 0) {
        this.ctx.beginPath()
        this.ctx.arc(effect.position.x, effect.position.y, ringSize, 0, Math.PI * 2)
        this.ctx.stroke()
      }
    }
  }
  
  private renderDebugInfo(): void {
    const { width, height } = this.canvas.getBoundingClientRect()
    const cameraState = this.camera.getState()
    
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(10, 10, 200, 120)
    
    this.ctx.fillStyle = '#38BDF8'
    this.ctx.font = '12px monospace'
    this.ctx.fillText(`FPS: ${this.fps.toFixed(1)}`, 20, 30)
    this.ctx.fillText(`Zoom: ${cameraState.zoom.toFixed(2)}`, 20, 50)
    this.ctx.fillText(`Pos: ${cameraState.position.x.toFixed(0)}, ${cameraState.position.y.toFixed(0)}`, 20, 70)
    this.ctx.fillText(`Viewport: ${width.toFixed(0)}x${height.toFixed(0)}`, 20, 90)
    this.ctx.fillText(`Pixel Ratio: ${this.config.pixelRatio}`, 20, 110)
  }
  
  private updateFPS(deltaTime: number): void {
    this.frameCount++
    const currentTime = performance.now()
    
    if (currentTime - this.lastFrameTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastFrameTime = currentTime
    }
  }
  
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }
  
  private darkenColor(color: string, factor: number): string {
    // Simple color darkening - in production, use a proper color library
    const hex = color.replace('#', '')
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) * (1 - factor))
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) * (1 - factor))
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) * (1 - factor))
    
    return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`
  }
  
  /**
   * Get renderer configuration
   */
  getConfig(): RendererConfig {
    return { ...this.config }
  }
  
  /**
   * Update renderer configuration
   */
  updateConfig(config: Partial<RendererConfig>): void {
    this.config = { ...this.config, ...config }
  }
}
