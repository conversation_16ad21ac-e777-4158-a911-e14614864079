import { Server as SocketIOServer, Socket } from 'socket.io'
import { GameLoopManager } from '../game/loop.js'
import { PlayerInput, GameSnapshot, GameD<PERSON><PERSON>, Player } from '../game/types.js'
import { RATE_LIMITS, LOBBY_TIERS } from '../config/lobby.js'
import { validatePlayerInput } from './validation.js'
import { RateLimiter } from './rate-limiter.js'
import { generateId, generateSessionToken } from '../utils/id.js'
import { authMiddleware } from '../auth/middleware.js'
import { inputReconciliation } from '../game/reconciliation.js'
import pino from 'pino'

const logger = pino({ name: 'socket-handler' })

export class SocketHandler {
  private io: SocketIOServer
  private gameLoopManager: GameLoopManager
  private rateLimiter: RateLimiter
  private connectedPlayers = new Map<string, ConnectedPlayer>()
  private socketToPlayer = new Map<string, string>() // socketId -> playerId

  constructor(io: SocketIOServer, gameLoopManager: GameLoopManager) {
    this.io = io
    this.gameLoopManager = gameLoopManager
    this.rateLimiter = new RateLimiter()
    
    this.setupSocketHandlers()
    this.startBroadcastLoop()
  }

  private setupSocketHandlers(): void {
    // Apply authentication middleware
    this.io.use(authMiddleware.authenticateSocket)
    this.io.use(authMiddleware.canJoinGame)
    this.io.use(authMiddleware.validatePlayerInput)

    this.io.on('connection', (socket: Socket) => {
      logger.info(`Client connected: ${socket.id}`)

      // Set up event handlers for this socket
      socket.on('join_game', (data) => this.handleJoinGame(socket, data))
      socket.on('player_input', (data) => this.handlePlayerInput(socket, data))
      socket.on('client_state', (data) => this.handleClientState(socket, data))
      socket.on('leave_game', () => this.handleLeaveGame(socket))
      socket.on('disconnect', () => this.handleDisconnect(socket))
      socket.on('ping', () => this.handlePing(socket))

      // Rate limiting setup
      this.rateLimiter.addClient(socket.id)
    })
  }

  private async handleJoinGame(socket: Socket, data: JoinGameData): Promise<void> {
    try {
      // Get authenticated user data from socket
      const userData = socket.data.user
      const sessionData = socket.data.session

      if (!userData || !sessionData) {
        socket.emit('join_error', { error: 'Authentication required' })
        return
      }

      // Check rate limiting
      if (!this.rateLimiter.checkLimit(socket.id, 'join_game')) {
        socket.emit('join_error', { error: 'Rate limit exceeded' })
        return
      }

      // Use authenticated data instead of request data
      const walletAddress = userData.walletAddress
      const wagerAmount = sessionData.wagerAmount

      // Determine lobby tier based on wager amount
      const lobbyTier = this.determineLobbyTier(wagerAmount)
      
      // Get or create game loop for this lobby tier
      const gameLoop = this.gameLoopManager.getLobby(lobbyTier) || 
                      this.gameLoopManager.createLobby(lobbyTier, LOBBY_TIERS[lobbyTier])

      // Create player object using authenticated data
      const player: Player = {
        id: generateId(),
        sessionToken: sessionData.sessionToken,
        socketId: socket.id,
        wallet: walletAddress,
        username: data?.username || `Player_${Date.now()}`,
        cells: [],
        totalMass: 0,
        score: 0,
        targetPosition: { x: 0, y: 0 },
        lastInput: Date.now(),
        inputCount: 0,
        wagerAmount: wagerAmount,
        currentValue: wagerAmount,
        initialMass: this.calculateInitialMass(wagerAmount),
        isAlive: false,
        canCashout: false,
        suspiciousActivity: false,
        lastValidatedPosition: { x: 0, y: 0 },
        lastValidatedTime: Date.now(),
      }

      // Add player to game
      gameLoop.getGameState().addPlayer(player)
      
      // Track connection
      this.connectedPlayers.set(player.id, {
        player,
        socket,
        lobbyTier,
        lastPing: Date.now(),
      })
      this.socketToPlayer.set(socket.id, player.id)

      // Join socket to lobby room
      socket.join(`lobby_${lobbyTier}`)

      // Send join success response
      socket.emit('join_success', {
        playerId: player.id,
        sessionToken: player.sessionToken,
        lobbyTier,
        initialMass: player.initialMass,
      })

      logger.info(`Player ${player.id} joined lobby ${lobbyTier}`)

    } catch (error) {
      logger.error(error, 'Error handling join game')
      socket.emit('join_error', { error: 'Internal server error' })
    }
  }

  private handlePlayerInput(socket: Socket, data: any): void {
    const playerId = this.socketToPlayer.get(socket.id)
    if (!playerId) return

    const connectedPlayer = this.connectedPlayers.get(playerId)
    if (!connectedPlayer) return

    // Check rate limiting
    if (!this.rateLimiter.checkLimit(socket.id, 'player_input')) {
      return // Silently drop input if rate limited
    }

    // Validate input
    const validatedInput = validatePlayerInput(data, connectedPlayer.player)
    if (!validatedInput) {
      logger.warn(`Invalid input from player ${playerId}`)
      return
    }

    // Get game loop for this player's lobby
    const gameLoop = this.gameLoopManager.getLobby(connectedPlayer.lobbyTier)
    if (!gameLoop) return

    // Process input
    gameLoop.processInput(validatedInput)
  }

  private handleLeaveGame(socket: Socket): void {
    const playerId = this.socketToPlayer.get(socket.id)
    if (!playerId) return

    this.removePlayer(playerId, 'left')
  }

  private handleDisconnect(socket: Socket): void {
    const playerId = this.socketToPlayer.get(socket.id)
    if (!playerId) return

    logger.info(`Client disconnected: ${socket.id}`)
    this.removePlayer(playerId, 'disconnected')
    this.rateLimiter.removeClient(socket.id)
  }

  private handlePing(socket: Socket): void {
    const playerId = this.socketToPlayer.get(socket.id)
    if (playerId) {
      const connectedPlayer = this.connectedPlayers.get(playerId)
      if (connectedPlayer) {
        connectedPlayer.lastPing = Date.now()
      }
    }
    
    socket.emit('pong', { timestamp: Date.now() })
  }

  private removePlayer(playerId: string, reason: string): void {
    const connectedPlayer = this.connectedPlayers.get(playerId)
    if (!connectedPlayer) return

    // Remove from game
    const gameLoop = this.gameLoopManager.getLobby(connectedPlayer.lobbyTier)
    if (gameLoop) {
      gameLoop.getGameState().removePlayer(playerId, reason)
    }

    // Clean up tracking
    this.connectedPlayers.delete(playerId)
    this.socketToPlayer.delete(connectedPlayer.socket.id)

    logger.info(`Player ${playerId} removed: ${reason}`)
  }

  // Handle client state for reconciliation
  private async handleClientState(socket: Socket, data: ClientStateData): Promise<void> {
    try {
      const connectedPlayer = this.socketToPlayer.get(socket.id)
      if (!connectedPlayer) {
        return
      }

      const player = connectedPlayer.player
      if (!player || !player.isAlive) {
        return
      }

      // Check rate limiting
      if (!this.rateLimiter.checkLimit(socket.id, 'client_state')) {
        return
      }

      // Validate client state data
      if (!this.validateClientStateData(data)) {
        logger.warn(`Invalid client state data from player ${player.id}`)
        return
      }

      // Perform reconciliation check
      const reconciliationData = inputReconciliation.reconcilePlayerState(
        player.id,
        data.tick,
        data.position
      )

      if (reconciliationData?.correctionNeeded) {
        // Send position correction to client
        socket.emit('position_correction', {
          serverPosition: reconciliationData.serverPosition,
          serverTick: reconciliationData.serverTick,
          difference: reconciliationData.positionDifference,
        })

        logger.debug(`Position correction sent to player ${player.id}: diff=${reconciliationData.positionDifference}`)
      }

    } catch (error) {
      logger.error(error, `Error handling client state for socket ${socket.id}`)
    }
  }

  private validateClientStateData(data: ClientStateData): boolean {
    return (
      typeof data.tick === 'number' &&
      typeof data.position === 'object' &&
      typeof data.position.x === 'number' &&
      typeof data.position.y === 'number' &&
      !isNaN(data.position.x) &&
      !isNaN(data.position.y) &&
      isFinite(data.position.x) &&
      isFinite(data.position.y)
    )
  }

  private startBroadcastLoop(): void {
    // Broadcast game state updates at 20 FPS
    setInterval(() => {
      this.broadcastGameUpdates()
    }, 1000 / 20)
  }

  private broadcastGameUpdates(): void {
    // Broadcast updates for each lobby
    for (const [lobbyTier, gameLoop] of this.gameLoopManager.getAllLobbies()) {
      this.broadcastLobbyUpdate(lobbyTier, gameLoop)
    }
  }

  private broadcastLobbyUpdate(lobbyTier: number, gameLoop: any): void {
    const gameState = gameLoop.getGameState()
    const world = gameState.getWorld()
    const events = gameState.getEvents()

    // Get all players in this lobby
    const lobbyPlayers = Array.from(this.connectedPlayers.values())
      .filter(cp => cp.lobbyTier === lobbyTier)

    if (lobbyPlayers.length === 0) return

    // Create game snapshot for each player (with their view)
    for (const connectedPlayer of lobbyPlayers) {
      const player = connectedPlayer.player
      if (!player.isAlive) continue

      const snapshot = this.createPlayerSnapshot(player, world, events)
      connectedPlayer.socket.emit('game_update', snapshot)
    }
  }

  private createPlayerSnapshot(player: Player, world: any, events: any[]): GameSnapshot {
    // Calculate player's view area
    const viewRadius = 1000 // This would be calculated based on player's mass
    const playerCenter = player.cells.length > 0 ? player.cells[0].position : { x: 0, y: 0 }
    
    const viewBounds = {
      x: playerCenter.x - viewRadius,
      y: playerCenter.y - viewRadius,
      width: viewRadius * 2,
      height: viewRadius * 2,
    }

    // Get entities in view
    const entitiesInView = world.quadTree.retrieve(viewBounds)
    
    // Filter and format entities for client
    const visiblePlayers = []
    const visiblePellets = []
    const visibleViruses = []
    const visibleEjectedMass = []

    // This would be implemented to filter entities and create the snapshot
    // For now, return a basic snapshot
    
    return {
      timestamp: Date.now(),
      tick: world.tickCount,
      players: visiblePlayers,
      pellets: visiblePellets,
      viruses: visibleViruses,
      ejectedMass: visibleEjectedMass,
      playerData: {
        totalMass: player.totalMass,
        score: player.score,
        currentValue: player.currentValue,
        canCashout: player.canCashout,
      },
      lobbyStats: {
        totalPlayers: world.totalPlayers,
        totalValue: world.totalValue,
      },
    }
  }

  // Helper methods
  private validateJoinGameData(data: any): data is JoinGameData {
    return data && 
           typeof data.wallet === 'string' &&
           typeof data.signature === 'string' &&
           typeof data.wagerAmount === 'number' &&
           data.wagerAmount > 0
  }

  private validateWalletSignature(wallet: string, signature: string): boolean {
    // TODO: Implement proper wallet signature validation
    return true
  }

  private determineLobbyTier(wagerAmount: number): number {
    // Determine lobby tier based on wager amount
    for (let tier = 0; tier < LOBBY_TIERS.length; tier++) {
      if (wagerAmount >= LOBBY_TIERS[tier].minWager) {
        return tier
      }
    }
    return 0 // Default to lowest tier
  }

  private calculateInitialMass(wagerAmount: number): number {
    // Calculate initial mass based on wager amount
    return Math.max(100, wagerAmount * 10) // Simple formula
  }
}

// Types
interface ConnectedPlayer {
  player: Player
  socket: Socket
  lobbyTier: number
  lastPing: number
}

interface JoinGameData {
  wallet: string
  signature: string
  wagerAmount: number
  username?: string
}

interface ClientStateData {
  tick: number
  position: {
    x: number
    y: number
  }
  timestamp: number
}
