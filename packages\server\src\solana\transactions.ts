import { 
  Connection, 
  PublicKey, 
  Transaction, 
  SystemProgram, 
  LAMPORTS_PER_SOL,
  TransactionInstruction,
  Keypair,
  sendAndConfirmTransaction
} from '@solana/web3.js'
import { solanaClient } from './client.js'
import { oracleService } from './oracle.js'
import { validateEnv } from '../config/env.js'
import pino from 'pino'

const logger = pino({ name: 'transaction-builder' })
const env = validateEnv()

export class TransactionBuilder {
  private connection: Connection
  private programId: PublicKey

  constructor() {
    this.connection = solanaClient['connection'] // Access private connection
    this.programId = new PublicKey(env.PROGRAM_ID)
  }

  // Create a game session transaction
  async createGameSessionTransaction(
    playerWallet: PublicKey,
    wagerAmount: number,
    sessionId: string
  ): Promise<Transaction> {
    try {
      const transaction = new Transaction()
      
      // Get recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash()
      transaction.recentBlockhash = blockhash
      transaction.feePayer = playerWallet

      // TODO: Add actual program instruction for creating game session
      // For now, create a placeholder instruction
      const instruction = new TransactionInstruction({
        keys: [
          { pubkey: playerWallet, isSigner: true, isWritable: true },
          { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
        ],
        programId: this.programId,
        data: Buffer.from(JSON.stringify({
          instruction: 'create_session',
          sessionId,
          wagerAmount,
          timestamp: Date.now(),
        })),
      })

      transaction.add(instruction)
      
      logger.info(`Created game session transaction for ${playerWallet.toString()}`)
      return transaction

    } catch (error) {
      logger.error(error, 'Failed to create game session transaction')
      throw error
    }
  }

  // Create a cashout transaction
  async createCashoutTransaction(
    playerWallet: PublicKey,
    sessionId: string,
    finalAmount: number,
    serverWallet: PublicKey
  ): Promise<Transaction> {
    try {
      const transaction = new Transaction()
      
      // Get recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash()
      transaction.recentBlockhash = blockhash
      transaction.feePayer = serverWallet // Server pays for cashout transaction

      // Calculate lamports
      const lamports = Math.floor(finalAmount * LAMPORTS_PER_SOL)

      // Create transfer instruction
      const transferInstruction = SystemProgram.transfer({
        fromPubkey: serverWallet,
        toPubkey: playerWallet,
        lamports,
      })

      transaction.add(transferInstruction)

      // TODO: Add program instruction to close session
      const closeSessionInstruction = new TransactionInstruction({
        keys: [
          { pubkey: playerWallet, isSigner: false, isWritable: true },
          { pubkey: serverWallet, isSigner: true, isWritable: true },
          { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
        ],
        programId: this.programId,
        data: Buffer.from(JSON.stringify({
          instruction: 'close_session',
          sessionId,
          finalAmount,
          timestamp: Date.now(),
        })),
      })

      transaction.add(closeSessionInstruction)
      
      logger.info(`Created cashout transaction: ${finalAmount} SOL to ${playerWallet.toString()}`)
      return transaction

    } catch (error) {
      logger.error(error, 'Failed to create cashout transaction')
      throw error
    }
  }

  // Create a refund transaction (for failed games)
  async createRefundTransaction(
    playerWallet: PublicKey,
    sessionId: string,
    refundAmount: number,
    serverWallet: PublicKey
  ): Promise<Transaction> {
    try {
      const transaction = new Transaction()
      
      // Get recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash()
      transaction.recentBlockhash = blockhash
      transaction.feePayer = serverWallet

      // Calculate lamports
      const lamports = Math.floor(refundAmount * LAMPORTS_PER_SOL)

      // Create transfer instruction
      const transferInstruction = SystemProgram.transfer({
        fromPubkey: serverWallet,
        toPubkey: playerWallet,
        lamports,
      })

      transaction.add(transferInstruction)

      // Add refund instruction
      const refundInstruction = new TransactionInstruction({
        keys: [
          { pubkey: playerWallet, isSigner: false, isWritable: true },
          { pubkey: serverWallet, isSigner: true, isWritable: true },
        ],
        programId: this.programId,
        data: Buffer.from(JSON.stringify({
          instruction: 'refund',
          sessionId,
          refundAmount,
          reason: 'game_failed',
          timestamp: Date.now(),
        })),
      })

      transaction.add(refundInstruction)
      
      logger.info(`Created refund transaction: ${refundAmount} SOL to ${playerWallet.toString()}`)
      return transaction

    } catch (error) {
      logger.error(error, 'Failed to create refund transaction')
      throw error
    }
  }

  // Estimate transaction fee
  async estimateTransactionFee(transaction: Transaction): Promise<number> {
    try {
      const feeCalculator = await this.connection.getFeeForMessage(
        transaction.compileMessage()
      )
      
      return (feeCalculator.value || 5000) / LAMPORTS_PER_SOL // Convert to SOL
      
    } catch (error) {
      logger.error(error, 'Failed to estimate transaction fee')
      return 0.005 // Default fee estimate
    }
  }

  // Validate transaction before sending
  async validateTransaction(transaction: Transaction): Promise<ValidationResult> {
    try {
      const result: ValidationResult = {
        isValid: true,
        errors: [],
        warnings: [],
      }

      // Check if transaction has instructions
      if (transaction.instructions.length === 0) {
        result.isValid = false
        result.errors.push('Transaction has no instructions')
      }

      // Check if fee payer is set
      if (!transaction.feePayer) {
        result.isValid = false
        result.errors.push('Transaction fee payer not set')
      }

      // Check if recent blockhash is set
      if (!transaction.recentBlockhash) {
        result.isValid = false
        result.errors.push('Transaction recent blockhash not set')
      }

      // Simulate transaction
      try {
        const simulation = await this.connection.simulateTransaction(transaction)
        
        if (simulation.value.err) {
          result.isValid = false
          result.errors.push(`Transaction simulation failed: ${JSON.stringify(simulation.value.err)}`)
        }

        if (simulation.value.logs) {
          // Check for common error patterns in logs
          const errorLogs = simulation.value.logs.filter(log => 
            log.includes('Error') || log.includes('failed')
          )
          
          if (errorLogs.length > 0) {
            result.warnings.push(`Potential issues in logs: ${errorLogs.join(', ')}`)
          }
        }

      } catch (simError) {
        result.warnings.push(`Could not simulate transaction: ${simError}`)
      }

      return result

    } catch (error) {
      logger.error(error, 'Failed to validate transaction')
      return {
        isValid: false,
        errors: ['Validation failed'],
        warnings: [],
      }
    }
  }

  // Send and confirm transaction with retries
  async sendAndConfirmTransactionWithRetry(
    transaction: Transaction,
    signers: Keypair[],
    maxRetries: number = 3
  ): Promise<string> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`Sending transaction (attempt ${attempt}/${maxRetries})`)
        
        // Get fresh blockhash for retry attempts
        if (attempt > 1) {
          const { blockhash } = await this.connection.getLatestBlockhash()
          transaction.recentBlockhash = blockhash
        }

        const signature = await sendAndConfirmTransaction(
          this.connection,
          transaction,
          signers,
          {
            commitment: 'confirmed',
            preflightCommitment: 'confirmed',
            maxRetries: 0, // We handle retries ourselves
          }
        )

        logger.info(`Transaction confirmed: ${signature}`)
        return signature

      } catch (error) {
        lastError = error as Error
        logger.warn(`Transaction attempt ${attempt} failed: ${error}`)
        
        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError || new Error('Transaction failed after all retries')
  }

  // Get transaction status
  async getTransactionStatus(signature: string): Promise<TransactionStatus> {
    try {
      const status = await this.connection.getSignatureStatus(signature)
      
      if (!status.value) {
        return { status: 'not_found', confirmations: 0 }
      }

      return {
        status: status.value.confirmationStatus || 'unknown',
        confirmations: status.value.confirmations || 0,
        error: status.value.err ? JSON.stringify(status.value.err) : undefined,
      }

    } catch (error) {
      logger.error(error, `Failed to get transaction status for ${signature}`)
      return { status: 'error', confirmations: 0 }
    }
  }

  // Calculate optimal transaction fee based on network conditions
  async calculateOptimalFee(): Promise<number> {
    try {
      // Get recent fee samples
      const feeCalculator = await this.connection.getRecentBlockhash()
      
      // TODO: Implement more sophisticated fee calculation
      // For now, return a conservative estimate
      return 0.005 // 0.005 SOL

    } catch (error) {
      logger.error(error, 'Failed to calculate optimal fee')
      return 0.01 // Fallback to higher fee
    }
  }
}

// Transaction queue for managing multiple transactions
export class TransactionQueue {
  private queue: QueuedTransaction[] = []
  private processing = false
  private maxConcurrent = 3

  async addTransaction(
    transaction: Transaction,
    signers: Keypair[],
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const queuedTx: QueuedTransaction = {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        transaction,
        signers,
        priority,
        resolve,
        reject,
        timestamp: Date.now(),
      }

      // Insert based on priority
      if (priority === 'high') {
        this.queue.unshift(queuedTx)
      } else {
        this.queue.push(queuedTx)
      }

      this.processQueue()
    })
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return
    }

    this.processing = true
    const builder = new TransactionBuilder()

    try {
      const concurrent = Math.min(this.maxConcurrent, this.queue.length)
      const batch = this.queue.splice(0, concurrent)

      const promises = batch.map(async (queuedTx) => {
        try {
          const signature = await builder.sendAndConfirmTransactionWithRetry(
            queuedTx.transaction,
            queuedTx.signers
          )
          queuedTx.resolve(signature)
        } catch (error) {
          queuedTx.reject(error)
        }
      })

      await Promise.all(promises)

    } catch (error) {
      logger.error(error, 'Error processing transaction queue')
    } finally {
      this.processing = false
      
      // Continue processing if there are more transactions
      if (this.queue.length > 0) {
        setTimeout(() => this.processQueue(), 100)
      }
    }
  }

  getQueueStatus(): {
    pending: number
    processing: boolean
  } {
    return {
      pending: this.queue.length,
      processing: this.processing,
    }
  }
}

// Types
interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

interface TransactionStatus {
  status: string
  confirmations: number
  error?: string
}

interface QueuedTransaction {
  id: string
  transaction: Transaction
  signers: Keypair[]
  priority: 'low' | 'normal' | 'high'
  resolve: (signature: string) => void
  reject: (error: Error) => void
  timestamp: number
}

// Singleton instances
export const transactionBuilder = new TransactionBuilder()
export const transactionQueue = new TransactionQueue()
