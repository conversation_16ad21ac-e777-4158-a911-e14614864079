# Agar.io Solana Clone

A modern implementation of the classic Agar.io game with Solana blockchain integration, featuring real-money wagering, oracle-based SOL conversion, and server-verified cash-out mechanisms.

## 🎮 Game Features

- **Classic Agar.io Gameplay**: Eat pellets, grow your cell, consume other players
- **Solana Integration**: Wager SOL tokens and cash out your winnings
- **Real-time Multiplayer**: Authoritative server with anti-cheat protection
- **Dynamic Pricing**: Pyth oracle integration for SOL/USD conversion
- **Multiple Wager Tiers**: $1 and $10 USD equivalent lobbies
- **Continuous Lobbies**: Always-on game world with persistent state

## 🏗️ Architecture

This project uses a monorepo structure with the following packages:

- **`packages/client/`**: Frontend application (Vite + TypeScript + Canvas)
- **`packages/server/`**: Authoritative game server (Node.js + Socket.IO)
- **`packages/anchor-program/`**: Solana smart contracts (Anchor framework)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm 8+
- Docker and Docker Compose
- Solana CLI tools
- Anchor CLI (for smart contract development)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd agario-solana-clone
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development services**

   ```bash
   # Start PostgreSQL and Redis
   docker-compose up -d postgres redis

   # Start the development servers
   pnpm dev
   ```

This will start:

- Client development server on `http://localhost:3000`
- Backend server on `http://localhost:8080`
- PostgreSQL on `localhost:5432`
- Redis on `localhost:6379`

### Development Workflow

```bash
# Run all packages in development mode
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Lint and format code
pnpm lint
pnpm format

# Clean all build artifacts
pnpm clean
```

## 📦 Package Scripts

### Root Level

- `pnpm dev` - Start all packages in development mode
- `pnpm build` - Build all packages
- `pnpm test` - Run tests across all packages
- `pnpm lint` - Lint all packages
- `pnpm format` - Format code with Prettier

### Client (`packages/client/`)

- `pnpm dev` - Start Vite development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build

### Server (`packages/server/`)

- `pnpm dev` - Start development server with hot reload
- `pnpm build` - Compile TypeScript
- `pnpm start` - Start production server

### Anchor Program (`packages/anchor-program/`)

- `pnpm build` - Build Anchor program
- `pnpm test` - Run Anchor tests
- `pnpm deploy` - Deploy to configured cluster

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for full list):

```bash
# Solana Configuration
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Server Configuration
PORT=8080
DATABASE_URL=postgresql://postgres:password@localhost:5432/agario_solana
REDIS_URL=redis://localhost:6379

# Game Configuration
PLATFORM_FEE_BPS=500
ORACLE_SAFETY_MARGIN_BPS=50
CASHOUT_HOLD_MS=5000
```

### Docker Development

The project includes Docker configuration for easy development:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🎯 Development Phases

### ✅ Phase 1: Setup & Configuration (COMPLETE)

- [x] Monorepo structure with pnpm workspaces
- [x] TypeScript, ESLint, Prettier configuration
- [x] Docker development environment
- [x] Basic client and server scaffolding
- [x] Anchor program foundation

### 🚧 Phase 2: Solana Program (IN PROGRESS)

- [ ] Complete PDA definitions and instructions
- [ ] Pyth oracle integration
- [ ] Unit tests and IDL generation

### 📋 Phase 3: Backend Server

- [ ] Game loop and physics engine
- [ ] Socket.IO real-time communication
- [ ] Database schema and migrations
- [ ] Anti-cheat and input validation

### 📋 Phase 4: Client Application

- [ ] Canvas rendering engine
- [ ] Wallet integration
- [ ] Game UI and HUD
- [ ] Real-time networking

### 📋 Phase 5: Integration & Testing

- [ ] End-to-end game flow
- [ ] Load testing and optimization
- [ ] Security audits

## 🛠️ Tech Stack

### Frontend

- **Vite** - Build tool and development server
- **TypeScript** - Type-safe JavaScript
- **HTML5 Canvas** - Game rendering
- **Socket.IO Client** - Real-time communication
- **Solana Web3.js** - Blockchain interaction

### Backend

- **Node.js** - Runtime environment
- **Express** - Web framework
- **Socket.IO** - Real-time bidirectional communication
- **PostgreSQL** - Primary database
- **Redis** - Caching and pub/sub
- **Prisma** - Database ORM

### Blockchain

- **Anchor** - Solana smart contract framework
- **Pyth Network** - Price oracle integration
- **Solana Web3.js** - Blockchain interaction

### DevOps

- **Docker** - Containerization
- **pnpm** - Package management
- **ESLint + Prettier** - Code quality
- **Husky** - Git hooks

## 📚 Documentation

- [Architecture Overview](docs/architecture.md) _(Coming Soon)_
- [Game Design Document](docs/whitepaper.md) _(Coming Soon)_
- [API Documentation](docs/api.md) _(Coming Soon)_
- [Deployment Guide](docs/deployment.md) _(Coming Soon)_

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This project is for educational and demonstration purposes. Ensure compliance with local gambling and financial regulations before deploying to production.

---

**Current Status**: Phase 1 Complete - Foundation established, ready for Phase 2 development!
