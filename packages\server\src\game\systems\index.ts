import { GameState } from '../state.js'
import { PlayerInput } from '../types.js'
import { MovementSystem } from './movement.js'
import { CollisionSystem } from './collision.js'
import { DecaySystem } from './decay.js'
import { PelletSystem } from './pellet.js'
import { VirusSystem } from './virus.js'
import { SplitMergeSystem } from './split-merge.js'
import { EjectSystem } from './eject.js'
import { CashoutSystem } from './cashout.js'
import { inputReconciliation } from '../reconciliation.js'
import pino from 'pino'

const logger = pino({ name: 'game-systems' })

export class GameSystems {
  private gameState: GameState
  private movementSystem: MovementSystem
  private collisionSystem: CollisionSystem
  private decaySystem: DecaySystem
  private pelletSystem: PelletSystem
  private virusSystem: VirusSystem
  private splitMergeSystem: SplitMergeSystem
  private ejectSystem: EjectSystem
  private cashoutSystem: CashoutSystem

  constructor(gameState: GameState) {
    this.gameState = gameState
    
    // Initialize all systems
    this.movementSystem = new MovementSystem(gameState)
    this.collisionSystem = new CollisionSystem(gameState)
    this.decaySystem = new DecaySystem(gameState)
    this.pelletSystem = new PelletSystem(gameState)
    this.virusSystem = new VirusSystem(gameState)
    this.splitMergeSystem = new SplitMergeSystem(gameState)
    this.ejectSystem = new EjectSystem(gameState)
    this.cashoutSystem = new CashoutSystem(gameState)

    logger.info('Game systems initialized')
  }

  // Update all systems
  update(deltaTime: number): void {
    // Update systems in order of dependency
    
    // 1. Process movement and physics
    this.movementSystem.update(deltaTime)
    
    // 2. Handle split/merge mechanics
    this.splitMergeSystem.update(deltaTime)
    
    // 3. Process collisions and consumption
    this.collisionSystem.update(deltaTime)
    
    // 4. Apply decay to player cells
    this.decaySystem.update(deltaTime)
    
    // 5. Manage pellets (respawning, cleanup)
    this.pelletSystem.update(deltaTime)
    
    // 6. Manage viruses (feeding, popping)
    this.virusSystem.update(deltaTime)
    
    // 7. Handle eject mechanics
    this.ejectSystem.update(deltaTime)
    
    // 8. Process cashout requests
    this.cashoutSystem.update(deltaTime)
  }

  // Process player input with reconciliation
  processInput(input: PlayerInput): boolean {
    try {
      const player = this.gameState.getPlayer(input.playerId)
      if (!player || !player.isAlive) {
        return false
      }

      // Validate session token
      if (player.sessionToken !== input.sessionToken) {
        logger.warn(`Invalid session token for player ${input.playerId}`)
        return false
      }

      // Process input through reconciliation system
      const reconciliationResult = inputReconciliation.processPlayerInput(player, input)

      if (!reconciliationResult.success) {
        logger.warn(`Input validation failed for player ${input.playerId}: ${reconciliationResult.error}`)

        // Disconnect player if severe violation
        if (reconciliationResult.shouldDisconnect) {
          this.gameState.removePlayer(input.playerId, 'anti_cheat_violation')
        }

        return false
      }

      // Use the processed input from reconciliation
      const processedInput = reconciliationResult.processedInput || input

      // Process movement input
      if (processedInput.targetX !== undefined && processedInput.targetY !== undefined) {
        this.movementSystem.processMovementInput(processedInput)
      }

      // Process action inputs
      if (processedInput.split) {
        this.splitMergeSystem.processSplitInput(processedInput)
      }

      if (processedInput.eject) {
        this.ejectSystem.processEjectInput(processedInput)
      }

      if (processedInput.cashout) {
        this.cashoutSystem.processCashoutInput(processedInput)
      }

      return true
    } catch (error) {
      logger.error(error, `Error processing input for player ${input.playerId}`)
      return false
    }
  }

  // Get collision check count for performance metrics
  getCollisionChecks(): number {
    return this.collisionSystem.getCollisionChecks()
  }

  // Get individual systems for direct access if needed
  getMovementSystem(): MovementSystem {
    return this.movementSystem
  }

  getCollisionSystem(): CollisionSystem {
    return this.collisionSystem
  }

  getDecaySystem(): DecaySystem {
    return this.decaySystem
  }

  getPelletSystem(): PelletSystem {
    return this.pelletSystem
  }

  getVirusSystem(): VirusSystem {
    return this.virusSystem
  }

  getSplitMergeSystem(): SplitMergeSystem {
    return this.splitMergeSystem
  }

  getEjectSystem(): EjectSystem {
    return this.ejectSystem
  }

  getCashoutSystem(): CashoutSystem {
    return this.cashoutSystem
  }
}
