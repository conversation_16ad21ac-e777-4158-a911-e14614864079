import { Button } from './Button'
import { Panel } from './Panel'

export interface UsernameFormOptions {
  onSubmit: (username: string) => void
  onCancel: () => void
  initialUsername?: string
}

export class UsernameForm {
  private element: HTMLElement
  private panel: Panel
  private usernameInput: HTMLInputElement
  private submitButton: Button
  private cancelButton: Button
  private options: UsernameFormOptions

  constructor(options: UsernameFormOptions) {
    this.options = options
    this.createElement()
    this.setupEventListeners()
  }

  private createElement(): void {
    this.element = document.createElement('div')
    this.element.className = 'username-form-overlay'
    
    this.panel = new Panel({
      title: 'Enter Username',
      className: 'username-form-panel'
    })

    // Create form content
    const formContent = document.createElement('div')
    formContent.className = 'username-form-content'
    
    // Username input
    const inputGroup = document.createElement('div')
    inputGroup.className = 'input-group'
    
    const label = document.createElement('label')
    label.textContent = 'Username'
    label.htmlFor = 'username-input'
    label.className = 'input-label'
    
    this.usernameInput = document.createElement('input')
    this.usernameInput.type = 'text'
    this.usernameInput.id = 'username-input'
    this.usernameInput.className = 'username-input'
    this.usernameInput.placeholder = 'Enter your username...'
    this.usernameInput.maxLength = 20
    this.usernameInput.value = this.options.initialUsername || ''
    
    const helpText = document.createElement('div')
    helpText.className = 'input-help'
    helpText.textContent = 'Max 20 characters. Letters, numbers, and underscores only.'
    
    inputGroup.appendChild(label)
    inputGroup.appendChild(this.usernameInput)
    inputGroup.appendChild(helpText)
    
    // Buttons
    const buttonGroup = document.createElement('div')
    buttonGroup.className = 'button-group'
    
    this.cancelButton = new Button({
      text: 'Cancel',
      variant: 'secondary',
      onClick: this.handleCancel
    })
    
    this.submitButton = new Button({
      text: 'Continue',
      variant: 'primary',
      onClick: this.handleSubmit
    })
    
    buttonGroup.appendChild(this.cancelButton.getElement())
    buttonGroup.appendChild(this.submitButton.getElement())
    
    formContent.appendChild(inputGroup)
    formContent.appendChild(buttonGroup)
    
    this.panel.setContent(formContent)
    this.element.appendChild(this.panel.getElement())
    
    this.addStyles()
  }

  private addStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .username-form-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(11, 15, 20, 0.8);
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 1rem;
      }
      
      .username-form-panel {
        width: 100%;
        max-width: 400px;
        animation: slideIn 0.3s ease-out;
      }
      
      .username-form-content {
        padding: 1.5rem;
      }
      
      .input-group {
        margin-bottom: 1.5rem;
      }
      
      .input-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
      }
      
      .username-input {
        width: 100%;
        padding: 0.75rem;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        color: var(--text-primary);
        font-size: 1rem;
        transition: all 0.2s ease;
        box-sizing: border-box;
      }
      
      .username-input:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
      
      .username-input:invalid {
        border-color: #ef4444;
      }
      
      .input-help {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
      }
      
      .button-group {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
      }
      
      .button-group button {
        min-width: 80px;
      }
      
      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @media (max-width: 480px) {
        .username-form-content {
          padding: 1rem;
        }
        
        .button-group {
          flex-direction: column;
        }
        
        .button-group button {
          width: 100%;
        }
      }
    `
    document.head.appendChild(style)
  }

  private setupEventListeners(): void {
    // Enter key to submit
    this.usernameInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        this.handleSubmit()
      }
    })
    
    // Input validation
    this.usernameInput.addEventListener('input', () => {
      this.validateInput()
    })
    
    // Focus input when shown
    setTimeout(() => {
      this.usernameInput.focus()
    }, 100)
  }

  private validateInput(): boolean {
    const username = this.usernameInput.value.trim()
    const isValid = /^[a-zA-Z0-9_]{1,20}$/.test(username)
    
    this.submitButton.setDisabled(!isValid)
    
    return isValid
  }

  private handleSubmit = (): void => {
    const username = this.usernameInput.value.trim()
    
    if (this.validateInput()) {
      this.options.onSubmit(username)
    }
  }

  private handleCancel = (): void => {
    this.options.onCancel()
  }

  public show(): void {
    document.body.appendChild(this.element)
    setTimeout(() => {
      this.usernameInput.focus()
    }, 100)
  }

  public hide(): void {
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
  }

  public destroy(): void {
    this.hide()
    this.panel.destroy()
    this.submitButton.destroy()
    this.cancelButton.destroy()
  }

  public getElement(): HTMLElement {
    return this.element
  }
}
