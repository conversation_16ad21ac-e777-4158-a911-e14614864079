import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';
import { logger, setupGracefulShutdown, metrics } from './utils/logger.js';
import { gameLoopManager } from './game/loop.js';
import { SocketHandler } from './network/socket-handler.js';
import { ipRateLimiter } from './network/rate-limiter.js';
import { validateEnv } from './config/env';
import { LOBBY_TIERS } from './config/lobby.js';
import { solanaClient } from './solana/client.js';
import { oracleService } from './solana/oracle.js';
import authRoutes from './routes/auth.js';
// Load environment variables
dotenv.config();
// Validate environment configuration
const env = validateEnv();
// Setup graceful shutdown logging
setupGracefulShutdown();
// Initialize Express app
const app = express();
const server = createServer(app);
// Initialize Socket.IO
const io = new SocketIOServer(server, {
    cors: {
        origin: env.CORS_ORIGIN,
        methods: ['GET', 'POST'],
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
});
// Middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
}));
app.use(cors({
    origin: env.CORS_ORIGIN,
    credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: env.NODE_ENV,
    });
});
// API routes
app.get('/api/status', (req, res) => {
    res.json({
        message: 'Agar.io Solana Server is running!',
        version: '0.1.0',
        phase: 'Phase 3: Backend Server Complete',
    });
});
// Authentication routes
app.use('/api/auth', authRoutes);
// Game info routes
app.get('/api/game/info', async (req, res) => {
    try {
        const solPrice = await oracleService.getSOLPrice();
        const connectionInfo = solanaClient.getConnectionInfo();
        res.json({
            solPrice: solPrice.price,
            priceTimestamp: solPrice.timestamp,
            isStale: solPrice.isStale,
            solanaConnection: {
                rpcUrl: connectionInfo.rpcUrl,
                isInitialized: connectionInfo.isInitialized,
            },
            lobbyTiers: LOBBY_TIERS.map((tier, index) => ({
                tier: index,
                minWager: tier.usdAmount,
                maxPlayers: tier.maxPlayers,
            })),
        });
    }
    catch (error) {
        logger.error(error, 'Error getting game info');
        res.status(500).json({ error: 'Failed to get game info' });
    }
});
// Initialize game systems
async function initializeGameSystems() {
    logger.info('Initializing game systems...');
    metrics.startTimer('initialization');
    // Initialize Solana services
    try {
        await solanaClient.initialize();
        await oracleService.initialize();
        logger.info('Solana services initialized successfully');
        metrics.increment('solana_services_initialized');
    }
    catch (error) {
        logger.error(error, 'Failed to initialize Solana services');
        metrics.increment('initialization_errors');
        process.exit(1);
    }
    // Create initial lobby tiers
    for (let tier = 0; tier < LOBBY_TIERS.length; tier++) {
        const lobbyConfig = {
            tier: tier,
            minWager: LOBBY_TIERS[tier].usdAmount,
            maxPlayers: LOBBY_TIERS[tier].maxPlayers,
            worldBounds: { x: 0, y: 0, width: 5000, height: 5000 },
            spawnAreas: [{ x: 100, y: 100, width: 4800, height: 4800 }]
        };
        gameLoopManager.createLobby(tier, lobbyConfig);
        logger.info(`Created lobby tier ${tier} with min wager ${LOBBY_TIERS[tier].usdAmount} USD`);
    }
    // Initialize network layer
    const socketHandler = new SocketHandler(io, gameLoopManager);
    // IP rate limiting middleware
    io.use((socket, next) => {
        const ip = socket.handshake.address;
        if (!ipRateLimiter.checkIPLimit(ip, 'connection')) {
            logger.warn(`Connection rate limit exceeded for IP: ${ip}`);
            next(new Error('Rate limit exceeded'));
            return;
        }
        next();
    });
    const initializationTime = metrics.endTimer('initialization');
    logger.info(`Game systems initialized successfully in ${initializationTime}ms`);
    metrics.increment('successful_initializations');
}
// Initialize systems
initializeGameSystems().catch((error) => {
    logger.error(error, 'Failed to initialize game systems');
    process.exit(1);
});
// Error handling middleware
app.use((err, req, res, next) => {
    logger.error(err, 'Unhandled error');
    res.status(500).json({
        error: 'Internal server error',
        message: env.NODE_ENV === 'development'
            ? err.message
            : 'Something went wrong',
    });
});
// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.method} ${req.path} not found`,
    });
});
// Start server
const PORT = env.PORT;
server.listen(PORT, () => {
    logger.info(`🚀 Agar.io Solana server running on port ${PORT}`);
    logger.info(`📊 Health check: http://localhost:${PORT}/health`);
    logger.info(`🎮 Socket.IO ready for connections`);
    logger.info(`🌍 Environment: ${env.NODE_ENV}`);
    logger.info(`🎯 Game lobbies: ${LOBBY_TIERS.length} tiers initialized`);
    logger.info(`🔒 Rate limiting and anti-cheat systems active`);
});
// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    // Shutdown game systems
    await gameLoopManager.shutdown();
    // Shutdown Solana services
    await solanaClient.cleanup();
    await oracleService.cleanup();
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    // Shutdown game systems
    await gameLoopManager.shutdown();
    // Shutdown Solana services
    await solanaClient.cleanup();
    await oracleService.cleanup();
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
