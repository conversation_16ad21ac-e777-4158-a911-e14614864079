hoistPattern:
  - '*'
hoistedDependencies:
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.28.0:
    '@babel/compat-data': private
  /@babel/core/7.28.0:
    '@babel/core': private
  /@babel/generator/7.28.0:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-globals/7.28.0:
    '@babel/helper-globals': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.28.0):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.28.2:
    '@babel/helpers': private
  /@babel/parser/7.28.0:
    '@babel/parser': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/runtime/7.28.2:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.28.0:
    '@babel/traverse': private
  /@babel/types/7.28.2:
    '@babel/types': private
  /@coral-xyz/anchor/0.28.0:
    '@coral-xyz/anchor': private
  /@coral-xyz/borsh/0.28.0(@solana/web3.js@1.87.6):
    '@coral-xyz/borsh': private
  /@esbuild-kit/cjs-loader/2.4.4:
    '@esbuild-kit/cjs-loader': private
  /@esbuild-kit/core-utils/3.3.2:
    '@esbuild-kit/core-utils': private
  /@esbuild-kit/esm-loader/2.6.5:
    '@esbuild-kit/esm-loader': private
  /@esbuild/android-arm/0.18.20:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.18.20:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.18.20:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.18.20:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.18.20:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.18.20:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.18.20:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.18.20:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.18.20:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.18.20:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.18.20:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.18.20:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.18.20:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.18.20:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.18.20:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.18.20:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-x64/0.18.20:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-x64/0.18.20:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.18.20:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.18.20:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.18.20:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.18.20:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.45.0):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.44.0:
    '@eslint/js': public
  /@humanwhocodes/config-array/0.11.14:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jridgewell/gen-mapping/0.3.12:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/sourcemap-codec/1.5.4:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.29:
    '@jridgewell/trace-mapping': private
  /@noble/curves/1.9.6:
    '@noble/curves': private
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@pkgr/core/0.1.2:
    '@pkgr/core': private
  /@prisma/client/5.2.0(prisma@5.2.0):
    '@prisma/client': private
  /@prisma/engines-version/5.2.0-25.2804dc98259d2ea960602aca6b8e7fdc03c1758f:
    '@prisma/engines-version': private
  /@prisma/engines/5.2.0:
    '@prisma/engines': private
  /@pythnetwork/client/2.19.0(@solana/web3.js@1.87.6):
    '@pythnetwork/client': private
  /@redis/bloom/1.2.0(@redis/client@1.5.9):
    '@redis/bloom': private
  /@redis/client/1.5.9:
    '@redis/client': private
  /@redis/graph/1.1.0(@redis/client@1.5.9):
    '@redis/graph': private
  /@redis/json/1.0.4(@redis/client@1.5.9):
    '@redis/json': private
  /@redis/search/1.1.3(@redis/client@1.5.9):
    '@redis/search': private
  /@redis/time-series/1.0.5(@redis/client@1.5.9):
    '@redis/time-series': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@socket.io/component-emitter/3.1.2:
    '@socket.io/component-emitter': private
  /@solana/buffer-layout/4.0.1:
    '@solana/buffer-layout': private
  /@solana/web3.js/1.87.6:
    '@solana/web3.js': private
  /@types/body-parser/1.19.6:
    '@types/body-parser': private
  /@types/chai-subset/1.3.6(@types/chai@4.3.20):
    '@types/chai-subset': private
  /@types/chai/4.3.20:
    '@types/chai': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookie/0.4.1:
    '@types/cookie': private
  /@types/cors/2.8.13:
    '@types/cors': private
  /@types/express-serve-static-core/4.19.6:
    '@types/express-serve-static-core': private
  /@types/express/4.17.17:
    '@types/express': private
  /@types/http-errors/2.0.5:
    '@types/http-errors': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/jsonwebtoken/9.0.10:
    '@types/jsonwebtoken': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/node/20.5.0:
    '@types/node': private
  /@types/pg/8.10.2:
    '@types/pg': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.5:
    '@types/send': private
  /@types/serve-static/1.15.8:
    '@types/serve-static': private
  /@types/ws/7.4.7:
    '@types/ws': private
  /@typescript-eslint/scope-manager/6.0.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/6.0.0(eslint@8.45.0)(typescript@5.1.6):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.0.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.0.0(typescript@5.1.6):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.0.0(eslint@8.45.0)(typescript@5.1.6):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.0.0:
    '@typescript-eslint/visitor-keys': public
  /@vitejs/plugin-react/4.0.3(vite@4.4.5):
    '@vitejs/plugin-react': private
  /@vitest/expect/0.34.3:
    '@vitest/expect': private
  /@vitest/runner/0.34.3:
    '@vitest/runner': private
  /@vitest/snapshot/0.34.3:
    '@vitest/snapshot': private
  /@vitest/spy/0.34.3:
    '@vitest/spy': private
  /@vitest/utils/0.34.3:
    '@vitest/utils': private
  /abort-controller/3.0.0:
    abort-controller: private
  /accepts/1.3.8:
    accepts: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.15.0:
    acorn: private
  /agentkeepalive/4.6.0:
    agentkeepalive: private
  /aggregate-error/3.1.0:
    aggregate-error: private
  /ajv/6.12.6:
    ajv: private
  /ansi-colors/4.1.1:
    ansi-colors: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /anymatch/3.1.3:
    anymatch: private
  /argparse/2.0.1:
    argparse: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-union/2.1.0:
    array-union: private
  /arrify/1.0.1:
    arrify: private
  /assertion-error/1.1.0:
    assertion-error: private
  /astral-regex/2.0.0:
    astral-regex: private
  /atomic-sleep/1.0.0:
    atomic-sleep: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base-x/5.0.1:
    base-x: private
  /base64-js/1.5.1:
    base64-js: private
  /base64id/2.0.0:
    base64id: private
  /bigint-buffer/1.1.5:
    bigint-buffer: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /bindings/1.5.0:
    bindings: private
  /bn.js/5.2.2:
    bn.js: private
  /body-parser/1.20.1:
    body-parser: private
  /borsh/0.7.0:
    borsh: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browser-stdout/1.3.1:
    browser-stdout: private
  /browserslist/4.25.1:
    browserslist: private
  /bs58/6.0.0:
    bs58: private
  /buffer-equal-constant-time/1.0.1:
    buffer-equal-constant-time: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer-layout/1.2.2:
    buffer-layout: private
  /buffer-writer/2.0.0:
    buffer-writer: private
  /buffer/6.0.3:
    buffer: private
  /bufferutil/4.0.9:
    bufferutil: private
  /bytes/3.1.2:
    bytes: private
  /cac/6.7.14:
    cac: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001731:
    caniuse-lite: private
  /chai/4.3.7:
    chai: private
  /chalk/4.1.2:
    chalk: private
  /check-error/1.0.3:
    check-error: private
  /chokidar/3.5.3:
    chokidar: private
  /clean-stack/2.2.0:
    clean-stack: private
  /cli-cursor/3.1.0:
    cli-cursor: private
  /cli-truncate/3.1.0:
    cli-truncate: private
  /cliui/7.0.4:
    cliui: private
  /cluster-key-slot/1.1.2:
    cluster-key-slot: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /colorette/2.0.20:
    colorette: private
  /commander/10.0.1:
    commander: private
  /concat-map/0.0.1:
    concat-map: private
  /confbox/0.1.8:
    confbox: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.5.0:
    cookie: private
  /cors/2.8.5:
    cors: private
  /cross-fetch/3.2.0:
    cross-fetch: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /crypto-hash/1.3.0:
    crypto-hash: private
  /dateformat/4.6.3:
    dateformat: private
  /debug/4.4.1:
    debug: private
  /decamelize/4.0.0:
    decamelize: private
  /deep-eql/4.1.4:
    deep-eql: private
  /deep-is/0.1.4:
    deep-is: private
  /delay/5.0.0:
    delay: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /diff/5.0.0:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /doctrine/3.0.0:
    doctrine: private
  /dot-case/3.0.4:
    dot-case: private
  /dotenv/16.3.1:
    dotenv: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ecdsa-sig-formatter/1.0.11:
    ecdsa-sig-formatter: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.194:
    electron-to-chromium: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /encodeurl/1.0.2:
    encodeurl: private
  /end-of-stream/1.4.5:
    end-of-stream: private
  /engine.io-client/6.5.4:
    engine.io-client: private
  /engine.io-parser/5.2.3:
    engine.io-parser: private
  /engine.io/6.5.5:
    engine.io: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es6-promise/4.2.8:
    es6-promise: private
  /es6-promisify/5.0.0:
    es6-promisify: private
  /esbuild/0.18.20:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-plugin-react-hooks/4.6.0(eslint@8.45.0):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react-refresh/0.4.3(eslint@8.45.0):
    eslint-plugin-react-refresh: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /espree/9.6.1:
    espree: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /event-target-shim/5.0.1:
    event-target-shim: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /execa/7.2.0:
    execa: private
  /express/4.18.2:
    express: private
  /eyes/0.1.8:
    eyes: private
  /fast-copy/3.0.2:
    fast-copy: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-redact/3.5.0:
    fast-redact: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fast-stable-stringify/1.0.0:
    fast-stable-stringify: private
  /fastq/1.19.1:
    fastq: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /file-uri-to-path/1.0.0:
    file-uri-to-path: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.2.0:
    finalhandler: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flat/5.0.2:
    flat: private
  /flatted/3.3.3:
    flatted: private
  /forwarded/0.2.0:
    forwarded: private
  /fresh/0.5.2:
    fresh: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /generic-pool/3.9.0:
    generic-pool: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-func-name/2.0.2:
    get-func-name: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/7.2.0:
    glob: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /grapheme-splitter/1.0.4:
    grapheme-splitter: private
  /graphemer/1.4.0:
    graphemer: private
  /has-flag/4.0.0:
    has-flag: private
  /has-symbols/1.1.0:
    has-symbols: private
  /hasown/2.0.2:
    hasown: private
  /he/1.2.0:
    he: private
  /helmet/7.0.0:
    helmet: private
  /help-me/4.2.0:
    help-me: private
  /http-errors/2.0.0:
    http-errors: private
  /human-signals/4.3.1:
    human-signals: private
  /humanize-ms/1.2.1:
    humanize-ms: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/4.0.0:
    is-fullwidth-code-point: private
  /is-glob/4.0.3:
    is-glob: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/2.1.0:
    is-plain-obj: private
  /is-stream/3.0.0:
    is-stream: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /isexe/2.0.0:
    isexe: private
  /isomorphic-ws/4.0.1(ws@7.5.10):
    isomorphic-ws: private
  /jayson/4.2.0:
    jayson: private
  /joycon/3.1.1:
    joycon: private
  /js-sha256/0.9.0:
    js-sha256: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json-stringify-safe/5.0.1:
    json-stringify-safe: private
  /json5/2.2.3:
    json5: private
  /jsonwebtoken/9.0.2:
    jsonwebtoken: private
  /jwa/1.4.2:
    jwa: private
  /jws/3.2.2:
    jws: private
  /keyv/4.5.4:
    keyv: private
  /levn/0.4.1:
    levn: private
  /lilconfig/2.1.0:
    lilconfig: private
  /listr2/5.0.8:
    listr2: private
  /local-pkg/0.4.3:
    local-pkg: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.includes/4.3.0:
    lodash.includes: private
  /lodash.isboolean/3.0.3:
    lodash.isboolean: private
  /lodash.isinteger/4.0.4:
    lodash.isinteger: private
  /lodash.isnumber/3.0.3:
    lodash.isnumber: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.isstring/4.0.1:
    lodash.isstring: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.once/4.1.1:
    lodash.once: private
  /log-symbols/4.1.0:
    log-symbols: private
  /log-update/4.0.0:
    log-update: private
  /loose-envify/1.4.0:
    loose-envify: private
  /loupe/2.3.7:
    loupe: private
  /lower-case/2.0.2:
    lower-case: private
  /lru-cache/5.1.1:
    lru-cache: private
  /magic-string/0.30.17:
    magic-string: private
  /make-error/1.3.6:
    make-error: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /media-typer/0.3.0:
    media-typer: private
  /merge-descriptors/1.0.1:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /mimic-fn/4.0.0:
    mimic-fn: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /mkdirp/0.5.6:
    mkdirp: private
  /mlly/1.7.4:
    mlly: private
  /mocha/10.2.0:
    mocha: private
  /ms/2.1.3:
    ms: private
  /nanoid/3.3.3:
    nanoid: private
  /natural-compare-lite/1.4.0:
    natural-compare-lite: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.3:
    negotiator: private
  /no-case/3.0.4:
    no-case: private
  /node-fetch/2.7.0:
    node-fetch: private
  /node-gyp-build/4.8.4:
    node-gyp-build: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /npm-run-path/5.3.0:
    npm-run-path: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /obuf/1.1.2:
    obuf: private
  /on-exit-leak-free/2.1.2:
    on-exit-leak-free: private
  /on-finished/2.4.1:
    on-finished: private
  /once/1.4.0:
    once: private
  /onetime/6.0.0:
    onetime: private
  /optionator/0.9.4:
    optionator: private
  /p-limit/4.0.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-map/4.0.0:
    p-map: private
  /packet-reader/1.0.0:
    packet-reader: private
  /pako/2.1.0:
    pako: private
  /parent-module/1.0.1:
    parent-module: private
  /parseurl/1.3.3:
    parseurl: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-to-regexp/0.1.7:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pathe/1.1.2:
    pathe: private
  /pathval/1.1.1:
    pathval: private
  /pg-cloudflare/1.2.7:
    pg-cloudflare: private
  /pg-connection-string/2.9.1:
    pg-connection-string: private
  /pg-int8/1.0.1:
    pg-int8: private
  /pg-numeric/1.0.2:
    pg-numeric: private
  /pg-pool/3.10.1(pg@8.11.3):
    pg-pool: private
  /pg-protocol/1.10.3:
    pg-protocol: private
  /pg-types/4.1.0:
    pg-types: private
  /pg/8.11.3:
    pg: private
  /pgpass/1.0.5:
    pgpass: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pino-abstract-transport/1.2.0:
    pino-abstract-transport: private
  /pino-pretty/10.2.0:
    pino-pretty: private
  /pino-std-serializers/6.2.2:
    pino-std-serializers: private
  /pino/8.15.0:
    pino: private
  /pkg-types/1.3.1:
    pkg-types: private
  /postcss/8.5.6:
    postcss: private
  /postgres-array/2.0.0:
    postgres-array: private
  /postgres-bytea/1.0.0:
    postgres-bytea: private
  /postgres-date/1.0.7:
    postgres-date: private
  /postgres-interval/1.2.0:
    postgres-interval: private
  /postgres-range/1.1.4:
    postgres-range: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /prisma/5.2.0:
    prisma: private
  /process-warning/2.3.2:
    process-warning: private
  /process/0.11.10:
    process: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /pump/3.0.3:
    pump: private
  /punycode/2.3.1:
    punycode: private
  /qs/6.11.0:
    qs: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /quick-format-unescaped/4.0.4:
    quick-format-unescaped: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.1:
    raw-body: private
  /react-is/18.3.1:
    react-is: private
  /react-refresh/0.14.2:
    react-refresh: private
  /react/18.3.1:
    react: private
  /readable-stream/4.7.0:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /real-require/0.2.0:
    real-require: private
  /redis/4.6.8:
    redis: private
  /require-directory/2.1.1:
    require-directory: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /rollup/3.29.5:
    rollup: private
  /rpc-websockets/7.11.2:
    rpc-websockets: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-stable-stringify/2.5.0:
    safe-stable-stringify: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /secure-json-parse/2.7.0:
    secure-json-parse: private
  /semver/7.7.2:
    semver: private
  /send/0.18.0:
    send: private
  /serialize-javascript/6.0.0:
    serialize-javascript: private
  /serve-static/1.15.0:
    serve-static: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /siginfo/2.0.0:
    siginfo: private
  /signal-exit/3.0.7:
    signal-exit: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/5.0.0:
    slice-ansi: private
  /snake-case/3.0.4:
    snake-case: private
  /socket.io-adapter/2.5.5:
    socket.io-adapter: private
  /socket.io-client/4.7.2:
    socket.io-client: private
  /socket.io-parser/4.2.4:
    socket.io-parser: private
  /socket.io/4.7.2:
    socket.io: private
  /sonic-boom/3.8.1:
    sonic-boom: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /split2/4.2.0:
    split2: private
  /stackback/0.0.2:
    stackback: private
  /statuses/2.0.1:
    statuses: private
  /std-env/3.9.0:
    std-env: private
  /stream-chain/2.2.5:
    stream-chain: private
  /stream-json/1.9.1:
    stream-json: private
  /string-argv/0.3.2:
    string-argv: private
  /string-width/5.1.2:
    string-width: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-final-newline/3.0.0:
    strip-final-newline: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-literal/1.3.0:
    strip-literal: private
  /superstruct/0.15.5:
    superstruct: private
  /supports-color/8.1.1:
    supports-color: private
  /synckit/0.8.8:
    synckit: private
  /text-encoding-utf-8/1.0.2:
    text-encoding-utf-8: private
  /text-table/0.2.0:
    text-table: private
  /thread-stream/2.7.0:
    thread-stream: private
  /through/2.3.8:
    through: private
  /tinybench/2.9.0:
    tinybench: private
  /tinypool/0.7.0:
    tinypool: private
  /tinyspy/2.2.1:
    tinyspy: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /toml/3.0.0:
    toml: private
  /tr46/0.0.3:
    tr46: private
  /ts-api-utils/1.4.3(typescript@5.1.6):
    ts-api-utils: private
  /ts-mocha/10.0.0(mocha@10.2.0):
    ts-mocha: private
  /ts-node/7.0.1:
    ts-node: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /tsx/3.12.7:
    tsx: private
  /tweetnacl/1.0.3:
    tweetnacl: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.1.0:
    type-detect: private
  /type-fest/0.20.2:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /ufo/1.6.1:
    ufo: private
  /undici-types/6.21.0:
    undici-types: private
  /unpipe/1.0.0:
    unpipe: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /use-sync-external-store/1.2.0(react@18.3.1):
    use-sync-external-store: private
  /utf-8-validate/5.0.10:
    utf-8-validate: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/8.3.2:
    uuid: private
  /vary/1.1.2:
    vary: private
  /vite-node/0.34.3(@types/node@20.5.0):
    vite-node: private
  /vite/4.4.5(@types/node@20.5.0):
    vite: private
  /vitest/0.34.3:
    vitest: private
  /webidl-conversions/3.0.1:
    webidl-conversions: private
  /whatwg-url/5.0.0:
    whatwg-url: private
  /which/2.0.2:
    which: private
  /why-is-node-running/2.3.0:
    why-is-node-running: private
  /word-wrap/1.2.5:
    word-wrap: private
  /workerpool/6.2.1:
    workerpool: private
  /wrap-ansi/7.0.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /ws/8.17.1:
    ws: private
  /xmlhttprequest-ssl/2.0.0:
    xmlhttprequest-ssl: private
  /xtend/4.0.2:
    xtend: private
  /y18n/5.0.8:
    y18n: private
  /yallist/4.0.0:
    yallist: private
  /yaml/2.8.0:
    yaml: private
  /yargs-parser/20.2.4:
    yargs-parser: private
  /yargs-unparser/2.0.0:
    yargs-unparser: private
  /yargs/16.2.0:
    yargs: private
  /yn/2.0.0:
    yn: private
  /yocto-queue/1.2.1:
    yocto-queue: private
  /zod/3.22.2:
    zod: private
  /zustand/4.4.1(react@18.3.1):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.6.12
pendingBuilds: []
prunedAt: Sun, 03 Aug 2025 20:16:13 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/android-arm/0.18.20
  - /@esbuild/android-arm64/0.18.20
  - /@esbuild/android-x64/0.18.20
  - /@esbuild/darwin-arm64/0.18.20
  - /@esbuild/darwin-x64/0.18.20
  - /@esbuild/freebsd-arm64/0.18.20
  - /@esbuild/freebsd-x64/0.18.20
  - /@esbuild/linux-arm/0.18.20
  - /@esbuild/linux-arm64/0.18.20
  - /@esbuild/linux-ia32/0.18.20
  - /@esbuild/linux-loong64/0.18.20
  - /@esbuild/linux-mips64el/0.18.20
  - /@esbuild/linux-ppc64/0.18.20
  - /@esbuild/linux-riscv64/0.18.20
  - /@esbuild/linux-s390x/0.18.20
  - /@esbuild/linux-x64/0.18.20
  - /@esbuild/netbsd-x64/0.18.20
  - /@esbuild/openbsd-x64/0.18.20
  - /@esbuild/sunos-x64/0.18.20
  - /@esbuild/win32-arm64/0.18.20
  - /@esbuild/win32-ia32/0.18.20
  - /fsevents/2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Documents\Code\agario gamble\node_modules\.pnpm
