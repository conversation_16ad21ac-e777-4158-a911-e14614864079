{"name": "@agario-solana/client", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "clean": "rm -rf dist node_modules"}, "dependencies": {"@solana/web3.js": "^1.98.0", "socket.io-client": "^4.7.2", "zustand": "^4.4.1"}, "devDependencies": {"@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}