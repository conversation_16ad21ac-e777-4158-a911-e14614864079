{"version": 3, "file": "rpc.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/rpc.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;AACvD,OAAO,QAAQ,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,GAAG,EAAE,MAAM,cAAc,CAAC;AAEnC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EACL,eAAe,EACf,oBAAoB,EACpB,yBAAyB,EAC1B,MAAM,YAAY,CAAC;AAEpB,MAAM,CAAC,OAAO,OAAO,UAAU;WACf,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,EACjE,KAAK,EAAE,CAAC,EACR,IAAI,EAAE,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,EAC3B,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAC9B,QAAQ,EAAE,QAAQ,GACjB,KAAK;CAsBT;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,MAAM,YAAY,CACtB,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,IACnD,yBAAyB,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAErE;;;GAGG;AACH,MAAM,MAAM,KAAK,CACf,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,IACnD,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC"}