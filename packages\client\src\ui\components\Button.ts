export interface ButtonProps {
  text: string
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string
  onClick?: () => void
  className?: string
}

export class Button {
  private element: HTMLButtonElement
  private props: ButtonProps
  
  constructor(props: ButtonProps) {
    this.props = props
    this.element = this.createElement()
    this.setupEventListeners()
  }
  
  private createElement(): HTMLButtonElement {
    const button = document.createElement('button')
    button.type = 'button'
    
    this.updateElement(button)
    return button
  }
  
  private updateElement(button: HTMLButtonElement): void {
    const { text, variant = 'primary', size = 'md', disabled = false, loading = false, icon, className = '' } = this.props
    
    // Clear existing content
    button.innerHTML = ''
    button.className = ''
    
    // Base classes
    button.className = `btn btn--${variant} btn--${size} ${className}`.trim()
    
    // States
    if (disabled || loading) {
      button.disabled = true
      button.classList.add('btn--disabled')
    }
    
    if (loading) {
      button.classList.add('btn--loading')
    }
    
    // Content
    if (loading) {
      const spinner = document.createElement('div')
      spinner.className = 'btn__spinner'
      button.appendChild(spinner)
    } else if (icon) {
      const iconEl = document.createElement('span')
      iconEl.className = 'btn__icon'
      iconEl.textContent = icon
      button.appendChild(iconEl)
    }
    
    const textEl = document.createElement('span')
    textEl.className = 'btn__text'
    textEl.textContent = text
    button.appendChild(textEl)
    
    // Add styles if not already present
    this.ensureStyles()
  }
  
  private setupEventListeners(): void {
    this.element.addEventListener('click', (e) => {
      e.preventDefault()
      if (!this.props.disabled && !this.props.loading && this.props.onClick) {
        this.props.onClick()
      }
    })
  }
  
  private ensureStyles(): void {
    if (document.getElementById('button-styles')) return
    
    const style = document.createElement('style')
    style.id = 'button-styles'
    style.textContent = `
      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-sm);
        border: none;
        border-radius: var(--radius-md);
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: all var(--transition-fast);
        text-decoration: none;
        user-select: none;
        position: relative;
        overflow: hidden;
      }
      
      .btn:focus-visible {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
      }
      
      .btn--disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .btn--loading {
        cursor: wait;
      }
      
      /* Sizes */
      .btn--sm {
        padding: var(--space-xs) var(--space-md);
        font-size: 0.875rem;
        line-height: 1.25rem;
      }
      
      .btn--md {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--font-size-body);
        line-height: var(--line-height-body);
      }
      
      .btn--lg {
        padding: var(--space-md) var(--space-xl);
        font-size: 1.125rem;
        line-height: 1.75rem;
      }
      
      /* Variants */
      .btn--primary {
        background: var(--color-primary);
        color: var(--color-background-primary);
      }
      
      .btn--primary:hover:not(.btn--disabled) {
        background: #0EA5E9;
        transform: translateY(-1px);
      }
      
      .btn--secondary {
        background: var(--color-background-panel);
        color: var(--color-text-primary);
        border: 1px solid var(--color-border);
      }
      
      .btn--secondary:hover:not(.btn--disabled) {
        background: #1E293B;
        border-color: var(--color-primary);
      }
      
      .btn--danger {
        background: var(--color-error);
        color: white;
      }
      
      .btn--danger:hover:not(.btn--disabled) {
        background: #DC2626;
        transform: translateY(-1px);
      }
      
      .btn--ghost {
        background: transparent;
        color: var(--color-text-secondary);
      }
      
      .btn--ghost:hover:not(.btn--disabled) {
        background: var(--color-background-panel);
        color: var(--color-text-primary);
      }
      
      /* Loading spinner */
      .btn__spinner {
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .btn__icon {
        font-size: 1.2em;
      }
      
      .btn__text {
        white-space: nowrap;
      }
    `
    
    document.head.appendChild(style)
  }
  
  /**
   * Update button properties
   */
  update(props: Partial<ButtonProps>): void {
    this.props = { ...this.props, ...props }
    this.updateElement(this.element)
  }
  
  /**
   * Get the DOM element
   */
  getElement(): HTMLButtonElement {
    return this.element
  }
  
  /**
   * Destroy the button
   */
  destroy(): void {
    this.element.remove()
  }
}
