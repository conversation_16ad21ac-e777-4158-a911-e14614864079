{"name": "agario-solana-clone", "version": "0.1.0", "private": true, "description": "Agar.io clone with Solana integration and wagering", "scripts": {"dev": "pnpm --parallel --recursive dev", "build": "pnpm --recursive build", "test": "pnpm --recursive test", "lint": "pnpm --recursive lint", "lint-staged": "lint-staged", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "pnpm --recursive clean && rm -rf node_modules", "postinstall": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "lint-staged": {"*.{js,ts,tsx,json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.12"}