# Agar.io Solana Escrow Program

This Anchor program manages the Solana-based escrow system for the Agar.io clone, handling wagers, deposits, cash-outs, and treasury management.

## Overview

The program implements a secure escrow system where:
- Users deposit SOL to participate in wagered games
- Wagers are held in escrow during gameplay
- Winners can cash out based on their in-game performance
- Platform fees are automatically deducted
- Oracle pricing ensures fair SOL/USD conversions

## Program Architecture

### Accounts (PDAs)

- **GlobalConfig**: System-wide configuration (fees, oracle settings, pause state)
- **Treasury**: Escrow account holding all deposited SOL
- **Lobby**: Per-tier lobby configuration and state
- **UserState**: Per-user balance and transaction history
- **Session**: Active game session linking user, lobby, and wager

### Instructions

#### Admin Instructions
- `initialize_global`: Initialize system configuration
- `initialize_treasury`: Create treasury PDA
- `create_lobby`: Create a new wager tier lobby
- `set_pause`: Pause/unpause the system
- `update_fees`: Update platform fees and oracle margins
- `record_elimination`: Record game eliminations (audit trail)

#### User Instructions
- `deposit`: Deposit SOL to user's balance
- `enter_lobby`: Enter a lobby with a wager
- `cash_out`: Cash out current session value
- `withdraw_unused`: Withdraw unused deposited SOL

## Security Features

### Oracle Integration
- Pyth Network SOL/USD price feeds
- Staleness checks (5-minute maximum)
- Confidence interval validation
- Safety margins for price volatility

### Anti-Cheat Measures
- Server-signed session tokens
- Authoritative game state validation
- Replay protection via nonces
- Rate limiting and input validation

### Financial Controls
- Platform fee caps (max 10%)
- Circuit breakers for large payouts
- Separate escrow for user deposits
- Audit trail for all transactions

## Development

### Prerequisites
- Rust and Cargo
- Solana CLI tools
- Anchor Framework

### Building
```bash
# Build the program
pnpm build

# Run tests
pnpm test

# Deploy to devnet
pnpm deploy
```

### Testing
The test suite covers:
- Global configuration setup
- Treasury initialization
- Lobby creation and management
- User deposits and withdrawals
- Lobby entry and session creation
- Admin controls (pause, fee updates)
- Error conditions and edge cases

### Configuration

Key configuration parameters:
- `PLATFORM_FEE_BPS`: Platform fee in basis points (default: 500 = 5%)
- `ORACLE_SAFETY_MARGIN_BPS`: Oracle confidence margin (default: 50 = 0.5%)
- Lobby tiers and minimum wagers
- Session timeout and cooldown periods

## Deployment

### Devnet
```bash
# Set cluster to devnet
solana config set --url devnet

# Deploy program
anchor deploy
```

### Mainnet
```bash
# Set cluster to mainnet-beta
solana config set --url mainnet-beta

# Deploy program (requires sufficient SOL for deployment)
anchor deploy
```

## Integration

The generated IDL is automatically copied to `docs/idl/agar_escrow.json` for client integration.

### Client Usage
```typescript
import { Program } from "@coral-xyz/anchor";
import { AgarEscrow } from "./idl/agar_escrow";

// Initialize program
const program = new Program<AgarEscrow>(idl, programId, provider);

// Deposit SOL
await program.methods
  .deposit(new BN(amount))
  .accounts({
    userState: userStatePda,
    treasury: treasuryPda,
    globalConfig: globalConfigPda,
    user: userPublicKey,
    systemProgram: SystemProgram.programId,
  })
  .rpc();
```

## Error Codes

- `GlobalPaused`: System is paused
- `InsufficientBalance`: User has insufficient balance
- `InvalidWagerAmount`: Wager below minimum or invalid
- `StalePriceData`: Oracle price is too old
- `WideConfidenceInterval`: Oracle confidence too wide
- `MathOverflow`: Arithmetic overflow detected
- `InsufficientTreasuryBalance`: Treasury lacks funds for payout

## Monitoring

Key metrics to monitor:
- Treasury balance vs. total user deposits
- Platform fee collection
- Oracle price feed health
- Transaction success/failure rates
- Active sessions and lobby utilization
