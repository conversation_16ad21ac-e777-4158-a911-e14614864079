import { randomBytes } from 'crypto';
// Generate a unique ID for entities
export function generateId() {
    return randomBytes(16).toString('hex');
}
// Generate a session token
export function generateSessionToken() {
    return randomBytes(32).toString('hex');
}
// Generate a shorter ID for temporary entities
export function generateShortId() {
    return randomBytes(8).toString('hex');
}
// Validate ID format
export function isValidId(id) {
    return /^[a-f0-9]{32}$/.test(id);
}
// Validate session token format
export function isValidSessionToken(token) {
    return /^[a-f0-9]{64}$/.test(token);
}
