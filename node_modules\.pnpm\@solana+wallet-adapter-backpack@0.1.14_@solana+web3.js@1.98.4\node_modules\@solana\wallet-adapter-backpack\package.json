{"name": "@solana/wallet-adapter-backpack", "version": "0.1.14", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/solana-labs/wallet-adapter", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE"], "engines": {"node": ">=16"}, "type": "module", "sideEffects": false, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js", "types": "./lib/types/index.d.ts"}, "peerDependencies": {"@solana/web3.js": "^1.77.3"}, "dependencies": {"@solana/wallet-adapter-base": "^0.9.23"}, "devDependencies": {"@solana/web3.js": "^1.77.3", "shx": "^0.3.4"}, "scripts": {"build": "tsc --build --verbose && pnpm run package", "clean": "shx mkdir -p lib && shx rm -rf lib", "lint": "prettier --check 'src/{*,**/*}.{ts,tsx,js,jsx,json}' && eslint", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json"}}