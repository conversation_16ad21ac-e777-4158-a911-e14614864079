import { GameState } from '../state.js'
import { PlayerInput, PlayerCell, EntityType, GameEventType } from '../types.js'
import { GAME_CONFIG } from '../../config/lobby.js'
import { generateId } from '../../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'split-merge-system' })

export class SplitMergeSystem {
  private gameState: GameState

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  update(deltaTime: number): void {
    const world = this.gameState.getWorld()
    const now = Date.now()

    // Update merge cooldowns and check for automatic merging
    for (const player of world.players.values()) {
      if (!player.isAlive) continue

      this.updateCellMergeCooldowns(player, now)
      this.checkAutoMerge(player, now)
    }
  }

  processSplitInput(input: PlayerInput): void {
    const player = this.gameState.getPlayer(input.playerId)
    if (!player || !player.isAlive) return

    const now = Date.now()

    // Check if player can split (cooldown and cell count limits)
    if (!this.canPlayerSplit(player, now)) {
      return
    }

    // Split the largest cell
    const largestCell = this.getLargestCell(player)
    if (largestCell && largestCell.mass >= GAME_CONFIG.MIN_SPLIT_MASS) {
      this.splitCell(largestCell, player.targetPosition, now)
    }
  }

  private updateCellMergeCooldowns(player: any, now: number): void {
    for (const cell of player.cells) {
      // Update merge cooldown
      if (cell.mergeTime <= now) {
        cell.canMerge = true
      }

      // Update split cooldown
      if (cell.splitCooldown <= now) {
        cell.splitCooldown = 0
      }
    }
  }

  private checkAutoMerge(player: any, now: number): void {
    const world = this.gameState.getWorld()
    const cellsToMerge: PlayerCell[] = []

    // Find cells that can merge
    for (let i = 0; i < player.cells.length; i++) {
      const cell1 = player.cells[i]
      if (!cell1.canMerge) continue

      for (let j = i + 1; j < player.cells.length; j++) {
        const cell2 = player.cells[j]
        if (!cell2.canMerge) continue

        // Check if cells are close enough to merge
        const dx = cell1.position.x - cell2.position.x
        const dy = cell1.position.y - cell2.position.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const mergeDistance = (cell1.radius + cell2.radius) * GAME_CONFIG.MERGE_DISTANCE_FACTOR

        if (distance <= mergeDistance) {
          // Merge the smaller cell into the larger one
          const [largerCell, smallerCell] = cell1.mass >= cell2.mass ? [cell1, cell2] : [cell2, cell1]
          
          if (!cellsToMerge.includes(smallerCell)) {
            cellsToMerge.push(smallerCell)
            this.mergeCells(largerCell, smallerCell, player, now)
          }
        }
      }
    }
  }

  private canPlayerSplit(player: any, now: number): boolean {
    // Check cell count limit
    if (player.cells.length >= GAME_CONFIG.MAX_CELLS_PER_PLAYER) {
      return false
    }

    // Check if any cell can split (not on cooldown)
    return player.cells.some((cell: PlayerCell) => 
      cell.splitCooldown <= now && cell.mass >= GAME_CONFIG.MIN_SPLIT_MASS
    )
  }

  private getLargestCell(player: any): PlayerCell | null {
    if (player.cells.length === 0) return null

    return player.cells.reduce((largest: PlayerCell, current: PlayerCell) => 
      current.mass > largest.mass ? current : largest
    )
  }

  private splitCell(cell: PlayerCell, targetPosition: any, now: number): void {
    const world = this.gameState.getWorld()
    const player = world.players.get(cell.playerId)
    if (!player) return

    // Calculate split mass (each cell gets half)
    const splitMass = cell.mass / 2
    const splitRadius = this.massToRadius(splitMass)

    // Calculate direction towards target
    const dx = targetPosition.x - cell.position.x
    const dy = targetPosition.y - cell.position.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    let dirX = 0
    let dirY = 0
    if (distance > 0) {
      dirX = dx / distance
      dirY = dy / distance
    }

    // Update original cell
    cell.mass = splitMass
    cell.radius = splitRadius
    cell.canMerge = false
    cell.mergeTime = now + GAME_CONFIG.MERGE_COOLDOWN
    cell.splitCooldown = now + GAME_CONFIG.SPLIT_COOLDOWN
    cell.lastSplit = now

    // Create new cell
    const newCell: PlayerCell = {
      id: generateId(),
      type: EntityType.PLAYER_CELL,
      playerId: cell.playerId,
      position: {
        x: cell.position.x + dirX * (cell.radius + splitRadius + 5),
        y: cell.position.y + dirY * (cell.radius + splitRadius + 5),
      },
      radius: splitRadius,
      mass: splitMass,
      velocity: {
        x: dirX * GAME_CONFIG.SPLIT_VELOCITY,
        y: dirY * GAME_CONFIG.SPLIT_VELOCITY,
      },
      targetPosition: { ...targetPosition },
      canMerge: false,
      mergeTime: now + GAME_CONFIG.MERGE_COOLDOWN,
      splitCooldown: now + GAME_CONFIG.SPLIT_COOLDOWN,
      lastSplit: now,
    }

    // Ensure new cell is within bounds
    const bounds = world.quadTree.root.bounds
    newCell.position.x = Math.max(newCell.radius, Math.min(bounds.width - newCell.radius, newCell.position.x))
    newCell.position.y = Math.max(newCell.radius, Math.min(bounds.height - newCell.radius, newCell.position.y))

    // Add new cell to player and world
    player.cells.push(newCell)
    world.quadTree.insert(newCell)
    world.quadTree.update(cell) // Update original cell in quadtree

    logger.debug(`Player ${player.id} split cell ${cell.id}, created ${newCell.id}`)

    // Create event
    // This would be handled by the GameState class
  }

  private mergeCells(largerCell: PlayerCell, smallerCell: PlayerCell, player: any, now: number): void {
    const world = this.gameState.getWorld()

    // Add smaller cell's mass to larger cell
    largerCell.mass += smallerCell.mass
    largerCell.radius = this.massToRadius(largerCell.mass)

    // Remove smaller cell from world and player
    world.quadTree.remove(smallerCell)
    player.cells = player.cells.filter((c: PlayerCell) => c.id !== smallerCell.id)

    // Update larger cell in quadtree
    world.quadTree.update(largerCell)

    // Set merge cooldown
    largerCell.canMerge = false
    largerCell.mergeTime = now + GAME_CONFIG.MERGE_COOLDOWN

    logger.debug(`Player ${player.id} merged cells: ${smallerCell.id} into ${largerCell.id}`)

    // Create event
    // This would be handled by the GameState class
  }

  private massToRadius(mass: number): number {
    return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR
  }

  // Public methods for external access
  forceMergeCells(playerId: string): void {
    const player = this.gameState.getPlayer(playerId)
    if (!player || !player.isAlive) return

    const now = Date.now()

    // Merge all cells into the largest one
    if (player.cells.length <= 1) return

    const largestCell = this.getLargestCell(player)
    if (!largestCell) return

    const world = this.gameState.getWorld()
    const cellsToRemove: PlayerCell[] = []

    for (const cell of player.cells) {
      if (cell.id === largestCell.id) continue

      // Add mass to largest cell
      largestCell.mass += cell.mass
      cellsToRemove.push(cell)
    }

    // Remove merged cells
    for (const cell of cellsToRemove) {
      world.quadTree.remove(cell)
    }

    // Update player cells array
    player.cells = [largestCell]

    // Update largest cell
    largestCell.radius = this.massToRadius(largestCell.mass)
    largestCell.canMerge = false
    largestCell.mergeTime = now + GAME_CONFIG.MERGE_COOLDOWN

    world.quadTree.update(largestCell)

    logger.info(`Force merged all cells for player ${playerId}`)
  }

  getCellCount(playerId: string): number {
    const player = this.gameState.getPlayer(playerId)
    return player ? player.cells.length : 0
  }

  canSplit(playerId: string): boolean {
    const player = this.gameState.getPlayer(playerId)
    if (!player || !player.isAlive) return false

    const now = Date.now()
    return this.canPlayerSplit(player, now)
  }
}
