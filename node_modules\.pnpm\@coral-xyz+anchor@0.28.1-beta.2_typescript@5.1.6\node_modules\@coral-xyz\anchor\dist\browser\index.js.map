{"version": 3, "file": "index.js", "sources": ["../../../src/utils/common.ts", "../../../src/utils/bytes/hex.ts", "../../../src/utils/bytes/utf8.ts", "../../../src/utils/bytes/bs58.ts", "../../../src/utils/bytes/base64.ts", "../../../src/program/common.ts", "../../../../node_modules/superstruct/lib/index.es.js", "../../../src/utils/rpc.ts", "../../../src/provider.ts", "../../../src/utils/features.ts", "../../../src/error.ts", "../../../../node_modules/snake-case/node_modules/tslib/tslib.es6.js", "../../../../node_modules/dot-case/node_modules/tslib/tslib.es6.js", "../../../../node_modules/lower-case/dist.es2015/index.js", "../../../../node_modules/no-case/dist.es2015/index.js", "../../../../node_modules/dot-case/dist.es2015/index.js", "../../../../node_modules/snake-case/dist.es2015/index.js", "../../../src/coder/borsh/idl.ts", "../../../src/coder/borsh/instruction.ts", "../../../src/coder/common.ts", "../../../src/coder/borsh/discriminator.ts", "../../../src/coder/borsh/accounts.ts", "../../../src/coder/borsh/event.ts", "../../../src/coder/borsh/types.ts", "../../../src/coder/borsh/index.ts", "../../../../node_modules/buffer-layout/lib/Layout.js", "../../../src/coder/system/instruction.ts", "../../../src/coder/system/accounts.ts", "../../../src/coder/system/events.ts", "../../../src/coder/system/types.ts", "../../../src/coder/system/index.ts", "../../../src/utils/sha256.ts", "../../../src/utils/pubkey.ts", "../../../src/utils/token.ts", "../../../../node_modules/cross-fetch/dist/browser-ponyfill.js", "../../../src/utils/registry.ts", "../../../src/idl.ts", "../../../src/program/context.ts", "../../../src/program/namespace/instruction.ts", "../../../src/program/namespace/transaction.ts", "../../../src/program/namespace/rpc.ts", "../../../src/program/namespace/account.ts", "../../../src/program/event.ts", "../../../src/program/namespace/simulate.ts", "../../../src/program/token-account-layout.ts", "../../../src/program/accounts-resolver.ts", "../../../src/program/namespace/methods.ts", "../../../src/program/namespace/views.ts", "../../../src/program/namespace/index.ts", "../../../src/program/index.ts", "../../../src/native/system.ts", "../../../src/native/index.ts"], "sourcesContent": [null, null, null, null, null, null, "/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n  constructor(failure, failures) {\n    let cached;\n    const {\n      message,\n      ...rest\n    } = failure;\n    const {\n      path\n    } = failure;\n    const msg = path.length === 0 ? message : \"At path: \" + path.join('.') + \" -- \" + message;\n    super(msg);\n    this.value = void 0;\n    this.key = void 0;\n    this.type = void 0;\n    this.refinement = void 0;\n    this.path = void 0;\n    this.branch = void 0;\n    this.failures = void 0;\n    Object.assign(this, rest);\n    this.name = this.constructor.name;\n\n    this.failures = () => {\n      var _cached;\n\n      return (_cached = cached) != null ? _cached : cached = [failure, ...failures()];\n    };\n  }\n\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n  return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\n\n\nfunction isObject(x) {\n  return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\n\nfunction isPlainObject(x) {\n  if (Object.prototype.toString.call(x) !== '[object Object]') {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(x);\n  return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\n\nfunction print(value) {\n  return typeof value === 'string' ? JSON.stringify(value) : \"\" + value;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\n\nfunction shiftIterator(input) {\n  const {\n    done,\n    value\n  } = input.next();\n  return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\n\nfunction toFailure(result, context, struct, value) {\n  if (result === true) {\n    return;\n  } else if (result === false) {\n    result = {};\n  } else if (typeof result === 'string') {\n    result = {\n      message: result\n    };\n  }\n\n  const {\n    path,\n    branch\n  } = context;\n  const {\n    type\n  } = struct;\n  const {\n    refinement,\n    message = \"Expected a value of type `\" + type + \"`\" + (refinement ? \" with refinement `\" + refinement + \"`\" : '') + \", but received: `\" + print(value) + \"`\"\n  } = result;\n  return {\n    value,\n    type,\n    refinement,\n    key: path[path.length - 1],\n    path,\n    branch,\n    ...result,\n    message\n  };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\n\nfunction* toFailures(result, context, struct, value) {\n  if (!isIterable(result)) {\n    result = [result];\n  }\n\n  for (const r of result) {\n    const failure = toFailure(r, context, struct, value);\n\n    if (failure) {\n      yield failure;\n    }\n  }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\n\nfunction* run(value, struct, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    path = [],\n    branch = [value],\n    coerce = false,\n    mask = false\n  } = options;\n  const ctx = {\n    path,\n    branch\n  };\n\n  if (coerce) {\n    value = struct.coercer(value, ctx);\n\n    if (mask && struct.type !== 'type' && isObject(struct.schema) && isObject(value) && !Array.isArray(value)) {\n      for (const key in value) {\n        if (struct.schema[key] === undefined) {\n          delete value[key];\n        }\n      }\n    }\n  }\n\n  let valid = true;\n\n  for (const failure of struct.validator(value, ctx)) {\n    valid = false;\n    yield [failure, undefined];\n  }\n\n  for (let [k, v, s] of struct.entries(value, ctx)) {\n    const ts = run(v, s, {\n      path: k === undefined ? path : [...path, k],\n      branch: k === undefined ? branch : [...branch, v],\n      coerce,\n      mask\n    });\n\n    for (const t of ts) {\n      if (t[0]) {\n        valid = false;\n        yield [t[0], undefined];\n      } else if (coerce) {\n        v = t[1];\n\n        if (k === undefined) {\n          value = v;\n        } else if (value instanceof Map) {\n          value.set(k, v);\n        } else if (value instanceof Set) {\n          value.add(v);\n        } else if (isObject(value)) {\n          value[k] = v;\n        }\n      }\n    }\n  }\n\n  if (valid) {\n    for (const failure of struct.refiner(value, ctx)) {\n      valid = false;\n      yield [failure, undefined];\n    }\n  }\n\n  if (valid) {\n    yield [undefined, value];\n  }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\n\nclass Struct {\n  constructor(props) {\n    this.TYPE = void 0;\n    this.type = void 0;\n    this.schema = void 0;\n    this.coercer = void 0;\n    this.validator = void 0;\n    this.refiner = void 0;\n    this.entries = void 0;\n    const {\n      type,\n      schema,\n      validator,\n      refiner,\n      coercer = value => value,\n      entries = function* () {}\n    } = props;\n    this.type = type;\n    this.schema = schema;\n    this.entries = entries;\n    this.coercer = coercer;\n\n    if (validator) {\n      this.validator = (value, context) => {\n        const result = validator(value, context);\n        return toFailures(result, context, this, value);\n      };\n    } else {\n      this.validator = () => [];\n    }\n\n    if (refiner) {\n      this.refiner = (value, context) => {\n        const result = refiner(value, context);\n        return toFailures(result, context, this, value);\n      };\n    } else {\n      this.refiner = () => [];\n    }\n  }\n  /**\n   * Assert that a value passes the struct's validation, throwing if it doesn't.\n   */\n\n\n  assert(value) {\n    return assert(value, this);\n  }\n  /**\n   * Create a value with the struct's coercion logic, then validate it.\n   */\n\n\n  create(value) {\n    return create(value, this);\n  }\n  /**\n   * Check if a value passes the struct's validation.\n   */\n\n\n  is(value) {\n    return is(value, this);\n  }\n  /**\n   * Mask a value, coercing and validating it, but returning only the subset of\n   * properties defined by the struct's schema.\n   */\n\n\n  mask(value) {\n    return mask(value, this);\n  }\n  /**\n   * Validate a value with the struct's validation logic, returning a tuple\n   * representing the result.\n   *\n   * You may optionally pass `true` for the `withCoercion` argument to coerce\n   * the value before attempting to validate it. If you do, the result will\n   * contain the coerced result when successful.\n   */\n\n\n  validate(value, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    return validate(value, this, options);\n  }\n\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\n\nfunction assert(value, struct) {\n  const result = validate(value, struct);\n\n  if (result[0]) {\n    throw result[0];\n  }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\n\nfunction create(value, struct) {\n  const result = validate(value, struct, {\n    coerce: true\n  });\n\n  if (result[0]) {\n    throw result[0];\n  } else {\n    return result[1];\n  }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\n\nfunction mask(value, struct) {\n  const result = validate(value, struct, {\n    coerce: true,\n    mask: true\n  });\n\n  if (result[0]) {\n    throw result[0];\n  } else {\n    return result[1];\n  }\n}\n/**\n * Check if a value passes a struct.\n */\n\nfunction is(value, struct) {\n  const result = validate(value, struct);\n  return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\n\nfunction validate(value, struct, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const tuples = run(value, struct, options);\n  const tuple = shiftIterator(tuples);\n\n  if (tuple[0]) {\n    const error = new StructError(tuple[0], function* () {\n      for (const t of tuples) {\n        if (t[0]) {\n          yield t[0];\n        }\n      }\n    });\n    return [error, undefined];\n  } else {\n    const v = tuple[1];\n    return [undefined, v];\n  }\n}\n\nfunction assign() {\n  for (var _len = arguments.length, Structs = new Array(_len), _key = 0; _key < _len; _key++) {\n    Structs[_key] = arguments[_key];\n  }\n\n  const isType = Structs[0].type === 'type';\n  const schemas = Structs.map(s => s.schema);\n  const schema = Object.assign({}, ...schemas);\n  return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\n\nfunction define(name, validator) {\n  return new Struct({\n    type: name,\n    schema: null,\n    validator\n  });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\n\nfunction deprecated(struct, log) {\n  return new Struct({ ...struct,\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n\n    validator(value, ctx) {\n      if (value === undefined) {\n        return true;\n      } else {\n        log(value, ctx);\n        return struct.validator(value, ctx);\n      }\n    }\n\n  });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\n\nfunction dynamic(fn) {\n  return new Struct({\n    type: 'dynamic',\n    schema: null,\n\n    *entries(value, ctx) {\n      const struct = fn(value, ctx);\n      yield* struct.entries(value, ctx);\n    },\n\n    validator(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.validator(value, ctx);\n    },\n\n    coercer(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.coercer(value, ctx);\n    },\n\n    refiner(value, ctx) {\n      const struct = fn(value, ctx);\n      return struct.refiner(value, ctx);\n    }\n\n  });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\n\nfunction lazy(fn) {\n  let struct;\n  return new Struct({\n    type: 'lazy',\n    schema: null,\n\n    *entries(value, ctx) {\n      var _struct;\n\n      (_struct = struct) != null ? _struct : struct = fn();\n      yield* struct.entries(value, ctx);\n    },\n\n    validator(value, ctx) {\n      var _struct2;\n\n      (_struct2 = struct) != null ? _struct2 : struct = fn();\n      return struct.validator(value, ctx);\n    },\n\n    coercer(value, ctx) {\n      var _struct3;\n\n      (_struct3 = struct) != null ? _struct3 : struct = fn();\n      return struct.coercer(value, ctx);\n    },\n\n    refiner(value, ctx) {\n      var _struct4;\n\n      (_struct4 = struct) != null ? _struct4 : struct = fn();\n      return struct.refiner(value, ctx);\n    }\n\n  });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\n\nfunction omit(struct, keys) {\n  const {\n    schema\n  } = struct;\n  const subschema = { ...schema\n  };\n\n  for (const key of keys) {\n    delete subschema[key];\n  }\n\n  switch (struct.type) {\n    case 'type':\n      return type(subschema);\n\n    default:\n      return object(subschema);\n  }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\n\nfunction partial(struct) {\n  const schema = struct instanceof Struct ? { ...struct.schema\n  } : { ...struct\n  };\n\n  for (const key in schema) {\n    schema[key] = optional(schema[key]);\n  }\n\n  return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\n\nfunction pick(struct, keys) {\n  const {\n    schema\n  } = struct;\n  const subschema = {};\n\n  for (const key of keys) {\n    subschema[key] = schema[key];\n  }\n\n  return object(subschema);\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\n\nfunction struct(name, validator) {\n  console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n  return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\n\nfunction any() {\n  return define('any', () => true);\n}\nfunction array(Element) {\n  return new Struct({\n    type: 'array',\n    schema: Element,\n\n    *entries(value) {\n      if (Element && Array.isArray(value)) {\n        for (const [i, v] of value.entries()) {\n          yield [i, v, Element];\n        }\n      }\n    },\n\n    coercer(value) {\n      return Array.isArray(value) ? value.slice() : value;\n    },\n\n    validator(value) {\n      return Array.isArray(value) || \"Expected an array value, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a bigint.\n */\n\nfunction bigint() {\n  return define('bigint', value => {\n    return typeof value === 'bigint';\n  });\n}\n/**\n * Ensure that a value is a boolean.\n */\n\nfunction boolean() {\n  return define('boolean', value => {\n    return typeof value === 'boolean';\n  });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\n\nfunction date() {\n  return define('date', value => {\n    return value instanceof Date && !isNaN(value.getTime()) || \"Expected a valid `Date` object, but received: \" + print(value);\n  });\n}\nfunction enums(values) {\n  const schema = {};\n  const description = values.map(v => print(v)).join();\n\n  for (const key of values) {\n    schema[key] = key;\n  }\n\n  return new Struct({\n    type: 'enums',\n    schema,\n\n    validator(value) {\n      return values.includes(value) || \"Expected one of `\" + description + \"`, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a function.\n */\n\nfunction func() {\n  return define('func', value => {\n    return typeof value === 'function' || \"Expected a function, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\n\nfunction instance(Class) {\n  return define('instance', value => {\n    return value instanceof Class || \"Expected a `\" + Class.name + \"` instance, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is an integer.\n */\n\nfunction integer() {\n  return define('integer', value => {\n    return typeof value === 'number' && !isNaN(value) && Number.isInteger(value) || \"Expected an integer, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\n\nfunction intersection(Structs) {\n  return new Struct({\n    type: 'intersection',\n    schema: null,\n\n    *entries(value, ctx) {\n      for (const S of Structs) {\n        yield* S.entries(value, ctx);\n      }\n    },\n\n    *validator(value, ctx) {\n      for (const S of Structs) {\n        yield* S.validator(value, ctx);\n      }\n    },\n\n    *refiner(value, ctx) {\n      for (const S of Structs) {\n        yield* S.refiner(value, ctx);\n      }\n    }\n\n  });\n}\nfunction literal(constant) {\n  const description = print(constant);\n  const t = typeof constant;\n  return new Struct({\n    type: 'literal',\n    schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n\n    validator(value) {\n      return value === constant || \"Expected the literal `\" + description + \"`, but received: \" + print(value);\n    }\n\n  });\n}\nfunction map(Key, Value) {\n  return new Struct({\n    type: 'map',\n    schema: null,\n\n    *entries(value) {\n      if (Key && Value && value instanceof Map) {\n        for (const [k, v] of value.entries()) {\n          yield [k, k, Key];\n          yield [k, v, Value];\n        }\n      }\n    },\n\n    coercer(value) {\n      return value instanceof Map ? new Map(value) : value;\n    },\n\n    validator(value) {\n      return value instanceof Map || \"Expected a `Map` object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that no value ever passes validation.\n */\n\nfunction never() {\n  return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\n\nfunction nullable(struct) {\n  return new Struct({ ...struct,\n    validator: (value, ctx) => value === null || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === null || struct.refiner(value, ctx)\n  });\n}\n/**\n * Ensure that a value is a number.\n */\n\nfunction number() {\n  return define('number', value => {\n    return typeof value === 'number' && !isNaN(value) || \"Expected a number, but received: \" + print(value);\n  });\n}\nfunction object(schema) {\n  const knowns = schema ? Object.keys(schema) : [];\n  const Never = never();\n  return new Struct({\n    type: 'object',\n    schema: schema ? schema : null,\n\n    *entries(value) {\n      if (schema && isObject(value)) {\n        const unknowns = new Set(Object.keys(value));\n\n        for (const key of knowns) {\n          unknowns.delete(key);\n          yield [key, value[key], schema[key]];\n        }\n\n        for (const key of unknowns) {\n          yield [key, value[key], Never];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    },\n\n    coercer(value) {\n      return isObject(value) ? { ...value\n      } : value;\n    }\n\n  });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\n\nfunction optional(struct) {\n  return new Struct({ ...struct,\n    validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx)\n  });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\n\nfunction record(Key, Value) {\n  return new Struct({\n    type: 'record',\n    schema: null,\n\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k in value) {\n          const v = value[k];\n          yield [k, k, Key];\n          yield [k, v, Value];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\n\nfunction regexp() {\n  return define('regexp', value => {\n    return value instanceof RegExp;\n  });\n}\nfunction set(Element) {\n  return new Struct({\n    type: 'set',\n    schema: null,\n\n    *entries(value) {\n      if (Element && value instanceof Set) {\n        for (const v of value) {\n          yield [v, v, Element];\n        }\n      }\n    },\n\n    coercer(value) {\n      return value instanceof Set ? new Set(value) : value;\n    },\n\n    validator(value) {\n      return value instanceof Set || \"Expected a `Set` object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value is a string.\n */\n\nfunction string() {\n  return define('string', value => {\n    return typeof value === 'string' || \"Expected a string, but received: \" + print(value);\n  });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\n\nfunction tuple(Structs) {\n  const Never = never();\n  return new Struct({\n    type: 'tuple',\n    schema: null,\n\n    *entries(value) {\n      if (Array.isArray(value)) {\n        const length = Math.max(Structs.length, value.length);\n\n        for (let i = 0; i < length; i++) {\n          yield [i, value[i], Structs[i] || Never];\n        }\n      }\n    },\n\n    validator(value) {\n      return Array.isArray(value) || \"Expected an array, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\n\nfunction type(schema) {\n  const keys = Object.keys(schema);\n  return new Struct({\n    type: 'type',\n    schema,\n\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k of keys) {\n          yield [k, value[k], schema[k]];\n        }\n      }\n    },\n\n    validator(value) {\n      return isObject(value) || \"Expected an object, but received: \" + print(value);\n    }\n\n  });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\n\nfunction union(Structs) {\n  const description = Structs.map(s => s.type).join(' | ');\n  return new Struct({\n    type: 'union',\n    schema: null,\n\n    coercer(value, ctx) {\n      const firstMatch = Structs.find(s => {\n        const [e] = s.validate(value, {\n          coerce: true\n        });\n        return !e;\n      }) || unknown();\n      return firstMatch.coercer(value, ctx);\n    },\n\n    validator(value, ctx) {\n      const failures = [];\n\n      for (const S of Structs) {\n        const [...tuples] = run(value, S, ctx);\n        const [first] = tuples;\n\n        if (!first[0]) {\n          return [];\n        } else {\n          for (const [failure] of tuples) {\n            if (failure) {\n              failures.push(failure);\n            }\n          }\n        }\n      }\n\n      return [\"Expected the value to satisfy a union of `\" + description + \"`, but received: \" + print(value), ...failures];\n    }\n\n  });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\n\nfunction unknown() {\n  return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction coerce(struct, condition, coercer) {\n  return new Struct({ ...struct,\n    coercer: (value, ctx) => {\n      return is(value, condition) ? struct.coercer(coercer(value, ctx), ctx) : struct.coercer(value, ctx);\n    }\n  });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction defaulted(struct, fallback, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  return coerce(struct, unknown(), x => {\n    const f = typeof fallback === 'function' ? fallback() : fallback;\n\n    if (x === undefined) {\n      return f;\n    }\n\n    if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n      const ret = { ...x\n      };\n      let changed = false;\n\n      for (const key in f) {\n        if (ret[key] === undefined) {\n          ret[key] = f[key];\n          changed = true;\n        }\n      }\n\n      if (changed) {\n        return ret;\n      }\n    }\n\n    return x;\n  });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nfunction trimmed(struct) {\n  return coerce(struct, string(), x => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\n\nfunction empty(struct) {\n  return refine(struct, 'empty', value => {\n    const size = getSize(value);\n    return size === 0 || \"Expected an empty \" + struct.type + \" but received one with a size of `\" + size + \"`\";\n  });\n}\n\nfunction getSize(value) {\n  if (value instanceof Map || value instanceof Set) {\n    return value.size;\n  } else {\n    return value.length;\n  }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\n\n\nfunction max(struct, threshold, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    exclusive\n  } = options;\n  return refine(struct, 'max', value => {\n    return exclusive ? value < threshold : value <= threshold || \"Expected a \" + struct.type + \" less than \" + (exclusive ? '' : 'or equal to ') + threshold + \" but received `\" + value + \"`\";\n  });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\n\nfunction min(struct, threshold, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  const {\n    exclusive\n  } = options;\n  return refine(struct, 'min', value => {\n    return exclusive ? value > threshold : value >= threshold || \"Expected a \" + struct.type + \" greater than \" + (exclusive ? '' : 'or equal to ') + threshold + \" but received `\" + value + \"`\";\n  });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\n\nfunction nonempty(struct) {\n  return refine(struct, 'nonempty', value => {\n    const size = getSize(value);\n    return size > 0 || \"Expected a nonempty \" + struct.type + \" but received an empty one\";\n  });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\n\nfunction pattern(struct, regexp) {\n  return refine(struct, 'pattern', value => {\n    return regexp.test(value) || \"Expected a \" + struct.type + \" matching `/\" + regexp.source + \"/` but received \\\"\" + value + \"\\\"\";\n  });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\n\nfunction size(struct, min, max) {\n  if (max === void 0) {\n    max = min;\n  }\n\n  const expected = \"Expected a \" + struct.type;\n  const of = min === max ? \"of `\" + min + \"`\" : \"between `\" + min + \"` and `\" + max + \"`\";\n  return refine(struct, 'size', value => {\n    if (typeof value === 'number' || value instanceof Date) {\n      return min <= value && value <= max || expected + \" \" + of + \" but received `\" + value + \"`\";\n    } else if (value instanceof Map || value instanceof Set) {\n      const {\n        size\n      } = value;\n      return min <= size && size <= max || expected + \" with a size \" + of + \" but received one with a size of `\" + size + \"`\";\n    } else {\n      const {\n        length\n      } = value;\n      return min <= length && length <= max || expected + \" with a length \" + of + \" but received one with a length of `\" + length + \"`\";\n    }\n  });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\n\nfunction refine(struct, name, refiner) {\n  return new Struct({ ...struct,\n\n    *refiner(value, ctx) {\n      yield* struct.refiner(value, ctx);\n      const result = refiner(value, ctx);\n      const failures = toFailures(result, ctx, struct, value);\n\n      for (const failure of failures) {\n        yield { ...failure,\n          refinement: name\n        };\n      }\n    }\n\n  });\n}\n\nexport { Struct, StructError, any, array, assert, assign, bigint, boolean, coerce, create, date, defaulted, define, deprecated, dynamic, empty, enums, func, instance, integer, intersection, is, lazy, literal, map, mask, max, min, never, nonempty, nullable, number, object, omit, optional, partial, pattern, pick, record, refine, regexp, set, size, string, struct, trimmed, tuple, type, union, unknown, validate };\n//# sourceMappingURL=index.es.js.map\n", null, null, null, null, "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nvar SUPPORTED_LOCALE = {\n    tr: {\n        regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n        map: {\n            İ: \"\\u0069\",\n            I: \"\\u0131\",\n            İ: \"\\u0069\",\n        },\n    },\n    az: {\n        regexp: /\\u0130/g,\n        map: {\n            İ: \"\\u0069\",\n            I: \"\\u0131\",\n            İ: \"\\u0069\",\n        },\n    },\n    lt: {\n        regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n        map: {\n            I: \"\\u0069\\u0307\",\n            J: \"\\u006A\\u0307\",\n            Į: \"\\u012F\\u0307\",\n            Ì: \"\\u0069\\u0307\\u0300\",\n            Í: \"\\u0069\\u0307\\u0301\",\n            Ĩ: \"\\u0069\\u0307\\u0303\",\n        },\n    },\n};\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str, locale) {\n    var lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n    if (lang)\n        return lowerCase(str.replace(lang.regexp, function (m) { return lang.map[m]; }));\n    return lowerCase(str);\n}\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str) {\n    return str.toLowerCase();\n}\n//# sourceMappingURL=index.js.map", "import { lowerCase } from \"lower-case\";\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nvar DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n// Remove all non-word characters.\nvar DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input, options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.splitRegexp, splitRegexp = _a === void 0 ? DEFAULT_SPLIT_REGEXP : _a, _b = options.stripRegexp, stripRegexp = _b === void 0 ? DEFAULT_STRIP_REGEXP : _b, _c = options.transform, transform = _c === void 0 ? lowerCase : _c, _d = options.delimiter, delimiter = _d === void 0 ? \" \" : _d;\n    var result = replace(replace(input, splitRegexp, \"$1\\0$2\"), stripRegexp, \"\\0\");\n    var start = 0;\n    var end = result.length;\n    // Trim the delimiter from around the output string.\n    while (result.charAt(start) === \"\\0\")\n        start++;\n    while (result.charAt(end - 1) === \"\\0\")\n        end--;\n    // Transform each token independently.\n    return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input, re, value) {\n    if (re instanceof RegExp)\n        return input.replace(re, value);\n    return re.reduce(function (input, re) { return input.replace(re, value); }, input);\n}\n//# sourceMappingURL=index.js.map", "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nexport function dotCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return noCase(input, __assign({ delimiter: \".\" }, options));\n}\n//# sourceMappingURL=index.js.map", "import { __assign } from \"tslib\";\nimport { dotCase } from \"dot-case\";\nexport function snakeCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return dotCase(input, __assign({ delimiter: \"_\" }, options));\n}\n//# sourceMappingURL=index.js.map", null, null, null, null, null, null, null, null, "/* The MIT License (MIT)\n *\n * Copyright 2015-2018 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * Support for translating between Buffer instances and JavaScript\n * native types.\n *\n * {@link module:Layout~Layout|Layout} is the basis of a class\n * hierarchy that associates property names with sequences of encoded\n * bytes.\n *\n * Layouts are supported for these scalar (numeric) types:\n * * {@link module:Layout~UInt|Unsigned integers in little-endian\n *   format} with {@link module:Layout.u8|8-bit}, {@link\n *   module:Layout.u16|16-bit}, {@link module:Layout.u24|24-bit},\n *   {@link module:Layout.u32|32-bit}, {@link\n *   module:Layout.u40|40-bit}, and {@link module:Layout.u48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~UIntBE|Unsigned integers in big-endian\n *   format} with {@link module:Layout.u16be|16-bit}, {@link\n *   module:Layout.u24be|24-bit}, {@link module:Layout.u32be|32-bit},\n *   {@link module:Layout.u40be|40-bit}, and {@link\n *   module:Layout.u48be|48-bit} representation ranges;\n * * {@link module:Layout~Int|Signed integers in little-endian\n *   format} with {@link module:Layout.s8|8-bit}, {@link\n *   module:Layout.s16|16-bit}, {@link module:Layout.s24|24-bit},\n *   {@link module:Layout.s32|32-bit}, {@link\n *   module:Layout.s40|40-bit}, and {@link module:Layout.s48|48-bit}\n *   representation ranges;\n * * {@link module:Layout~IntBE|Signed integers in big-endian format}\n *   with {@link module:Layout.s16be|16-bit}, {@link\n *   module:Layout.s24be|24-bit}, {@link module:Layout.s32be|32-bit},\n *   {@link module:Layout.s40be|40-bit}, and {@link\n *   module:Layout.s48be|48-bit} representation ranges;\n * * 64-bit integral values that decode to an exact (if magnitude is\n *   less than 2^53) or nearby integral Number in {@link\n *   module:Layout.nu64|unsigned little-endian}, {@link\n *   module:Layout.nu64be|unsigned big-endian}, {@link\n *   module:Layout.ns64|signed little-endian}, and {@link\n *   module:Layout.ns64be|unsigned big-endian} encodings;\n * * 32-bit floating point values with {@link\n *   module:Layout.f32|little-endian} and {@link\n *   module:Layout.f32be|big-endian} representations;\n * * 64-bit floating point values with {@link\n *   module:Layout.f64|little-endian} and {@link\n *   module:Layout.f64be|big-endian} representations;\n * * {@link module:Layout.const|Constants} that take no space in the\n *   encoded expression.\n *\n * and for these aggregate types:\n * * {@link module:Layout.seq|Sequence}s of instances of a {@link\n *   module:Layout~Layout|Layout}, with JavaScript representation as\n *   an Array and constant or data-dependent {@link\n *   module:Layout~Sequence#count|length};\n * * {@link module:Layout.struct|Structure}s that aggregate a\n *   heterogeneous sequence of {@link module:Layout~Layout|Layout}\n *   instances, with JavaScript representation as an Object;\n * * {@link module:Layout.union|Union}s that support multiple {@link\n *   module:Layout~VariantLayout|variant layouts} over a fixed\n *   (padded) or variable (not padded) span of bytes, using an\n *   unsigned integer at the start of the data or a separate {@link\n *   module:Layout.unionLayoutDiscriminator|layout element} to\n *   determine which layout to use when interpreting the buffer\n *   contents;\n * * {@link module:Layout.bits|BitStructure}s that contain a sequence\n *   of individual {@link\n *   module:Layout~BitStructure#addField|BitField}s packed into an 8,\n *   16, 24, or 32-bit unsigned integer starting at the least- or\n *   most-significant bit;\n * * {@link module:Layout.cstr|C strings} of varying length;\n * * {@link module:Layout.blob|Blobs} of fixed- or variable-{@link\n *   module:Layout~Blob#length|length} raw data.\n *\n * All {@link module:Layout~Layout|Layout} instances are immutable\n * after construction, to prevent internal state from becoming\n * inconsistent.\n *\n * @local Layout\n * @local ExternalLayout\n * @local GreedyCount\n * @local OffsetLayout\n * @local UInt\n * @local UIntBE\n * @local Int\n * @local IntBE\n * @local NearUInt64\n * @local NearUInt64BE\n * @local NearInt64\n * @local NearInt64BE\n * @local Float\n * @local FloatBE\n * @local Double\n * @local DoubleBE\n * @local Sequence\n * @local Structure\n * @local UnionDiscriminator\n * @local UnionLayoutDiscriminator\n * @local Union\n * @local VariantLayout\n * @local BitStructure\n * @local BitField\n * @local Boolean\n * @local Blob\n * @local CString\n * @local Constant\n * @local bindConstructorLayout\n * @module Layout\n * @license MIT\n * <AUTHOR> A. Bigot\n * @see {@link https://github.com/pabigot/buffer-layout|buffer-layout on GitHub}\n */\n\n'use strict';\n\n/**\n * Base class for layout objects.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * Layout#encode|encode} or {@link Layout#decode|decode} functions.\n *\n * @param {Number} span - Initializer for {@link Layout#span|span}.  The\n * parameter must be an integer; a negative value signifies that the\n * span is {@link Layout#getSpan|value-specific}.\n *\n * @param {string} [property] - Initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n */\nclass Layout {\n  constructor(span, property) {\n    if (!Number.isInteger(span)) {\n      throw new TypeError('span must be an integer');\n    }\n\n    /** The span of the layout in bytes.\n     *\n     * Positive values are generally expected.\n     *\n     * Zero will only appear in {@link Constant}s and in {@link\n     * Sequence}s where the {@link Sequence#count|count} is zero.\n     *\n     * A negative value indicates that the span is value-specific, and\n     * must be obtained using {@link Layout#getSpan|getSpan}. */\n    this.span = span;\n\n    /** The property name used when this layout is represented in an\n     * Object.\n     *\n     * Used only for layouts that {@link Layout#decode|decode} to Object\n     * instances.  If left undefined the span of the unnamed layout will\n     * be treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Function to create an Object into which decoded properties will\n   * be written.\n   *\n   * Used only for layouts that {@link Layout#decode|decode} to Object\n   * instances, which means:\n   * * {@link Structure}\n   * * {@link Union}\n   * * {@link VariantLayout}\n   * * {@link BitStructure}\n   *\n   * If left undefined the JavaScript representation of these layouts\n   * will be Object instances.\n   *\n   * See {@link bindConstructorLayout}.\n   */\n  makeDestinationObject() {\n    return {};\n  }\n\n  /**\n   * Decode from a Buffer into an JavaScript value.\n   *\n   * @param {Buffer} b - the buffer from which encoded data is read.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {(Number|Array|Object)} - the value of the decoded data.\n   *\n   * @abstract\n   */\n  decode(b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Encode a JavaScript value into a Buffer.\n   *\n   * @param {(Number|Array|Object)} src - the value to be encoded into\n   * the buffer.  The type accepted depends on the (sub-)type of {@link\n   * Layout}.\n   *\n   * @param {Buffer} b - the buffer into which encoded data will be\n   * written.\n   *\n   * @param {Number} [offset] - the offset at which the encoded data\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @returns {Number} - the number of bytes encoded, including the\n   * space skipped for internal padding, but excluding data such as\n   * {@link Sequence#count|lengths} when stored {@link\n   * ExternalLayout|externally}.  This is the adjustment to `offset`\n   * producing the offset where data for the next layout would be\n   * written.\n   *\n   * @abstract\n   */\n  encode(src, b, offset) {\n    throw new Error('Layout is abstract');\n  }\n\n  /**\n   * Calculate the span of a specific instance of a layout.\n   *\n   * @param {Buffer} b - the buffer that contains an encoded instance.\n   *\n   * @param {Number} [offset] - the offset at which the encoded instance\n   * starts.  If absent a zero offset is inferred.\n   *\n   * @return {Number} - the number of bytes covered by the layout\n   * instance.  If this method is not overridden in a subclass the\n   * definition-time constant {@link Layout#span|span} will be\n   * returned.\n   *\n   * @throws {RangeError} - if the length of the value cannot be\n   * determined.\n   */\n  getSpan(b, offset) {\n    if (0 > this.span) {\n      throw new RangeError('indeterminate span');\n    }\n    return this.span;\n  }\n\n  /**\n   * Replicate the layout using a new property.\n   *\n   * This function must be used to get a structurally-equivalent layout\n   * with a different name since all {@link Layout} instances are\n   * immutable.\n   *\n   * **NOTE** This is a shallow copy.  All fields except {@link\n   * Layout#property|property} are strictly equal to the origin layout.\n   *\n   * @param {String} property - the value for {@link\n   * Layout#property|property} in the replica.\n   *\n   * @returns {Layout} - the copy with {@link Layout#property|property}\n   * set to `property`.\n   */\n  replicate(property) {\n    const rv = Object.create(this.constructor.prototype);\n    Object.assign(rv, this);\n    rv.property = property;\n    return rv;\n  }\n\n  /**\n   * Create an object from layout properties and an array of values.\n   *\n   * **NOTE** This function returns `undefined` if invoked on a layout\n   * that does not return its value as an Object.  Objects are\n   * returned for things that are a {@link Structure}, which includes\n   * {@link VariantLayout|variant layouts} if they are structures, and\n   * excludes {@link Union}s.  If you want this feature for a union\n   * you must use {@link Union.getVariant|getVariant} to select the\n   * desired layout.\n   *\n   * @param {Array} values - an array of values that correspond to the\n   * default order for properties.  As with {@link Layout#decode|decode}\n   * layout elements that have no property name are skipped when\n   * iterating over the array values.  Only the top-level properties are\n   * assigned; arguments are not assigned to properties of contained\n   * layouts.  Any unused values are ignored.\n   *\n   * @return {(Object|undefined)}\n   */\n  fromArray(values) {\n    return undefined;\n  }\n}\nexports.Layout = Layout;\n\n/* Provide text that carries a name (such as for a function that will\n * be throwing an error) annotated with the property of a given layout\n * (such as one for which the value was unacceptable).\n *\n * @ignore */\nfunction nameWithProperty(name, lo) {\n  if (lo.property) {\n    return name + '[' + lo.property + ']';\n  }\n  return name;\n}\nexports.nameWithProperty = nameWithProperty;\n\n/**\n * Augment a class so that instances can be encoded/decoded using a\n * given layout.\n *\n * Calling this function couples `Class` with `layout` in several ways:\n *\n * * `Class.layout_` becomes a static member property equal to `layout`;\n * * `layout.boundConstructor_` becomes a static member property equal\n *    to `Class`;\n * * The {@link Layout#makeDestinationObject|makeDestinationObject()}\n *   property of `layout` is set to a function that returns a `new\n *   Class()`;\n * * `Class.decode(b, offset)` becomes a static member function that\n *   delegates to {@link Layout#decode|layout.decode}.  The\n *   synthesized function may be captured and extended.\n * * `Class.prototype.encode(b, offset)` provides an instance member\n *   function that delegates to {@link Layout#encode|layout.encode}\n *   with `src` set to `this`.  The synthesized function may be\n *   captured and extended, but when the extension is invoked `this`\n *   must be explicitly bound to the instance.\n *\n * @param {class} Class - a JavaScript class with a nullary\n * constructor.\n *\n * @param {Layout} layout - the {@link Layout} instance used to encode\n * instances of `Class`.\n */\nfunction bindConstructorLayout(Class, layout) {\n  if ('function' !== typeof Class) {\n    throw new TypeError('Class must be constructor');\n  }\n  if (Class.hasOwnProperty('layout_')) {\n    throw new Error('Class is already bound to a layout');\n  }\n  if (!(layout && (layout instanceof Layout))) {\n    throw new TypeError('layout must be a Layout');\n  }\n  if (layout.hasOwnProperty('boundConstructor_')) {\n    throw new Error('layout is already bound to a constructor');\n  }\n  Class.layout_ = layout;\n  layout.boundConstructor_ = Class;\n  layout.makeDestinationObject = (() => new Class());\n  Object.defineProperty(Class.prototype, 'encode', {\n    value: function(b, offset) {\n      return layout.encode(this, b, offset);\n    },\n    writable: true,\n  });\n  Object.defineProperty(Class, 'decode', {\n    value: function(b, offset) {\n      return layout.decode(b, offset);\n    },\n    writable: true,\n  });\n}\nexports.bindConstructorLayout = bindConstructorLayout;\n\n/**\n * An object that behaves like a layout but does not consume space\n * within its containing layout.\n *\n * This is primarily used to obtain metadata about a member, such as a\n * {@link OffsetLayout} that can provide data about a {@link\n * Layout#getSpan|value-specific span}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support {@link\n * ExternalLayout#isCount|isCount} or other {@link Layout} functions.\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @abstract\n * @augments {Layout}\n */\nclass ExternalLayout extends Layout {\n  /**\n   * Return `true` iff the external layout decodes to an unsigned\n   * integer layout.\n   *\n   * In that case it can be used as the source of {@link\n   * Sequence#count|Sequence counts}, {@link Blob#length|Blob lengths},\n   * or as {@link UnionLayoutDiscriminator#layout|external union\n   * discriminators}.\n   *\n   * @abstract\n   */\n  isCount() {\n    throw new Error('ExternalLayout is abstract');\n  }\n}\n\n/**\n * An {@link ExternalLayout} that determines its {@link\n * Layout#decode|value} based on offset into and length of the buffer\n * on which it is invoked.\n *\n * *Factory*: {@link module:Layout.greedy|greedy}\n *\n * @param {Number} [elementSpan] - initializer for {@link\n * GreedyCount#elementSpan|elementSpan}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {ExternalLayout}\n */\nclass GreedyCount extends ExternalLayout {\n  constructor(elementSpan, property) {\n    if (undefined === elementSpan) {\n      elementSpan = 1;\n    }\n    if ((!Number.isInteger(elementSpan)) || (0 >= elementSpan)) {\n      throw new TypeError('elementSpan must be a (positive) integer');\n    }\n    super(-1, property);\n\n    /** The layout for individual elements of the sequence.  The value\n     * must be a positive integer.  If not provided, the value will be\n     * 1. */\n    this.elementSpan = elementSpan;\n  }\n\n  /** @override */\n  isCount() {\n    return true;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rem = b.length - offset;\n    return Math.floor(rem / this.elementSpan);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    return 0;\n  }\n}\n\n/**\n * An {@link ExternalLayout} that supports accessing a {@link Layout}\n * at a fixed offset from the start of another Layout.  The offset may\n * be before, within, or after the base layout.\n *\n * *Factory*: {@link module:Layout.offset|offset}\n *\n * @param {Layout} layout - initializer for {@link\n * OffsetLayout#layout|layout}, modulo `property`.\n *\n * @param {Number} [offset] - Initializes {@link\n * OffsetLayout#offset|offset}.  Defaults to zero.\n *\n * @param {string} [property] - Optional new property name for a\n * {@link Layout#replicate| replica} of `layout` to be used as {@link\n * OffsetLayout#layout|layout}.  If not provided the `layout` is used\n * unchanged.\n *\n * @augments {Layout}\n */\nclass OffsetLayout extends ExternalLayout {\n  constructor(layout, offset, property) {\n    if (!(layout instanceof Layout)) {\n      throw new TypeError('layout must be a Layout');\n    }\n\n    if (undefined === offset) {\n      offset = 0;\n    } else if (!Number.isInteger(offset)) {\n      throw new TypeError('offset must be integer or undefined');\n    }\n\n    super(layout.span, property || layout.property);\n\n    /** The subordinated layout. */\n    this.layout = layout;\n\n    /** The location of {@link OffsetLayout#layout} relative to the\n     * start of another layout.\n     *\n     * The value may be positive or negative, but an error will thrown\n     * if at the point of use it goes outside the span of the Buffer\n     * being accessed.  */\n    this.offset = offset;\n  }\n\n  /** @override */\n  isCount() {\n    return ((this.layout instanceof UInt)\n            || (this.layout instanceof UIntBE));\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.decode(b, offset + this.offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return this.layout.encode(src, b, offset + this.offset);\n  }\n}\n\n/**\n * Represent an unsigned integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.u8|u8}, {@link\n *  module:Layout.u16|u16}, {@link module:Layout.u24|u24}, {@link\n *  module:Layout.u32|u32}, {@link module:Layout.u40|u40}, {@link\n *  module:Layout.u48|u48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UInt extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent an unsigned integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.u8be|u8be}, {@link\n * module:Layout.u16be|u16be}, {@link module:Layout.u24be|u24be},\n * {@link module:Layout.u32be|u32be}, {@link\n * module:Layout.u40be|u40be}, {@link module:Layout.u48be|u48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UIntBE extends Layout {\n  constructor(span, property) {\n    super( span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readUIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeUIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in little-endian format.\n *\n * *Factory*: {@link module:Layout.s8|s8}, {@link\n *  module:Layout.s16|s16}, {@link module:Layout.s24|s24}, {@link\n *  module:Layout.s32|s32}, {@link module:Layout.s40|s40}, {@link\n *  module:Layout.s48|s48}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Int extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntLE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntLE(src, offset, this.span);\n    return this.span;\n  }\n}\n\n/**\n * Represent a signed integer in big-endian format.\n *\n * *Factory*: {@link module:Layout.s8be|s8be}, {@link\n * module:Layout.s16be|s16be}, {@link module:Layout.s24be|s24be},\n * {@link module:Layout.s32be|s32be}, {@link\n * module:Layout.s40be|s40be}, {@link module:Layout.s48be|s48be}\n *\n * @param {Number} span - initializer for {@link Layout#span|span}.\n * The parameter can range from 1 through 6.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass IntBE extends Layout {\n  constructor(span, property) {\n    super(span, property);\n    if (6 < this.span) {\n      throw new RangeError('span must not exceed 6 bytes');\n    }\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readIntBE(offset, this.span);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeIntBE(src, offset, this.span);\n    return this.span;\n  }\n}\n\nconst V2E32 = Math.pow(2, 32);\n\n/* True modulus high and low 32-bit words, where low word is always\n * non-negative. */\nfunction divmodInt64(src) {\n  const hi32 = Math.floor(src / V2E32);\n  const lo32 = src - (hi32 * V2E32);\n  return {hi32, lo32};\n}\n/* Reconstruct Number from quotient and non-negative remainder */\nfunction roundedInt64(hi32, lo32) {\n  return hi32 * V2E32 + lo32;\n}\n\n/**\n * Represent an unsigned 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64|nu64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readUInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeUInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent an unsigned 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.nu64be|nu64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearUInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readUInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in little-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64|ns64}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64 extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const lo32 = b.readUInt32LE(offset);\n    const hi32 = b.readInt32LE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeUInt32LE(split.lo32, offset);\n    b.writeInt32LE(split.hi32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a signed 64-bit integer in big-endian format when\n * encoded and as a near integral JavaScript Number when decoded.\n *\n * *Factory*: {@link module:Layout.ns64be|ns64be}\n *\n * **NOTE** Values with magnitude greater than 2^52 may not decode to\n * the exact value of the encoded representation.\n *\n * @augments {Layout}\n */\nclass NearInt64BE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const hi32 = b.readInt32BE(offset);\n    const lo32 = b.readUInt32BE(offset + 4);\n    return roundedInt64(hi32, lo32);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const split = divmodInt64(src);\n    b.writeInt32BE(split.hi32, offset);\n    b.writeUInt32BE(split.lo32, offset + 4);\n    return 8;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f32|f32}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Float extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatLE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 32-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f32be|f32be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass FloatBE extends Layout {\n  constructor(property) {\n    super(4, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readFloatBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeFloatBE(src, offset);\n    return 4;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in little-endian format.\n *\n * *Factory*: {@link module:Layout.f64|f64}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Double extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleLE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleLE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a 64-bit floating point number in big-endian format.\n *\n * *Factory*: {@link module:Layout.f64be|f64be}\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass DoubleBE extends Layout {\n  constructor(property) {\n    super(8, property);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.readDoubleBE(offset);\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    b.writeDoubleBE(src, offset);\n    return 8;\n  }\n}\n\n/**\n * Represent a contiguous sequence of a specific layout as an Array.\n *\n * *Factory*: {@link module:Layout.seq|seq}\n *\n * @param {Layout} elementLayout - initializer for {@link\n * Sequence#elementLayout|elementLayout}.\n *\n * @param {(Number|ExternalLayout)} count - initializer for {@link\n * Sequence#count|count}.  The parameter must be either a positive\n * integer or an instance of {@link ExternalLayout}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Sequence extends Layout {\n  constructor(elementLayout, count, property) {\n    if (!(elementLayout instanceof Layout)) {\n      throw new TypeError('elementLayout must be a Layout');\n    }\n    if (!(((count instanceof ExternalLayout) && count.isCount())\n          || (Number.isInteger(count) && (0 <= count)))) {\n      throw new TypeError('count must be non-negative integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n    let span = -1;\n    if ((!(count instanceof ExternalLayout))\n        && (0 < elementLayout.span)) {\n      span = count * elementLayout.span;\n    }\n\n    super(span, property);\n\n    /** The layout for individual elements of the sequence. */\n    this.elementLayout = elementLayout;\n\n    /** The number of elements in the sequence.\n     *\n     * This will be either a non-negative integer or an instance of\n     * {@link ExternalLayout} for which {@link\n     * ExternalLayout#isCount|isCount()} is `true`. */\n    this.count = count;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    if (0 < this.elementLayout.span) {\n      span = count * this.elementLayout.span;\n    } else {\n      let idx = 0;\n      while (idx < count) {\n        span += this.elementLayout.getSpan(b, offset + span);\n        ++idx;\n      }\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const rv = [];\n    let i = 0;\n    let count = this.count;\n    if (count instanceof ExternalLayout) {\n      count = count.decode(b, offset);\n    }\n    while (i < count) {\n      rv.push(this.elementLayout.decode(b, offset));\n      offset += this.elementLayout.getSpan(b, offset);\n      i += 1;\n    }\n    return rv;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Sequence}.\n   *\n   * **NOTE** If `src` is shorter than {@link Sequence#count|count} then\n   * the unused space in the buffer is left unchanged.  If `src` is\n   * longer than {@link Sequence#count|count} the unneeded elements are\n   * ignored.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const elo = this.elementLayout;\n    const span = src.reduce((span, v) => {\n      return span + elo.encode(v, b, offset + span);\n    }, 0);\n    if (this.count instanceof ExternalLayout) {\n      this.count.encode(src.length, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Represent a contiguous sequence of arbitrary layout elements as an\n * Object.\n *\n * *Factory*: {@link module:Layout.struct|struct}\n *\n * **NOTE** The {@link Layout#span|span} of the structure is variable\n * if any layout in {@link Structure#fields|fields} has a variable\n * span.  When {@link Layout#encode|encoding} we must have a value for\n * all variable-length fields, or we wouldn't be able to figure out\n * how much space to use for storage.  We can only identify the value\n * for a field when it has a {@link Layout#property|property}.  As\n * such, although a structure may contain both unnamed fields and\n * variable-length fields, it cannot contain an unnamed\n * variable-length field.\n *\n * @param {Layout[]} fields - initializer for {@link\n * Structure#fields|fields}.  An error is raised if this contains a\n * variable-length field for which a {@link Layout#property|property}\n * is not defined.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @param {Boolean} [decodePrefixes] - initializer for {@link\n * Structure#decodePrefixes|property}.\n *\n * @throws {Error} - if `fields` contains an unnamed variable-length\n * layout.\n *\n * @augments {Layout}\n */\nclass Structure extends Layout {\n  constructor(fields, property, decodePrefixes) {\n    if (!(Array.isArray(fields)\n          && fields.reduce((acc, v) => acc && (v instanceof Layout), true))) {\n      throw new TypeError('fields must be array of Layout instances');\n    }\n    if (('boolean' === typeof property)\n        && (undefined === decodePrefixes)) {\n      decodePrefixes = property;\n      property = undefined;\n    }\n\n    /* Verify absence of unnamed variable-length fields. */\n    for (const fd of fields) {\n      if ((0 > fd.span)\n          && (undefined === fd.property)) {\n        throw new Error('fields cannot contain unnamed variable-length layout');\n      }\n    }\n\n    let span = -1;\n    try {\n      span = fields.reduce((span, fd) => span + fd.getSpan(), 0);\n    } catch (e) {\n    }\n    super(span, property);\n\n    /** The sequence of {@link Layout} values that comprise the\n     * structure.\n     *\n     * The individual elements need not be the same type, and may be\n     * either scalar or aggregate layouts.  If a member layout leaves\n     * its {@link Layout#property|property} undefined the\n     * corresponding region of the buffer associated with the element\n     * will not be mutated.\n     *\n     * @type {Layout[]} */\n    this.fields = fields;\n\n    /** Control behavior of {@link Layout#decode|decode()} given short\n     * buffers.\n     *\n     * In some situations a structure many be extended with additional\n     * fields over time, with older installations providing only a\n     * prefix of the full structure.  If this property is `true`\n     * decoding will accept those buffers and leave subsequent fields\n     * undefined, as long as the buffer ends at a field boundary.\n     * Defaults to `false`. */\n    this.decodePrefixes = !!decodePrefixes;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = 0;\n    try {\n      span = this.fields.reduce((span, fd) => {\n        const fsp = fd.getSpan(b, offset);\n        offset += fsp;\n        return span + fsp;\n      }, 0);\n    } catch (e) {\n      throw new RangeError('indeterminate span');\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(b, offset);\n      }\n      offset += fd.getSpan(b, offset);\n      if (this.decodePrefixes\n          && (b.length === offset)) {\n        break;\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Structure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the buffer is\n   * left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const firstOffset = offset;\n    let lastOffset = 0;\n    let lastWrote = 0;\n    for (const fd of this.fields) {\n      let span = fd.span;\n      lastWrote = (0 < span) ? span : 0;\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          lastWrote = fd.encode(fv, b, offset);\n          if (0 > span) {\n            /* Read the as-encoded span, which is not necessarily the\n             * same as what we wrote. */\n            span = fd.getSpan(b, offset);\n          }\n        }\n      }\n      lastOffset = offset;\n      offset += span;\n    }\n    /* Use (lastOffset + lastWrote) instead of offset because the last\n     * item may have had a dynamic length and we don't want to include\n     * the padding between it and the end of the space reserved for\n     * it. */\n    return (lastOffset + lastWrote) - firstOffset;\n  }\n\n  /** @override */\n  fromArray(values) {\n    const dest = this.makeDestinationObject();\n    for (const fd of this.fields) {\n      if ((undefined !== fd.property)\n          && (0 < values.length)) {\n        dest[fd.property] = values.shift();\n      }\n    }\n    return dest;\n  }\n\n  /**\n   * Get access to the layout of a given property.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Layout} - the layout associated with `property`, or\n   * undefined if there is no such property.\n   */\n  layoutFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n\n  /**\n   * Get the offset of a structure member.\n   *\n   * @param {String} property - the structure member of interest.\n   *\n   * @return {Number} - the offset in bytes to the start of `property`\n   * within the structure, or undefined if `property` is not a field\n   * within the structure.  If the property is a member but follows a\n   * variable-length structure member a negative number will be\n   * returned.\n   */\n  offsetOf(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    let offset = 0;\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return offset;\n      }\n      if (0 > fd.span) {\n        offset = -1;\n      } else if (0 <= offset) {\n        offset += fd.span;\n      }\n    }\n  }\n}\n\n/**\n * An object that can provide a {@link\n * Union#discriminator|discriminator} API for {@link Union}.\n *\n * **NOTE** This is an abstract base class; you can create instances\n * if it amuses you, but they won't support the {@link\n * UnionDiscriminator#encode|encode} or {@link\n * UnionDiscriminator#decode|decode} functions.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}.\n *\n * @abstract\n */\nclass UnionDiscriminator {\n  constructor(property) {\n    /** The {@link Layout#property|property} to be used when the\n     * discriminator is referenced in isolation (generally when {@link\n     * Union#decode|Union decode} cannot delegate to a specific\n     * variant). */\n    this.property = property;\n  }\n\n  /** Analog to {@link Layout#decode|Layout decode} for union discriminators.\n   *\n   * The implementation of this method need not reference the buffer if\n   * variant information is available through other means. */\n  decode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n\n  /** Analog to {@link Layout#decode|Layout encode} for union discriminators.\n   *\n   * The implementation of this method need not store the value if\n   * variant information is maintained through other means. */\n  encode() {\n    throw new Error('UnionDiscriminator is abstract');\n  }\n}\n\n/**\n * An object that can provide a {@link\n * UnionDiscriminator|discriminator API} for {@link Union} using an\n * unsigned integral {@link Layout} instance located either inside or\n * outside the union.\n *\n * @param {ExternalLayout} layout - initializes {@link\n * UnionLayoutDiscriminator#layout|layout}.  Must satisfy {@link\n * ExternalLayout#isCount|isCount()}.\n *\n * @param {string} [property] - Default for {@link\n * UnionDiscriminator#property|property}, superseding the property\n * from `layout`, but defaulting to `variant` if neither `property`\n * nor layout provide a property name.\n *\n * @augments {UnionDiscriminator}\n */\nclass UnionLayoutDiscriminator extends UnionDiscriminator {\n  constructor(layout, property) {\n    if (!((layout instanceof ExternalLayout)\n          && layout.isCount())) {\n      throw new TypeError('layout must be an unsigned integer ExternalLayout');\n    }\n\n    super(property || layout.property || 'variant');\n\n    /** The {@link ExternalLayout} used to access the discriminator\n     * value. */\n    this.layout = layout;\n  }\n\n  /** Delegate decoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  decode(b, offset) {\n    return this.layout.decode(b, offset);\n  }\n\n  /** Delegate encoding to {@link UnionLayoutDiscriminator#layout|layout}. */\n  encode(src, b, offset) {\n    return this.layout.encode(src, b, offset);\n  }\n}\n\n/**\n * Represent any number of span-compatible layouts.\n *\n * *Factory*: {@link module:Layout.union|union}\n *\n * If the union has a {@link Union#defaultLayout|default layout} that\n * layout must have a non-negative {@link Layout#span|span}.  The span\n * of a fixed-span union includes its {@link\n * Union#discriminator|discriminator} if the variant is a {@link\n * Union#usesPrefixDiscriminator|prefix of the union}, plus the span\n * of its {@link Union#defaultLayout|default layout}.\n *\n * If the union does not have a default layout then the encoded span\n * of the union depends on the encoded span of its variant (which may\n * be fixed or variable).\n *\n * {@link VariantLayout#layout|Variant layout}s are added through\n * {@link Union#addVariant|addVariant}.  If the union has a default\n * layout, the span of the {@link VariantLayout#layout|layout\n * contained by the variant} must not exceed the span of the {@link\n * Union#defaultLayout|default layout} (minus the span of a {@link\n * Union#usesPrefixDiscriminator|prefix disriminator}, if used).  The\n * span of the variant will equal the span of the union itself.\n *\n * The variant for a buffer can only be identified from the {@link\n * Union#discriminator|discriminator} {@link\n * UnionDiscriminator#property|property} (in the case of the {@link\n * Union#defaultLayout|default layout}), or by using {@link\n * Union#getVariant|getVariant} and examining the resulting {@link\n * VariantLayout} instance.\n *\n * A variant compatible with a JavaScript object can be identified\n * using {@link Union#getSourceVariant|getSourceVariant}.\n *\n * @param {(UnionDiscriminator|ExternalLayout|Layout)} discr - How to\n * identify the layout used to interpret the union contents.  The\n * parameter must be an instance of {@link UnionDiscriminator}, an\n * {@link ExternalLayout} that satisfies {@link\n * ExternalLayout#isCount|isCount()}, or {@link UInt} (or {@link\n * UIntBE}).  When a non-external layout element is passed the layout\n * appears at the start of the union.  In all cases the (synthesized)\n * {@link UnionDiscriminator} instance is recorded as {@link\n * Union#discriminator|discriminator}.\n *\n * @param {(Layout|null)} defaultLayout - initializer for {@link\n * Union#defaultLayout|defaultLayout}.  If absent defaults to `null`.\n * If `null` there is no default layout: the union has data-dependent\n * length and attempts to decode or encode unrecognized variants will\n * throw an exception.  A {@link Layout} instance must have a\n * non-negative {@link Layout#span|span}, and if it lacks a {@link\n * Layout#property|property} the {@link\n * Union#defaultLayout|defaultLayout} will be a {@link\n * Layout#replicate|replica} with property `content`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Union extends Layout {\n  constructor(discr, defaultLayout, property) {\n    const upv = ((discr instanceof UInt)\n               || (discr instanceof UIntBE));\n    if (upv) {\n      discr = new UnionLayoutDiscriminator(new OffsetLayout(discr));\n    } else if ((discr instanceof ExternalLayout)\n               && discr.isCount()) {\n      discr = new UnionLayoutDiscriminator(discr);\n    } else if (!(discr instanceof UnionDiscriminator)) {\n      throw new TypeError('discr must be a UnionDiscriminator '\n                          + 'or an unsigned integer layout');\n    }\n    if (undefined === defaultLayout) {\n      defaultLayout = null;\n    }\n    if (!((null === defaultLayout)\n          || (defaultLayout instanceof Layout))) {\n      throw new TypeError('defaultLayout must be null or a Layout');\n    }\n    if (null !== defaultLayout) {\n      if (0 > defaultLayout.span) {\n        throw new Error('defaultLayout must have constant span');\n      }\n      if (undefined === defaultLayout.property) {\n        defaultLayout = defaultLayout.replicate('content');\n      }\n    }\n\n    /* The union span can be estimated only if there's a default\n     * layout.  The union spans its default layout, plus any prefix\n     * variant layout.  By construction both layouts, if present, have\n     * non-negative span. */\n    let span = -1;\n    if (defaultLayout) {\n      span = defaultLayout.span;\n      if ((0 <= span) && upv) {\n        span += discr.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The interface for the discriminator value in isolation.\n     *\n     * This a {@link UnionDiscriminator} either passed to the\n     * constructor or synthesized from the `discr` constructor\n     * argument.  {@link\n     * Union#usesPrefixDiscriminator|usesPrefixDiscriminator} will be\n     * `true` iff the `discr` parameter was a non-offset {@link\n     * Layout} instance. */\n    this.discriminator = discr;\n\n    /** `true` if the {@link Union#discriminator|discriminator} is the\n     * first field in the union.\n     *\n     * If `false` the discriminator is obtained from somewhere\n     * else. */\n    this.usesPrefixDiscriminator = upv;\n\n    /** The layout for non-discriminator content when the value of the\n     * discriminator is not recognized.\n     *\n     * This is the value passed to the constructor.  It is\n     * structurally equivalent to the second component of {@link\n     * Union#layout|layout} but may have a different property\n     * name. */\n    this.defaultLayout = defaultLayout;\n\n    /** A registry of allowed variants.\n     *\n     * The keys are unsigned integers which should be compatible with\n     * {@link Union.discriminator|discriminator}.  The property value\n     * is the corresponding {@link VariantLayout} instances assigned\n     * to this union by {@link Union#addVariant|addVariant}.\n     *\n     * **NOTE** The registry remains mutable so that variants can be\n     * {@link Union#addVariant|added} at any time.  Users should not\n     * manipulate the content of this property. */\n    this.registry = {};\n\n    /* Private variable used when invoking getSourceVariant */\n    let boundGetSourceVariant = this.defaultGetSourceVariant.bind(this);\n\n    /** Function to infer the variant selected by a source object.\n     *\n     * Defaults to {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant} but may\n     * be overridden using {@link\n     * Union#configGetSourceVariant|configGetSourceVariant}.\n     *\n     * @param {Object} src - as with {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * @returns {(undefined|VariantLayout)} The default variant\n     * (`undefined`) or first registered variant that uses a property\n     * available in `src`. */\n    this.getSourceVariant = function(src) {\n      return boundGetSourceVariant(src);\n    };\n\n    /** Function to override the implementation of {@link\n     * Union#getSourceVariant|getSourceVariant}.\n     *\n     * Use this if the desired variant cannot be identified using the\n     * algorithm of {@link\n     * Union#defaultGetSourceVariant|defaultGetSourceVariant}.\n     *\n     * **NOTE** The provided function will be invoked bound to this\n     * Union instance, providing local access to {@link\n     * Union#registry|registry}.\n     *\n     * @param {Function} gsv - a function that follows the API of\n     * {@link Union#defaultGetSourceVariant|defaultGetSourceVariant}. */\n    this.configGetSourceVariant = function(gsv) {\n      boundGetSourceVariant = gsv.bind(this);\n    };\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Default layouts always have non-negative span, so we don't have\n     * one and we have to recognize the variant which will in turn\n     * determine the span. */\n    const vlo = this.getVariant(b, offset);\n    if (!vlo) {\n      throw new Error('unable to determine span for unrecognized variant');\n    }\n    return vlo.getSpan(b, offset);\n  }\n\n  /**\n   * Method to infer a registered Union variant compatible with `src`.\n   *\n   * The first satisified rule in the following sequence defines the\n   * return value:\n   * * If `src` has properties matching the Union discriminator and\n   *   the default layout, `undefined` is returned regardless of the\n   *   value of the discriminator property (this ensures the default\n   *   layout will be used);\n   * * If `src` has a property matching the Union discriminator, the\n   *   value of the discriminator identifies a registered variant, and\n   *   either (a) the variant has no layout, or (b) `src` has the\n   *   variant's property, then the variant is returned (because the\n   *   source satisfies the constraints of the variant it identifies);\n   * * If `src` does not have a property matching the Union\n   *   discriminator, but does have a property matching a registered\n   *   variant, then the variant is returned (because the source\n   *   matches a variant without an explicit conflict);\n   * * An error is thrown (because we either can't identify a variant,\n   *   or we were explicitly told the variant but can't satisfy it).\n   *\n   * @param {Object} src - an object presumed to be compatible with\n   * the content of the Union.\n   *\n   * @return {(undefined|VariantLayout)} - as described above.\n   *\n   * @throws {Error} - if `src` cannot be associated with a default or\n   * registered variant.\n   */\n  defaultGetSourceVariant(src) {\n    if (src.hasOwnProperty(this.discriminator.property)) {\n      if (this.defaultLayout\n          && src.hasOwnProperty(this.defaultLayout.property)) {\n        return undefined;\n      }\n      const vlo = this.registry[src[this.discriminator.property]];\n      if (vlo\n          && ((!vlo.layout)\n              || src.hasOwnProperty(vlo.property))) {\n        return vlo;\n      }\n    } else {\n      for (const tag in this.registry) {\n        const vlo = this.registry[tag];\n        if (src.hasOwnProperty(vlo.property)) {\n          return vlo;\n        }\n      }\n    }\n    throw new Error('unable to infer src variant');\n  }\n\n  /** Implement {@link Layout#decode|decode} for {@link Union}.\n   *\n   * If the variant is {@link Union#addVariant|registered} the return\n   * value is an instance of that variant, with no explicit\n   * discriminator.  Otherwise the {@link Union#defaultLayout|default\n   * layout} is used to decode the content. */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let dest;\n    const dlo = this.discriminator;\n    const discr = dlo.decode(b, offset);\n    let clo = this.registry[discr];\n    if (undefined === clo) {\n      let contentOffset = 0;\n      clo = this.defaultLayout;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dest = this.makeDestinationObject();\n      dest[dlo.property] = discr;\n      dest[clo.property] = this.defaultLayout.decode(b, offset + contentOffset);\n    } else {\n      dest = clo.decode(b, offset);\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Union}.\n   *\n   * This API assumes the `src` object is consistent with the union's\n   * {@link Union#defaultLayout|default layout}.  To encode variants\n   * use the appropriate variant-specific {@link VariantLayout#encode}\n   * method. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const vlo = this.getSourceVariant(src);\n    if (undefined === vlo) {\n      const dlo = this.discriminator;\n      const clo = this.defaultLayout;\n      let contentOffset = 0;\n      if (this.usesPrefixDiscriminator) {\n        contentOffset = dlo.layout.span;\n      }\n      dlo.encode(src[dlo.property], b, offset);\n      return contentOffset + clo.encode(src[clo.property], b,\n                                        offset + contentOffset);\n    }\n    return vlo.encode(src, b, offset);\n  }\n\n  /** Register a new variant structure within a union.  The newly\n   * created variant is returned.\n   *\n   * @param {Number} variant - initializer for {@link\n   * VariantLayout#variant|variant}.\n   *\n   * @param {Layout} layout - initializer for {@link\n   * VariantLayout#layout|layout}.\n   *\n   * @param {String} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {VariantLayout} */\n  addVariant(variant, layout, property) {\n    const rv = new VariantLayout(this, variant, layout, property);\n    this.registry[variant] = rv;\n    return rv;\n  }\n\n  /**\n   * Get the layout associated with a registered variant.\n   *\n   * If `vb` does not produce a registered variant the function returns\n   * `undefined`.\n   *\n   * @param {(Number|Buffer)} vb - either the variant number, or a\n   * buffer from which the discriminator is to be read.\n   *\n   * @param {Number} offset - offset into `vb` for the start of the\n   * union.  Used only when `vb` is an instance of {Buffer}.\n   *\n   * @return {({VariantLayout}|undefined)}\n   */\n  getVariant(vb, offset) {\n    let variant = vb;\n    if (Buffer.isBuffer(vb)) {\n      if (undefined === offset) {\n        offset = 0;\n      }\n      variant = this.discriminator.decode(vb, offset);\n    }\n    return this.registry[variant];\n  }\n}\n\n/**\n * Represent a specific variant within a containing union.\n *\n * **NOTE** The {@link Layout#span|span} of the variant may include\n * the span of the {@link Union#discriminator|discriminator} used to\n * identify it, but values read and written using the variant strictly\n * conform to the content of {@link VariantLayout#layout|layout}.\n *\n * **NOTE** User code should not invoke this constructor directly.  Use\n * the union {@link Union#addVariant|addVariant} helper method.\n *\n * @param {Union} union - initializer for {@link\n * VariantLayout#union|union}.\n *\n * @param {Number} variant - initializer for {@link\n * VariantLayout#variant|variant}.\n *\n * @param {Layout} [layout] - initializer for {@link\n * VariantLayout#layout|layout}.  If absent the variant carries no\n * data.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.  Unlike many other layouts, variant\n * layouts normally include a property name so they can be identified\n * within their containing {@link Union}.  The property identifier may\n * be absent only if `layout` is is absent.\n *\n * @augments {Layout}\n */\nclass VariantLayout extends Layout {\n  constructor(union, variant, layout, property) {\n    if (!(union instanceof Union)) {\n      throw new TypeError('union must be a Union');\n    }\n    if ((!Number.isInteger(variant)) || (0 > variant)) {\n      throw new TypeError('variant must be a (non-negative) integer');\n    }\n    if (('string' === typeof layout)\n        && (undefined === property)) {\n      property = layout;\n      layout = null;\n    }\n    if (layout) {\n      if (!(layout instanceof Layout)) {\n        throw new TypeError('layout must be a Layout');\n      }\n      if ((null !== union.defaultLayout)\n          && (0 <= layout.span)\n          && (layout.span > union.defaultLayout.span)) {\n        throw new Error('variant span exceeds span of containing union');\n      }\n      if ('string' !== typeof property) {\n        throw new TypeError('variant must have a String property');\n      }\n    }\n    let span = union.span;\n    if (0 > union.span) {\n      span = layout ? layout.span : 0;\n      if ((0 <= span) && union.usesPrefixDiscriminator) {\n        span += union.discriminator.layout.span;\n      }\n    }\n    super(span, property);\n\n    /** The {@link Union} to which this variant belongs. */\n    this.union = union;\n\n    /** The unsigned integral value identifying this variant within\n     * the {@link Union#discriminator|discriminator} of the containing\n     * union. */\n    this.variant = variant;\n\n    /** The {@link Layout} to be used when reading/writing the\n     * non-discriminator part of the {@link\n     * VariantLayout#union|union}.  If `null` the variant carries no\n     * data. */\n    this.layout = layout || null;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (0 <= this.span) {\n      /* Will be equal to the containing union span if that is not\n       * variable. */\n      return this.span;\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    /* Span is defined solely by the variant (and prefix discriminator) */\n    return contentOffset + this.layout.getSpan(b, offset + contentOffset);\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    if (this !== this.union.getVariant(b, offset)) {\n      throw new Error('variant mismatch');\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout) {\n      dest[this.property] = this.layout.decode(b, offset + contentOffset);\n    } else if (this.property) {\n      dest[this.property] = true;\n    } else if (this.union.usesPrefixDiscriminator) {\n      dest[this.union.discriminator.property] = this.variant;\n    }\n    return dest;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let contentOffset = 0;\n    if (this.union.usesPrefixDiscriminator) {\n      contentOffset = this.union.discriminator.layout.span;\n    }\n    if (this.layout\n        && (!src.hasOwnProperty(this.property))) {\n      throw new TypeError('variant lacks property ' + this.property);\n    }\n    this.union.discriminator.encode(this.variant, b, offset);\n    let span = contentOffset;\n    if (this.layout) {\n      this.layout.encode(src[this.property], b, offset + contentOffset);\n      span += this.layout.getSpan(b, offset + contentOffset);\n      if ((0 <= this.union.span)\n          && (span > this.union.span)) {\n        throw new Error('encoded variant overruns containing union');\n      }\n    }\n    return span;\n  }\n\n  /** Delegate {@link Layout#fromArray|fromArray} to {@link\n   * VariantLayout#layout|layout}. */\n  fromArray(values) {\n    if (this.layout) {\n      return this.layout.fromArray(values);\n    }\n  }\n}\n\n/** JavaScript chose to define bitwise operations as operating on\n * signed 32-bit values in 2's complement form, meaning any integer\n * with bit 31 set is going to look negative.  For right shifts that's\n * not a problem, because `>>>` is a logical shift, but for every\n * other bitwise operator we have to compensate for possible negative\n * results. */\nfunction fixBitwiseResult(v) {\n  if (0 > v) {\n    v += 0x100000000;\n  }\n  return v;\n}\n\n/**\n * Contain a sequence of bit fields as an unsigned integer.\n *\n * *Factory*: {@link module:Layout.bits|bits}\n *\n * This is a container element; within it there are {@link BitField}\n * instances that provide the extracted properties.  The container\n * simply defines the aggregate representation and its bit ordering.\n * The representation is an object containing properties with numeric\n * or {@link Boolean} values.\n *\n * {@link BitField}s are added with the {@link\n * BitStructure#addField|addField} and {@link\n * BitStructure#addBoolean|addBoolean} methods.\n\n * @param {Layout} word - initializer for {@link\n * BitStructure#word|word}.  The parameter must be an instance of\n * {@link UInt} (or {@link UIntBE}) that is no more than 4 bytes wide.\n *\n * @param {bool} [msb] - `true` if the bit numbering starts at the\n * most significant bit of the containing word; `false` (default) if\n * it starts at the least significant bit of the containing word.  If\n * the parameter at this position is a string and `property` is\n * `undefined` the value of this argument will instead be used as the\n * value of `property`.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass BitStructure extends Layout {\n  constructor(word, msb, property) {\n    if (!((word instanceof UInt)\n          || (word instanceof UIntBE))) {\n      throw new TypeError('word must be a UInt or UIntBE layout');\n    }\n    if (('string' === typeof msb)\n        && (undefined === property)) {\n      property = msb;\n      msb = undefined;\n    }\n    if (4 < word.span) {\n      throw new RangeError('word cannot exceed 32 bits');\n    }\n    super(word.span, property);\n\n    /** The layout used for the packed value.  {@link BitField}\n     * instances are packed sequentially depending on {@link\n     * BitStructure#msb|msb}. */\n    this.word = word;\n\n    /** Whether the bit sequences are packed starting at the most\n     * significant bit growing down (`true`), or the least significant\n     * bit growing up (`false`).\n     *\n     * **NOTE** Regardless of this value, the least significant bit of\n     * any {@link BitField} value is the least significant bit of the\n     * corresponding section of the packed value. */\n    this.msb = !!msb;\n\n    /** The sequence of {@link BitField} layouts that comprise the\n     * packed structure.\n     *\n     * **NOTE** The array remains mutable to allow fields to be {@link\n     * BitStructure#addField|added} after construction.  Users should\n     * not manipulate the content of this property.*/\n    this.fields = [];\n\n    /* Storage for the value.  Capture a variable instead of using an\n     * instance property because we don't want anything to change the\n     * value without going through the mutator. */\n    let value = 0;\n    this._packedSetValue = function(v) {\n      value = fixBitwiseResult(v);\n      return this;\n    };\n    this._packedGetValue = function() {\n      return value;\n    };\n  }\n\n  /** @override */\n  decode(b, offset) {\n    const dest = this.makeDestinationObject();\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        dest[fd.property] = fd.decode(value);\n      }\n    }\n    return dest;\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link BitStructure}.\n   *\n   * If `src` is missing a property for a member with a defined {@link\n   * Layout#property|property} the corresponding region of the packed\n   * value is left unmodified.  Unused bits are also left unmodified. */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    const value = this.word.decode(b, offset);\n    this._packedSetValue(value);\n    for (const fd of this.fields) {\n      if (undefined !== fd.property) {\n        const fv = src[fd.property];\n        if (undefined !== fv) {\n          fd.encode(fv);\n        }\n      }\n    }\n    return this.word.encode(this._packedGetValue(), b, offset);\n  }\n\n  /** Register a new bitfield with a containing bit structure.  The\n   * resulting bitfield is returned.\n   *\n   * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {BitField} */\n  addField(bits, property) {\n    const bf = new BitField(this, bits, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /** As with {@link BitStructure#addField|addField} for single-bit\n   * fields with `boolean` value representation.\n   *\n   * @param {string} property - initializer for {@link\n   * Layout#property|property}.\n   *\n   * @return {Boolean} */\n  addBoolean(property) {\n    // This is my Boolean, not the Javascript one.\n    // eslint-disable-next-line no-new-wrappers\n    const bf = new Boolean(this, property);\n    this.fields.push(bf);\n    return bf;\n  }\n\n  /**\n   * Get access to the bit field for a given property.\n   *\n   * @param {String} property - the bit field of interest.\n   *\n   * @return {BitField} - the field associated with `property`, or\n   * undefined if there is no such property.\n   */\n  fieldFor(property) {\n    if ('string' !== typeof property) {\n      throw new TypeError('property must be string');\n    }\n    for (const fd of this.fields) {\n      if (fd.property === property) {\n        return fd;\n      }\n    }\n  }\n}\n\n/**\n * Represent a sequence of bits within a {@link BitStructure}.\n *\n * All bit field values are represented as unsigned integers.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addField|addField} helper\n * method.\n *\n * **NOTE** BitField instances are not instances of {@link Layout}\n * since {@link Layout#span|span} measures 8-bit units.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {Number} bits - initializer for {@link BitField#bits|bits}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n */\nclass BitField {\n  constructor(container, bits, property) {\n    if (!(container instanceof BitStructure)) {\n      throw new TypeError('container must be a BitStructure');\n    }\n    if ((!Number.isInteger(bits)) || (0 >= bits)) {\n      throw new TypeError('bits must be positive integer');\n    }\n    const totalBits = 8 * container.span;\n    const usedBits = container.fields.reduce((sum, fd) => sum + fd.bits, 0);\n    if ((bits + usedBits) > totalBits) {\n      throw new Error('bits too long for span remainder ('\n                      + (totalBits - usedBits) + ' of '\n                      + totalBits + ' remain)');\n    }\n\n    /** The {@link BitStructure} instance to which this bit field\n     * belongs. */\n    this.container = container;\n\n    /** The span of this value in bits. */\n    this.bits = bits;\n\n    /** A mask of {@link BitField#bits|bits} bits isolating value bits\n     * that fit within the field.\n     *\n     * That is, it masks a value that has not yet been shifted into\n     * position within its containing packed integer. */\n    this.valueMask = (1 << bits) - 1;\n    if (32 === bits) { // shifted value out of range\n      this.valueMask = 0xFFFFFFFF;\n    }\n\n    /** The offset of the value within the containing packed unsigned\n     * integer.  The least significant bit of the packed value is at\n     * offset zero, regardless of bit ordering used. */\n    this.start = usedBits;\n    if (this.container.msb) {\n      this.start = totalBits - usedBits - bits;\n    }\n\n    /** A mask of {@link BitField#bits|bits} isolating the field value\n     * within the containing packed unsigned integer. */\n    this.wordMask = fixBitwiseResult(this.valueMask << this.start);\n\n    /** The property name used when this bitfield is represented in an\n     * Object.\n     *\n     * Intended to be functionally equivalent to {@link\n     * Layout#property}.\n     *\n     * If left undefined the corresponding span of bits will be\n     * treated as padding: it will not be mutated by {@link\n     * Layout#encode|encode} nor represented as a property in the\n     * decoded Object. */\n    this.property = property;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field. */\n  decode() {\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(word & this.wordMask);\n    const value = wordValue >>> this.start;\n    return value;\n  }\n\n  /** Store a value into the corresponding subsequence of the containing\n   * bit field.\n   *\n   * **NOTE** This is not a specialization of {@link\n   * Layout#encode|Layout.encode} and there is no return value. */\n  encode(value) {\n    if ((!Number.isInteger(value))\n        || (value !== fixBitwiseResult(value & this.valueMask))) {\n      throw new TypeError(nameWithProperty('BitField.encode', this)\n                          + ' value must be integer not exceeding ' + this.valueMask);\n    }\n    const word = this.container._packedGetValue();\n    const wordValue = fixBitwiseResult(value << this.start);\n    this.container._packedSetValue(fixBitwiseResult(word & ~this.wordMask)\n                                   | wordValue);\n  };\n}\n\n/**\n * Represent a single bit within a {@link BitStructure} as a\n * JavaScript boolean.\n *\n * **NOTE** User code should not invoke this constructor directly.\n * Use the container {@link BitStructure#addBoolean|addBoolean} helper\n * method.\n *\n * @param {BitStructure} container - initializer for {@link\n * BitField#container|container}.\n *\n * @param {string} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {BitField}\n */\n/* eslint-disable no-extend-native */\nclass Boolean extends BitField {\n  constructor(container, property) {\n    super(container, 1, property);\n  }\n\n  /** Override {@link BitField#decode|decode} for {@link Boolean|Boolean}.\n   *\n   * @returns {boolean} */\n  decode(b, offset) {\n    return !!BitField.prototype.decode.call(this, b, offset);\n  }\n\n  /** @override */\n  encode(value) {\n    if ('boolean' === typeof value) {\n      // BitField requires integer values\n      value = +value;\n    }\n    return BitField.prototype.encode.call(this, value);\n  }\n}\n/* eslint-enable no-extend-native */\n\n/**\n * Contain a fixed-length block of arbitrary data, represented as a\n * Buffer.\n *\n * *Factory*: {@link module:Layout.blob|blob}\n *\n * @param {(Number|ExternalLayout)} length - initializes {@link\n * Blob#length|length}.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Blob extends Layout {\n  constructor(length, property) {\n    if (!(((length instanceof ExternalLayout) && length.isCount())\n          || (Number.isInteger(length) && (0 <= length)))) {\n      throw new TypeError('length must be positive integer '\n                          + 'or an unsigned integer ExternalLayout');\n    }\n\n    let span = -1;\n    if (!(length instanceof ExternalLayout)) {\n      span = length;\n    }\n    super(span, property);\n\n    /** The number of bytes in the blob.\n     *\n     * This may be a non-negative integer, or an instance of {@link\n     * ExternalLayout} that satisfies {@link\n     * ExternalLayout#isCount|isCount()}. */\n    this.length = length;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return span;\n  }\n\n  /** @override */\n  decode(b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.span;\n    if (0 > span) {\n      span = this.length.decode(b, offset);\n    }\n    return b.slice(offset, offset + span);\n  }\n\n  /** Implement {@link Layout#encode|encode} for {@link Blob}.\n   *\n   * **NOTE** If {@link Layout#count|count} is an instance of {@link\n   * ExternalLayout} then the length of `src` will be encoded as the\n   * count after `src` is encoded. */\n  encode(src, b, offset) {\n    let span = this.length;\n    if (this.length instanceof ExternalLayout) {\n      span = src.length;\n    }\n    if (!(Buffer.isBuffer(src)\n          && (span === src.length))) {\n      throw new TypeError(nameWithProperty('Blob.encode', this)\n                          + ' requires (length ' + span + ') Buffer as src');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    b.write(src.toString('hex'), offset, span, 'hex');\n    if (this.length instanceof ExternalLayout) {\n      this.length.encode(span, b, offset);\n    }\n    return span;\n  }\n}\n\n/**\n * Contain a `NUL`-terminated UTF8 string.\n *\n * *Factory*: {@link module:Layout.cstr|cstr}\n *\n * **NOTE** Any UTF8 string that incorporates a zero-valued byte will\n * not be correctly decoded by this layout.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass CString extends Layout {\n  constructor(property) {\n    super(-1, property);\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let idx = offset;\n    while ((idx < b.length) && (0 !== b[idx])) {\n      idx += 1;\n    }\n    return 1 + idx - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    return b.slice(offset, offset + span - 1).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    b[offset + span] = 0;\n    return span + 1;\n  }\n}\n\n/**\n * Contain a UTF8 string with implicit length.\n *\n * *Factory*: {@link module:Layout.utf8|utf8}\n *\n * **NOTE** Because the length is implicit in the size of the buffer\n * this layout should be used only in isolation, or in a situation\n * where the length can be expressed by operating on a slice of the\n * containing buffer.\n *\n * @param {Number} [maxSpan] - the maximum length allowed for encoded\n * string content.  If not provided there is no bound on the allowed\n * content.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass UTF8 extends Layout {\n  constructor(maxSpan, property) {\n    if (('string' === typeof maxSpan)\n        && (undefined === property)) {\n      property = maxSpan;\n      maxSpan = undefined;\n    }\n    if (undefined === maxSpan) {\n      maxSpan = -1;\n    } else if (!Number.isInteger(maxSpan)) {\n      throw new TypeError('maxSpan must be an integer');\n    }\n\n    super(-1, property);\n\n    /** The maximum span of the layout in bytes.\n     *\n     * Positive values are generally expected.  Zero is abnormal.\n     * Attempts to encode or decode a value that exceeds this length\n     * will throw a `RangeError`.\n     *\n     * A negative value indicates that there is no bound on the length\n     * of the content. */\n    this.maxSpan = maxSpan;\n  }\n\n  /** @override */\n  getSpan(b, offset) {\n    if (!Buffer.isBuffer(b)) {\n      throw new TypeError('b must be a Buffer');\n    }\n    if (undefined === offset) {\n      offset = 0;\n    }\n    return b.length - offset;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    let span = this.getSpan(b, offset);\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    return b.slice(offset, offset + span).toString('utf-8');\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    if (undefined === offset) {\n      offset = 0;\n    }\n    /* Must force this to a string, lest it be a number and the\n     * \"utf8-encoding\" below actually allocate a buffer of length\n     * src */\n    if ('string' !== typeof src) {\n      src = src.toString();\n    }\n    const srcb = new Buffer(src, 'utf8');\n    const span = srcb.length;\n    if ((0 <= this.maxSpan)\n        && (this.maxSpan < span)) {\n      throw new RangeError('text length exceeds maxSpan');\n    }\n    if ((offset + span) > b.length) {\n      throw new RangeError('encoding overruns Buffer');\n    }\n    srcb.copy(b, offset);\n    return span;\n  }\n}\n\n/**\n * Contain a constant value.\n *\n * This layout may be used in cases where a JavaScript value can be\n * inferred without an expression in the binary encoding.  An example\n * would be a {@link VariantLayout|variant layout} where the content\n * is implied by the union {@link Union#discriminator|discriminator}.\n *\n * @param {Object|Number|String} value - initializer for {@link\n * Constant#value|value}.  If the value is an object (or array) and\n * the application intends the object to remain unchanged regardless\n * of what is done to values decoded by this layout, the value should\n * be frozen prior passing it to this constructor.\n *\n * @param {String} [property] - initializer for {@link\n * Layout#property|property}.\n *\n * @augments {Layout}\n */\nclass Constant extends Layout {\n  constructor(value, property) {\n    super(0, property);\n\n    /** The value produced by this constant when the layout is {@link\n     * Constant#decode|decoded}.\n     *\n     * Any JavaScript value including `null` and `undefined` is\n     * permitted.\n     *\n     * **WARNING** If `value` passed in the constructor was not\n     * frozen, it is possible for users of decoded values to change\n     * the content of the value. */\n    this.value = value;\n  }\n\n  /** @override */\n  decode(b, offset, dest) {\n    return this.value;\n  }\n\n  /** @override */\n  encode(src, b, offset) {\n    /* Constants take no space */\n    return 0;\n  }\n}\n\nexports.ExternalLayout = ExternalLayout;\nexports.GreedyCount = GreedyCount;\nexports.OffsetLayout = OffsetLayout;\nexports.UInt = UInt;\nexports.UIntBE = UIntBE;\nexports.Int = Int;\nexports.IntBE = IntBE;\nexports.Float = Float;\nexports.FloatBE = FloatBE;\nexports.Double = Double;\nexports.DoubleBE = DoubleBE;\nexports.Sequence = Sequence;\nexports.Structure = Structure;\nexports.UnionDiscriminator = UnionDiscriminator;\nexports.UnionLayoutDiscriminator = UnionLayoutDiscriminator;\nexports.Union = Union;\nexports.VariantLayout = VariantLayout;\nexports.BitStructure = BitStructure;\nexports.BitField = BitField;\nexports.Boolean = Boolean;\nexports.Blob = Blob;\nexports.CString = CString;\nexports.UTF8 = UTF8;\nexports.Constant = Constant;\n\n/** Factory for {@link GreedyCount}. */\nexports.greedy = ((elementSpan, property) => new GreedyCount(elementSpan, property));\n\n/** Factory for {@link OffsetLayout}. */\nexports.offset = ((layout, offset, property) => new OffsetLayout(layout, offset, property));\n\n/** Factory for {@link UInt|unsigned int layouts} spanning one\n * byte. */\nexports.u8 = (property => new UInt(1, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16 = (property => new UInt(2, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24 = (property => new UInt(3, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32 = (property => new UInt(4, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40 = (property => new UInt(5, property));\n\n/** Factory for {@link UInt|little-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48 = (property => new UInt(6, property));\n\n/** Factory for {@link NearUInt64|little-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64 = (property => new NearUInt64(property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning two bytes. */\nexports.u16be = (property => new UIntBE(2, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning three bytes. */\nexports.u24be = (property => new UIntBE(3, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning four bytes. */\nexports.u32be = (property => new UIntBE(4, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning five bytes. */\nexports.u40be = (property => new UIntBE(5, property));\n\n/** Factory for {@link UInt|big-endian unsigned int layouts}\n * spanning six bytes. */\nexports.u48be = (property => new UIntBE(6, property));\n\n/** Factory for {@link NearUInt64BE|big-endian unsigned int\n * layouts} interpreted as Numbers. */\nexports.nu64be = (property => new NearUInt64BE(property));\n\n/** Factory for {@link Int|signed int layouts} spanning one\n * byte. */\nexports.s8 = (property => new Int(1, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning two bytes. */\nexports.s16 = (property => new Int(2, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning three bytes. */\nexports.s24 = (property => new Int(3, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning four bytes. */\nexports.s32 = (property => new Int(4, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning five bytes. */\nexports.s40 = (property => new Int(5, property));\n\n/** Factory for {@link Int|little-endian signed int layouts}\n * spanning six bytes. */\nexports.s48 = (property => new Int(6, property));\n\n/** Factory for {@link NearInt64|little-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64 = (property => new NearInt64(property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning two bytes. */\nexports.s16be = (property => new IntBE(2, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning three bytes. */\nexports.s24be = (property => new IntBE(3, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning four bytes. */\nexports.s32be = (property => new IntBE(4, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning five bytes. */\nexports.s40be = (property => new IntBE(5, property));\n\n/** Factory for {@link Int|big-endian signed int layouts}\n * spanning six bytes. */\nexports.s48be = (property => new IntBE(6, property));\n\n/** Factory for {@link NearInt64BE|big-endian signed int layouts}\n * interpreted as Numbers. */\nexports.ns64be = (property => new NearInt64BE(property));\n\n/** Factory for {@link Float|little-endian 32-bit floating point} values. */\nexports.f32 = (property => new Float(property));\n\n/** Factory for {@link FloatBE|big-endian 32-bit floating point} values. */\nexports.f32be = (property => new FloatBE(property));\n\n/** Factory for {@link Double|little-endian 64-bit floating point} values. */\nexports.f64 = (property => new Double(property));\n\n/** Factory for {@link DoubleBE|big-endian 64-bit floating point} values. */\nexports.f64be = (property => new DoubleBE(property));\n\n/** Factory for {@link Structure} values. */\nexports.struct = ((fields, property, decodePrefixes) => new Structure(fields, property, decodePrefixes));\n\n/** Factory for {@link BitStructure} values. */\nexports.bits = ((word, msb, property) => new BitStructure(word, msb, property));\n\n/** Factory for {@link Sequence} values. */\nexports.seq = ((elementLayout, count, property) => new Sequence(elementLayout, count, property));\n\n/** Factory for {@link Union} values. */\nexports.union = ((discr, defaultLayout, property) => new Union(discr, defaultLayout, property));\n\n/** Factory for {@link UnionLayoutDiscriminator} values. */\nexports.unionLayoutDiscriminator = ((layout, property) => new UnionLayoutDiscriminator(layout, property));\n\n/** Factory for {@link Blob} values. */\nexports.blob = ((length, property) => new Blob(length, property));\n\n/** Factory for {@link CString} values. */\nexports.cstr = (property => new CString(property));\n\n/** Factory for {@link UTF8} values. */\nexports.utf8 = ((maxSpan, property) => new UTF8(maxSpan, property));\n\n/** Factory for {@link Constant} values. */\nexports.const = ((value, property) => new Constant(value, property));\n", null, null, null, null, null, null, null, null, "var global = typeof self !== 'undefined' ? self : this;\nvar __self__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = global.DOMException\n}\nF.prototype = global;\nreturn new F();\n})();\n(function(self) {\n\nvar irrelevant = (function (exports) {\n\n  var support = {\n    searchParams: 'URLSearchParams' in self,\n    iterable: 'Symbol' in self && 'iterator' in Symbol,\n    blob:\n      'FileReader' in self &&\n      'Blob' in self &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in self,\n    arrayBuffer: 'ArrayBuffer' in self\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~]/i.test(name)) {\n      throw new TypeError('Invalid character in header field name')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          return consumed(this) || Promise.resolve(this._bodyArrayBuffer)\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    preProcessedHeaders.split(/\\r?\\n/).forEach(function(line) {\n      var parts = line.split(':');\n      var key = parts.shift().trim();\n      if (key) {\n        var value = parts.join(':').trim();\n        headers.append(key, value);\n      }\n    });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = 'statusText' in options ? options.statusText : 'OK';\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = self.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        resolve(new Response(body, options));\n      };\n\n      xhr.onerror = function() {\n        reject(new TypeError('Network request failed'));\n      };\n\n      xhr.ontimeout = function() {\n        reject(new TypeError('Network request failed'));\n      };\n\n      xhr.onabort = function() {\n        reject(new exports.DOMException('Aborted', 'AbortError'));\n      };\n\n      xhr.open(request.method, request.url, true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr && support.blob) {\n        xhr.responseType = 'blob';\n      }\n\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value);\n      });\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!self.fetch) {\n    self.fetch = fetch;\n    self.Headers = Headers;\n    self.Request = Request;\n    self.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n})({});\n})(__self__);\n__self__.fetch.ponyfill = true;\n// Remove \"polyfill\" property added by whatwg-fetch\ndelete __self__.fetch.polyfill;\n// Choose between native implementation (global) or custom implementation (__self__)\n// var ctx = global.fetch ? global : __self__;\nvar ctx = __self__; // this line disable service worker support temporarily\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["encode", "decode", "<PERSON><PERSON><PERSON>", "bs58", "base64", "union", "pick", "bs58.encode", "features.isSet", "__assign", "sha256", "camelcase", "Blob", "BufferLayout.Layout", "BufferLayout.struct", "BufferLayout.u32", "BufferLayout.blob", "BufferLayout.offset", "public<PERSON>ey", "BufferLayout.union", "BufferLayout.ns64", "WrappedLayout", "BufferLayout.nu64", "this", "rpcUtil.getMultipleAccountsAndContext", "pubkeyUtil.associated", "Layout", "BufferLayout.u8", "utf8.encode", "InstructionFactory", "utf8.decode", "systemProgram"], "mappings": ";;;;;;;;;;;;;;AAUA;;;;;;AAMG;AACa,SAAA,MAAM,CAAI,KAAU,EAAE,IAAY,EAAA;IAChD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAClE,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAC5D,CAAC;AACJ,CAAC;AAED;;;;;AAKG;AACI,MAAM,sBAAsB,GAAG,CACpC,EAAsC,KACR;IAC9B,OAAO,SAAS,IAAI,EAAE,CAAC;AACzB,CAAC;;AC/BK,SAAUA,QAAM,CAAC,IAAY,EAAA;AACjC,IAAA,OAAO,IAAI,CAAC,MAAM,CAChB,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EACvD,IAAI,CACL,CAAC;AACJ,CAAC;AAEK,SAAUC,QAAM,CAAC,IAAY,EAAA;IACjC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC5B,QAAA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,KAAA;AACD,IAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AACzB,QAAA,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;AACnB,KAAA;IAED,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAE9B,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,QAAA,OAAOC,QAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,KAAA;IAED,OAAOA,QAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D;;;;;;;;ACtBM,SAAUD,QAAM,CAAC,KAAiB,EAAA;IACtC,MAAM,OAAO,GACT,IAAI,WAAW,CAAC,OAAO,CAAC;AAC1B,QAA4C,CAAC;AAE/C,IAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAEK,SAAUD,QAAM,CAAC,KAAa,EAAA;IAClC,MAAM,OAAO,GACT,IAAI,WAAW,EAAE;AACnB,QAA4C,CAAC;AAC/C,IAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B;;;;;;;;ACbM,SAAUA,QAAM,CAAC,IAAoC,EAAA;AACzD,IAAA,OAAOG,MAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAEK,SAAUF,QAAM,CAAC,IAAY,EAAA;AACjC,IAAA,OAAOE,MAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B;;;;;;;;ACLM,SAAU,MAAM,CAAC,IAAY,EAAA;AACjC,IAAA,OAAOC,QAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACpC,CAAC;AAEK,SAAU,MAAM,CAAC,IAAY,EAAA;IACjC,OAAOF,QAAM,CAAC,IAAI,CAACE,QAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/C;;;;;;;;;;;;;;;;ACCM,SAAU,cAAc,CAAC,GAAQ,EAAA;AACrC,IAAA,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;IACzB,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;;YACvB,IAAI,GAAG,GAAG,CAAA,EAAA,GAAA,CAAC,CAAC,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,CAAC,IAAI,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC1B,SAAC,CAAC,CAAC;AACJ,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;SACgB,aAAa,CAC3B,KAAsC,EACtC,GAAG,IAAW,EAAA;IAEd,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AACpC,QAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC5C,KAAA;IACD,MAAM,EAAE,GAA2B,EAAE,CAAC;IACtC,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;QAC3B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3B,GAAG,IAAI,CAAC,CAAC;AACX,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;SACgB,gBAAgB,CAC9B,UAA4B,EAC5B,WAAqB,EAAE,EAAA;AAEvB,IAAA,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;QACzB,IAAI,UAAU,IAAI,GAAG,EAAE;AACrB,YAAA,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAa,CAAC,CAAC;AAChE,SAAA;AAAM,aAAA;YACL,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,CAAA,mBAAA,EAAsB,GAAG,CAAC,IAAI,CAAgB,cAAA,CAAA,CAAC,CAAC;AACjE,aAAA;AACF,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AACM,SAAU,gBAAgB,CAAC,OAAgB,EAAA;AAC/C,IAAA,OAAO,OAAO,YAAY,SAAS,GAAG,OAAO,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;AACzE;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,SAAS,SAAS,CAAC;AACpC,EAAE,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,MAAM;AACV,MAAM,OAAO;AACb,MAAM,GAAG,IAAI;AACb,KAAK,GAAG,OAAO,CAAC;AAChB,IAAI,MAAM;AACV,MAAM,IAAI;AACV,KAAK,GAAG,OAAO,CAAC;AAChB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;AAC9F,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACtC;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM;AAC1B,MAAM,IAAI,OAAO,CAAC;AAClB;AACA,MAAM,OAAO,CAAC,OAAO,GAAG,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;AACtF,KAAK,CAAC;AACN,GAAG;AACH;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,CAAC,EAAE;AACvB,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrB,EAAE,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC;AAC5C,CAAC;AAaD;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,KAAK,EAAE;AACtB,EAAE,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACxE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,KAAK;AACT,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AACnB,EAAE,OAAO,IAAI,GAAG,SAAS,GAAG,KAAK,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;AACnD,EAAE,IAAI,MAAM,KAAK,IAAI,EAAE;AACvB,IAAI,OAAO;AACX,GAAG,MAAM,IAAI,MAAM,KAAK,KAAK,EAAE;AAC/B,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,GAAG,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACzC,IAAI,MAAM,GAAG;AACb,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,GAAG,OAAO,CAAC;AACd,EAAE,MAAM;AACR,IAAI,IAAI;AACR,GAAG,GAAG,MAAM,CAAC;AACb,EAAE,MAAM;AACR,IAAI,UAAU;AACd,IAAI,OAAO,GAAG,4BAA4B,GAAG,IAAI,GAAG,GAAG,IAAI,UAAU,GAAG,oBAAoB,GAAG,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,mBAAmB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG;AAChK,GAAG,GAAG,MAAM,CAAC;AACb,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,GAAG,MAAM;AACb,IAAI,OAAO;AACX,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,UAAU,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;AACrD,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AAC3B,IAAI,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AACtB,GAAG;AACH;AACA,EAAE,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;AAC1B,IAAI,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzD;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,OAAO,CAAC;AACpB,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,GAAG;AACH;AACA,EAAE,MAAM;AACR,IAAI,IAAI,GAAG,EAAE;AACb,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC;AACpB,IAAI,MAAM,GAAG,KAAK;AAClB,IAAI,IAAI,GAAG,KAAK;AAChB,GAAG,GAAG,OAAO,CAAC;AACd,EAAE,MAAM,GAAG,GAAG;AACd,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACvC;AACA,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC/G,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AAC/B,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AAC9C,UAAU,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;AACA,EAAE,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;AACtD,IAAI,KAAK,GAAG,KAAK,CAAC;AAClB,IAAI,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;AACpD,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,IAAI,EAAE,CAAC,KAAK,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;AACjD,MAAM,MAAM,EAAE,CAAC,KAAK,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;AACvD,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,KAAK,CAAC,CAAC;AACP;AACA,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE;AACxB,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAChB,QAAQ,KAAK,GAAG,KAAK,CAAC;AACtB,QAAQ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAChC,OAAO,MAAM,IAAI,MAAM,EAAE;AACzB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;AACA,QAAQ,IAAI,CAAC,KAAK,SAAS,EAAE;AAC7B,UAAU,KAAK,GAAG,CAAC,CAAC;AACpB,SAAS,MAAM,IAAI,KAAK,YAAY,GAAG,EAAE;AACzC,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,SAAS,MAAM,IAAI,KAAK,YAAY,GAAG,EAAE;AACzC,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,SAAS,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpC,UAAU,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;AACtD,MAAM,KAAK,GAAG,KAAK,CAAC;AACpB,MAAM,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACjC,KAAK;AACL,GAAG;AACH;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC7B,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AAC1B,IAAI,MAAM;AACV,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,OAAO,GAAG,KAAK,IAAI,KAAK;AAC9B,MAAM,OAAO,GAAG,aAAa,EAAE;AAC/B,KAAK,GAAG,KAAK,CAAC;AACd,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK;AAC3C,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACjD,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACxD,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;AAChC,KAAK;AACL;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK;AACzC,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC/C,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACxD,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;AAC9B,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,EAAE,CAAC,KAAK,EAAE;AACZ,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE;AAC3B,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,KAAK;AACL;AACA,IAAI,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;AAC/B,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;AAC/B,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE;AACzC,IAAI,MAAM,EAAE,IAAI;AAChB,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;AAC7B,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE;AACzC,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AACjB,IAAI,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE;AAC3B,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AACtC;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAChB,IAAI,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa;AACzD,MAAM,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;AAC9B,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAClB,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH,CAAC;AAYD;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE;AACjC,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,SAAS;AACb,GAAG,CAAC,CAAC;AACL,CAAC;AA8KD;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,GAAG;AACf,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,KAAK,CAAC,OAAO,EAAE;AACxB,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,MAAM,EAAE,OAAO;AACnB;AACA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC3C,QAAQ,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;AAC9C,UAAU,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAChC,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,CAAC,KAAK,EAAE;AACnB,MAAM,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC;AAC1D,KAAK;AACL;AACA,IAAI,SAAS,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,yCAAyC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9F,KAAK;AACL;AACA,GAAG,CAAC,CAAC;AACL,CAAC;AAUD;AACA;AACA;AACA;AACA,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI;AACpC,IAAI,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;AACtC,GAAG,CAAC,CAAC;AACL,CAAC;AAuFD,SAAS,OAAO,CAAC,QAAQ,EAAE;AAC3B,EAAE,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AACtC,EAAE,MAAM,CAAC,GAAG,OAAO,QAAQ,CAAC;AAC5B,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,MAAM,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,SAAS,GAAG,QAAQ,GAAG,IAAI;AACjF;AACA,IAAI,SAAS,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,KAAK,KAAK,QAAQ,IAAI,wBAAwB,GAAG,WAAW,GAAG,mBAAmB,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/G,KAAK;AACL;AACA,GAAG,CAAC,CAAC;AACL,CAAC;AAgCD;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,EAAE,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,MAAM;AAC/B,IAAI,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;AAC7E,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACzE,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,MAAM,GAAG;AAClB,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI;AACnC,IAAI,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,mCAAmC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5G,GAAG,CAAC,CAAC;AACL,CAAC;AAkCD;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,EAAE,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,MAAM;AAC/B,IAAI,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;AAClF,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAC9E,GAAG,CAAC,CAAC;AACL,CAAC;AAgED;AACA;AACA;AACA;AACA,SAAS,MAAM,GAAG;AAClB,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI;AACnC,IAAI,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,mCAAmC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,CAAC;AA4BD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,MAAM,EAAE;AACtB,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnC,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM;AACV;AACA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC3B,QAAQ,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AAC9B,UAAU,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,SAAS,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,oCAAoC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACpF,KAAK;AACL;AACA,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,SAASC,OAAK,CAAC,OAAO,EAAE;AACxB,EAAE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3D,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,MAAM,EAAE,IAAI;AAChB;AACA,IAAI,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE;AACxB,MAAM,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;AAC3C,QAAQ,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;AACtC,UAAU,MAAM,EAAE,IAAI;AACtB,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,OAAO,CAAC,IAAI,OAAO,EAAE,CAAC;AACtB,MAAM,OAAO,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5C,KAAK;AACL;AACA,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE;AAC1B,MAAM,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC1B;AACA,MAAM,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;AAC/B,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC/C,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AAC/B;AACA,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACvB,UAAU,OAAO,EAAE,CAAC;AACpB,SAAS,MAAM;AACf,UAAU,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,EAAE;AAC1C,YAAY,IAAI,OAAO,EAAE;AACzB,cAAc,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,aAAa;AACb,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA,MAAM,OAAO,CAAC,4CAA4C,GAAG,WAAW,GAAG,mBAAmB,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC5H,KAAK;AACL;AACA,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,MAAM;AAC/B,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK;AAC7B,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1G,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;AC/9BA;;;AAGG;AACI,eAAe,MAAM,CAC1B,SAAkB,EAClB,QAA6B,EAC7B,IAAa,EACb,QAAmB,EAAA;AAEnB,IAAA,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;IACxC,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,WAAW,EAAE,CAAC;AAC1B,KAAA;AAED,IAAA,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7B,IAAA,EAAE,CAAC,GAAG,CACJ,IAAI,sBAAsB,CAAC;QACzB,SAAS;AACT,QAAA,IAAI,EAAE,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,QAAQ,GAAI,EAAE;QACpB,IAAI;AACL,KAAA,CAAC,CACH,CAAC;AAEF,IAAA,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE;AACzC,QAAA,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;AACH,KAAA;IAED,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,2BAA2B,GAAW,EAAE,CAAC;AAExC,eAAe,mBAAmB,CACvC,UAAsB,EACtB,UAAuB,EACvB,UAAuB,EAAA;IAIvB,MAAM,OAAO,GAAG,MAAM,6BAA6B,CACjD,UAAU,EACV,UAAU,EACV,UAAU,CACX,CAAC;AACF,IAAA,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;AAC5B,QAAA,OAAO,MAAM;AACX,cAAE,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;cACxD,IAAI,CAAC;AACX,KAAC,CAAC,CAAC;AACL,CAAC;AAEM,eAAe,6BAA6B,CACjD,UAAsB,EACtB,UAAuB,EACvB,UAAuB,EAAA;AAQvB,IAAA,IAAI,UAAU,CAAC,MAAM,IAAI,2BAA2B,EAAE;QACpD,OAAO,MAAM,iCAAiC,CAC5C,UAAU,EACV,UAAU,EACV,UAAU,CACX,CAAC;AACH,KAAA;AAAM,SAAA;QACL,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAO/B,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAChB,iCAAiC,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC,CACjE,CACF,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;AACvB,KAAA;AACH,CAAC;AAED,eAAe,iCAAiC,CAC9C,UAAsB,EACtB,UAAuB,EACvB,kBAA+B,EAAA;IAQ/B,MAAM,UAAU,GAAG,kBAAkB,KAAlB,IAAA,IAAA,kBAAkB,KAAlB,KAAA,CAAA,GAAA,kBAAkB,GAAI,UAAU,CAAC,UAAU,CAAC;AAC/D,IAAA,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,GACpC,MAAM,UAAU,CAAC,iCAAiC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC7E,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,KAAI;QACjD,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACD,OAAO;AACL,YAAA,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC;YAC1B,OAAO;YACP,OAAO;SACR,CAAC;AACJ,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;AACO,eAAe,mBAAmB,CACvC,UAAsB,EACtB,WAAwB,EACxB,OAAuB,EACvB,UAAuB,EACvB,eAA4C,EAAA;;AAE5C,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,QAAA,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B,KAAA;;AAGD,IAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;AACvC,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;;IAErC,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACzD,MAAM,kBAAkB,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAA,MAAM,MAAM,GAAQ;AAClB,QAAA,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE,UAAU,KAAV,IAAA,IAAA,UAAU,cAAV,UAAU,GAAI,UAAU,CAAC,UAAU;KAChD,CAAC;AAEF,IAAA,IAAI,eAAe,EAAE;AACnB,QAAA,MAAM,SAAS,GAAG,CAChB,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,OAAO,CAAC,aAAa,EAAE,EAC1E,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE/B,MAAM,CAAC,UAAU,CAAC,GAAG;AACnB,YAAA,QAAQ,EAAE,QAAQ;YAClB,SAAS;SACV,CAAC;AACH,KAAA;AAED,IAAA,IAAI,OAAO,EAAE;AACX,QAAA,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,KAAA;AAED,IAAA,MAAM,IAAI,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;;IAE1C,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,WAAW,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;IAC5E,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;IAClE,IAAI,OAAO,IAAI,GAAG,EAAE;AAClB,QAAA,IAAI,IAAI,CAAC;AACT,QAAA,IAAI,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE;YACvB,IAAI,GAAG,MAAA,GAAG,CAAC,KAAK,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC;YAC5B,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC/B,MAAM,WAAW,GAAG,QAAQ,CAAC;gBAC7B,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACtD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC5C,aAAA;AACF,SAAA;AACD,QAAA,MAAM,IAAI,oBAAoB,CAC5B,kCAAkC,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,EACtD,IAAI,CACL,CAAC;AACH,KAAA;IACD,OAAO,GAAG,CAAC,MAAM,CAAC;AACpB,CAAC;AAED;AACA,SAAS,aAAa,CAAO,MAAoB,EAAA;AAC/C,IAAA,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,gBAAgB,EAAE,CAAC,KAAK,KAAI;QACjE,IAAI,OAAO,IAAI,KAAK,EAAE;AACpB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;YACL,OAAO;AACL,gBAAA,GAAG,KAAK;gBACR,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;aACrC,CAAC;AACH,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AACA,MAAM,gBAAgB,GAAG,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AAEpD;AACA,SAAS,eAAe,CAAO,MAAoB,EAAA;AACjD,IAAA,OAAOA,OAAK,CAAC;AACX,QAAAC,IAAI,CAAC;AACH,YAAA,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;YACvB,EAAE,EAAE,MAAM,EAAE;YACZ,MAAM;SACP,CAAC;AACF,QAAAA,IAAI,CAAC;AACH,YAAA,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;YACvB,EAAE,EAAE,MAAM,EAAE;YACZ,KAAK,EAAEA,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO,EAAE;gBACf,OAAO,EAAE,MAAM,EAAE;AACjB,gBAAA,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;aACtB,CAAC;SACH,CAAC;AACH,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AACA,SAAS,uBAAuB,CAAO,KAAmB,EAAA;IACxD,OAAO,aAAa,CAClBA,IAAI,CAAC;QACH,OAAO,EAAEA,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM,EAAE;SACf,CAAC;QACF,KAAK;AACN,KAAA,CAAC,CACH,CAAC;AACJ,CAAC;AAED;AACA,MAAM,kCAAkC,GAAG,uBAAuB,CAChEA,IAAI,CAAC;AACH,IAAA,GAAG,EAAE,QAAQ,CAACD,OAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/B,QAAQ,EAAE,QAAQ,CAChB,QAAQ,CACN,KAAK,CACH,QAAQ,CACNA,IAAI,CAAC;QACH,UAAU,EAAE,OAAO,EAAE;QACrB,KAAK,EAAE,MAAM,EAAE;QACf,QAAQ,EAAE,MAAM,EAAE;AAClB,QAAA,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;AACrB,QAAA,SAAS,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;KAC9B,CAAC,CACH,CACF,CACF,CACF;AACD,IAAA,aAAa,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;AAClC,CAAA,CAAC,CACH;;;;;;;;;;ACzOD;;;AAGG;MACU,cAAc,CAAA;AAGzB;;;;AAIG;AACH,IAAA,WAAA,CACW,UAAsB,EACtB,MAAc,EACd,IAAoB,EAAA;QAFpB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;QACtB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACd,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAE7B,IAAI,CAAC,SAAS,GAAG,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,SAAS,CAAC;KACpC;AAED,IAAA,OAAO,cAAc,GAAA;QACnB,OAAO;AACL,YAAA,mBAAmB,EAAE,WAAW;AAChC,YAAA,UAAU,EAAE,WAAW;SACxB,CAAC;KACH;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,KAAK,CAAC,GAAY,EAAE,IAAqB,EAAA;AAC9C,QAAe;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAC;AAChE,SAAA;KASF;AAED;;;;;AAKG;AACH,IAAA,OAAO,GAAG,GAAA;AACR,QAAe;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,yCAAA,CAA2C,CAAC,CAAC;AAC9D,SAAA;KAaF;AAED;;;;;;AAMG;AACH,IAAA,MAAM,cAAc,CAClB,EAAsC,EACtC,OAAkB,EAClB,IAAqB,EAAA;;QAErB,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;AAC9B,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,EAAE,CAAC,QAAQ,GAAG,CAAA,EAAA,GAAA,EAAE,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AACnD,YAAA,EAAE,CAAC,eAAe,GAAG,CACnB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAClE,SAAS,CAAC;AAEZ,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC5B,oBAAA,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACxB,iBAAA;AACF,aAAA;AACF,SAAA;QACD,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC3C,QAAA,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;QAE7B,IAAI;YACF,OAAO,MAAM,4BAA4B,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACzE,SAAA;AAAC,QAAA,OAAO,GAAG,EAAE;;;YAGZ,IAAI,GAAG,YAAY,YAAY,EAAE;;;;;gBAK/B,MAAM,KAAK,GAAGC,QAAW,CACvB,sBAAsB,CAAC,EAAE,CAAC;AACxB,sBAAE,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,KAAI,IAAI,UAAU,EAAE;sBACtC,CAAA,EAAA,GAAA,EAAE,CAAC,SAAS,mCAAI,IAAI,UAAU,EAAE,CACrC,CAAC;gBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3D,oBAAA,UAAU,EAAE,WAAW;AACxB,iBAAA,CAAC,CAAC;gBACH,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,MAAM,GAAG,CAAC;AACX,iBAAA;AAAM,qBAAA;oBACL,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,QAAQ,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC;AACxC,oBAAA,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjE,iBAAA;AACF,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,GAAG,CAAC;AACX,aAAA;AACF,SAAA;KACF;AAED;;;;;;AAMG;AACH,IAAA,MAAM,OAAO,CACX,aAGG,EACH,IAAqB,EAAA;;QAErB,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAClB,SAAA;AACD,QAAA,MAAM,eAAe,GAAG,CACtB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAClE,SAAS,CAAC;QAEZ,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,KAAI;;AAChC,YAAA,IAAI,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,gBAAA,IAAI,EAAE,GAAyB,CAAC,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,CAAC,OAAO,EAAE;AACb,oBAAA,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpB,iBAAA;AACD,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,EAAE,GAAgB,CAAC,CAAC,EAAE,CAAC;gBAC3B,IAAI,OAAO,GAAG,CAAA,EAAA,GAAA,CAAC,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AAE9B,gBAAA,EAAE,CAAC,QAAQ,GAAG,CAAA,EAAA,GAAA,EAAE,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AACnD,gBAAA,EAAE,CAAC,eAAe,GAAG,eAAe,CAAC;AAErC,gBAAA,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACrB,oBAAA,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACrB,iBAAC,CAAC,CAAC;AACH,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AACH,SAAC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAE7D,MAAM,IAAI,GAA2B,EAAE,CAAC;AAExC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACxB,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;YAE7B,IAAI;AACF,gBAAA,IAAI,CAAC,IAAI,CACP,MAAM,4BAA4B,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CACjE,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAG,EAAE;;;gBAGZ,IAAI,GAAG,YAAY,YAAY,EAAE;;;;;oBAK/B,MAAM,KAAK,GAAGA,QAAW,CACvB,sBAAsB,CAAC,EAAE,CAAC;AACxB,0BAAE,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,KAAI,IAAI,UAAU,EAAE;0BACtC,CAAA,EAAA,GAAA,EAAE,CAAC,SAAS,mCAAI,IAAI,UAAU,EAAE,CACrC,CAAC;oBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3D,wBAAA,UAAU,EAAE,WAAW;AACxB,qBAAA,CAAC,CAAC;oBACH,IAAI,CAAC,QAAQ,EAAE;AACb,wBAAA,MAAM,GAAG,CAAC;AACX,qBAAA;AAAM,yBAAA;wBACL,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,QAAQ,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC;AACxC,wBAAA,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjE,qBAAA;AACF,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,GAAG,CAAC;AACX,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;;;AASG;IACH,MAAM,QAAQ,CACZ,EAAsC,EACtC,OAAkB,EAClB,UAAuB,EACvB,eAAuC,EAAA;QAEvC,IAAI,eAAe,GAAG,CACpB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACtC,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,UAAU,GAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CACzC,EACD,SAAS,CAAC;AAEZ,QAAA,IAAI,MAA2D,CAAC;AAChE,QAAA,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;AAC9B,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACjB,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC5C,aAAA;;;AAID,YAAA,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;AACxE,SAAA;AAAM,aAAA;AACL,YAAA,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AACnD,YAAA,EAAE,CAAC,eAAe,GAAG,eAAe,CAAC;AAErC,YAAA,IAAI,OAAO,EAAE;gBACX,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC5C,aAAA;AACD,YAAA,MAAM,GAAG,MAAM,mBAAmB,CAChC,IAAI,CAAC,UAAU,EACf,EAAE,EACF,OAAO,EACP,UAAU,EACV,eAAe,CAChB,CAAC;AACH,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;AACpB,YAAA,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvC,SAAA;QAED,OAAO,MAAM,CAAC,KAAK,CAAC;KACrB;AACF,CAAA;AAED,MAAM,aAAc,SAAQ,KAAK,CAAA;IAC/B,WACW,CAAA,kBAAgD,EACzD,OAAgB,EAAA;QAEhB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHN,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAA8B;KAI1D;AACF,CAAA;AAqBD;AACA;AACA,eAAe,4BAA4B,CACzC,UAAsB,EACtB,cAAmC,EACnC,OAAwB,EAAA;IAExB,MAAM,WAAW,GAAG,OAAO,IAAI;QAC7B,aAAa,EAAE,OAAO,CAAC,aAAa;AACpC,QAAA,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,UAAU;KACvE,CAAC;IAEF,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,kBAAkB,CACnD,cAAc,EACd,WAAW,CACZ,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG,CACb,MAAM,UAAU,CAAC,kBAAkB,CACjC,SAAS,EACT,OAAO,IAAI,OAAO,CAAC,UAAU,CAC9B,EACD,KAAK,CAAC;IAER,IAAI,MAAM,CAAC,GAAG,EAAE;AACd,QAAA,MAAM,IAAI,YAAY,CACpB,CAAA,gBAAA,EAAmB,SAAS,CAAY,SAAA,EAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,CAAA,CAAG,CAClE,CAAC;AACH,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,YAAa,SAAQ,KAAK,CAAA;AAC9B,IAAA,WAAA,CAAY,OAAgB,EAAA;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;KAChB;AACF,CAAA;AAED;;AAEG;AACG,SAAU,WAAW,CAAC,QAAkB,EAAA;IAC5C,SAAS,GAAG,QAAQ,CAAC;AACvB,CAAC;AAED;;AAEG;SACa,WAAW,GAAA;IACzB,IAAI,SAAS,KAAK,IAAI,EAAE;AACtB,QAAA,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC;AAC/B,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;AACA,IAAI,SAAS,GAAoB,IAAI;;AC9ZrC,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY,CAAC,CAAC,CAAC;AAE/E,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAEtB,SAAU,GAAG,CAAC,GAAW,EAAA;AAC7B,IAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACjC,QAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACpC,KAAA;AACD,IAAA,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3B,CAAC;AAEK,SAAU,KAAK,CAAC,GAAW,EAAA;IAC/B,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC;AAC1C;;;;;;;;ACVM,MAAO,QAAS,SAAQ,KAAK,CAAA;AACjC,IAAA,WAAA,CAAY,OAAe,EAAA;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KACxB;AACF,CAAA;MAiBY,iBAAiB,CAAA;AAC5B,IAAA,WAAA,CAAqB,KAAkB,EAAA;QAAlB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAa;KAAI;IAEpC,OAAO,KAAK,CAAC,IAAc,EAAA;;QAChC,MAAM,eAAe,GAAG,uBAAuB,CAAC;QAChD,MAAM,YAAY,GAAG,sBAAsB,CAAC;QAE5C,MAAM,YAAY,GAAgB,EAAE,CAAC;AACrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9B,YAAY,CAAC,GAAG,EAAE,CAAC;gBACnB,SAAS;AACV,aAAA;AAED,YAAA,MAAM,UAAU,GAAG,CAAA,EAAA,GAAA,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,EAAE;gBACf,SAAS;AACV,aAAA;YACD,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,SAAA;AACD,QAAA,OAAO,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC;KAC5C;AACF,CAAA;AAEK,MAAO,WAAY,SAAQ,KAAK,CAAA;IASpC,WACE,CAAA,SAAoB,EACpB,YAAoB,EACX,SAAmB,EACnB,IAAc,EACvB,MAAe,EACf,cAA+B,EAAA;AAE/B,QAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC;QALhD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAU;AAKvB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;QACjE,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzD;IAEM,OAAO,KAAK,CAAC,IAAc,EAAA;QAChC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAC7C,GAAG,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAC3C,CAAC;AACF,QAAA,IAAI,mBAAmB,KAAK,CAAC,CAAC,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACjD,QAAA,MAAM,SAAS,GAAG,CAAC,cAAc,CAAC,CAAC;AACnC,QAAA,IAAI,cAA0C,CAAC;AAC/C,QAAA,IAAI,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;;;;;;;YAOzC,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,KAAK,oBAAoB,EAAE;gBAC1D,MAAM,WAAW,GAAG,qBAAqB,CAAC;AAC1C,gBAAA,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;AACvE,gBAAA,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;AACxE,gBAAA,cAAc,GAAG;oBACf,IAAI,SAAS,CAAC,UAAU,CAAC;oBACzB,IAAI,SAAS,CAAC,WAAW,CAAC;iBAC3B,CAAC;AACF,gBAAA,SAAS,CAAC,IAAI,CACZ,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,CAAC,CAAC,CAChE,CAAC;AACH,aAAA;;;;;iBAKI,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE;gBACvE,MAAM,UAAU,GAAG,mCAAmC,CAAC;AACvD,gBAAA,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;AACrE,gBAAA,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;AACtE,gBAAA,SAAS,CAAC,IAAI,CACZ,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,CAAC,CAAC,CAChE,CAAC;AACF,gBAAA,cAAc,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAC1C,aAAA;AACF,SAAA;QACD,MAAM,WAAW,GACf,qGAAqG,CAAC;QACxG,MAAM,oBAAoB,GAAG,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,MAAM,aAAa,GACjB,iHAAiH,CAAC;QACpH,MAAM,sBAAsB,GAAG,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClE,MAAM,gBAAgB,GACpB,oHAAoH,CAAC;QACvH,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACxE,QAAA,IAAI,oBAAoB,EAAE;AACxB,YAAA,MAAM,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,CAAC,GAChD,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,YAAA,MAAM,SAAS,GAAG;AAChB,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;aAC9B,CAAC;AACF,YAAA,OAAO,IAAI,WAAW,CACpB,SAAS,EACT,YAAY,EACZ,SAAS,EACT,IAAI,EACJ,SAAS,EACT,cAAc,CACf,CAAC;AACH,SAAA;AAAM,aAAA,IAAI,sBAAsB,EAAE;YACjC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,CAAC,GAC5D,sBAAsB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,YAAA,MAAM,SAAS,GAAG;AAChB,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;aAC9B,CAAC;AACF,YAAA,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;AAChD,YAAA,OAAO,IAAI,WAAW,CACpB,SAAS,EACT,YAAY,EACZ,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,cAAc,CACf,CAAC;AACH,SAAA;AAAM,aAAA,IAAI,yBAAyB,EAAE;AACpC,YAAA,MAAM,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,CAAC,GAC7D,yBAAyB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,WAAW,CAAC;AAC3B,YAAA,MAAM,SAAS,GAAG;AAChB,gBAAA,IAAI,EAAE,eAAe;AACrB,gBAAA,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;aAC9B,CAAC;AACF,YAAA,OAAO,IAAI,WAAW,CACpB,SAAS,EACT,YAAY,EACZ,SAAS,EACT,IAAI,EACJ,MAAM,EACN,cAAc,CACf,CAAC;AACH,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;KACF;AAED,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CACzC,CAAC;KACH;AAED,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;KACtC;IAEM,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AACF,CAAA;AAED;AACM,MAAO,YAAa,SAAQ,KAAK,CAAA;AAGrC,IAAA,WAAA,CACW,IAAY,EACZ,GAAW,EACX,IAAe,EAAA;AAExB,QAAA,KAAK,EAAE,CAAC;QAJC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAQ;QACX,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAW;AAGxB,QAAA,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzD,SAAA;KACF;AAEM,IAAA,OAAO,KAAK,CACjB,GAAQ,EACR,SAA8B,EAAA;AAE9B,QAAA,MAAM,SAAS,GAAW,GAAG,CAAC,QAAQ,EAAE,CAAC;;;AAGzC,QAAA,IAAI,iBAAyB,CAAC;AAC9B,QAAA,IAAI,SAAS,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE;YAC/C,IAAI,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC3D,YAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3B,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AAAM,iBAAA;AACL,gBAAA,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,aAAA;AACF,SAAA;AAAM,aAAA;YACL,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACD,YAAA,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAE,CAAC,CAAC,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,IAAI,SAAiB,CAAC;QACtB,IAAI;AACF,YAAA,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AACzC,SAAA;AAAC,QAAA,OAAO,QAAQ,EAAE;AACjB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;;QAGD,IAAI,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACxD,SAAA;;AAGD,QAAA,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACxD,SAAA;;AAGD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAI,OAAO,GAAA;;AACT,QAAA,OAAO,MAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CACzC,CAAC;KACH;AAED,IAAA,IAAI,iBAAiB,GAAA;;AACnB,QAAA,OAAO,MAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;KACvC;IAEM,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;AACF,CAAA;AAEe,SAAA,cAAc,CAAC,GAAQ,EAAE,SAA8B,EAAA;AACrE,IAAA,IAAIC,KAAc,CAAC,YAAY,CAAC,EAAE;AAChC,QAAA,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;AACxC,KAAA;IAED,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChD,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;IAED,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACxD,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,YAAY,CAAC;AACrB,KAAA;IACD,IAAI,GAAG,CAAC,IAAI,EAAE;AACZ,QAAA,MAAM,OAAO,GAAG;AACd,YAAA,GAAG,EAAE,UAAU,MAAM,EAAE,IAAI,EAAA;gBACzB,IAAI,IAAI,KAAK,mBAAmB,EAAE;AAChC,oBAAA,OAAO,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACvC,iBAAA;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AAC7B,oBAAA,OAAO,MAAM,CAAC,iBAAiB,CAAC,KAAK,CACnC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CACvC,CAAC;AACH,iBAAA;AAAM,qBAAA;;;;AAIL,oBAAA,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;AAClC,iBAAA;aACF;SACF,CAAC;QACF,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAChC,KAAA;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAEY,MAAA,aAAa,GAAG;;AAE3B,IAAA,kBAAkB,EAAE,GAAG;AACvB,IAAA,2BAA2B,EAAE,GAAG;AAChC,IAAA,4BAA4B,EAAE,GAAG;AACjC,IAAA,0BAA0B,EAAE,GAAG;;AAG/B,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,4BAA4B,EAAE,IAAI;;AAGlC,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,gBAAgB,EAAE,IAAI;AACtB,IAAA,gBAAgB,EAAE,IAAI;AACtB,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,wBAAwB,EAAE,IAAI;AAC9B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,cAAc,EAAE,IAAI;AACpB,IAAA,mBAAmB,EAAE,IAAI;AACzB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,2BAA2B,EAAE,IAAI;AACjC,IAAA,6BAA6B,EAAE,IAAI;AACnC,IAAA,sBAAsB,EAAE,IAAI;AAC5B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,uBAAuB,EAAE,IAAI;;AAG7B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,sBAAsB,EAAE,IAAI;AAC5B,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,kBAAkB,EAAE,IAAI;;AAGxB,IAAA,8BAA8B,EAAE,IAAI;AACpC,IAAA,4BAA4B,EAAE,IAAI;AAClC,IAAA,4BAA4B,EAAE,IAAI;AAClC,IAAA,wBAAwB,EAAE,IAAI;AAC9B,IAAA,sBAAsB,EAAE,IAAI;AAC5B,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,iBAAiB,EAAE,IAAI;AACvB,IAAA,0BAA0B,EAAE,IAAI;AAChC,IAAA,gBAAgB,EAAE,IAAI;AACtB,IAAA,wBAAwB,EAAE,IAAI;AAC9B,IAAA,gBAAgB,EAAE,IAAI;AACtB,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,gCAAgC,EAAE,IAAI;AACtC,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,0BAA0B,EAAE,IAAI;AAChC,IAAA,wBAAwB,EAAE,IAAI;;AAG9B,IAAA,yBAAyB,EAAE,IAAI;;AAG/B,IAAA,UAAU,EAAE,IAAI;EAChB;AAEW,MAAA,gBAAgB,GAAG,IAAI,GAAG,CAAC;;AAEtC,IAAA;AACE,QAAA,aAAa,CAAC,kBAAkB;QAChC,4CAA4C;AAC7C,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,2BAA2B;QACzC,sCAAsC;AACvC,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,4BAA4B;QAC1C,yDAAyD;AAC1D,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,0BAA0B;QACxC,uDAAuD;AACxD,KAAA;;AAGD,IAAA;AACE,QAAA,aAAa,CAAC,kBAAkB;QAChC,mDAAmD;AACpD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,4BAA4B;QAC1C,sEAAsE;AACvE,KAAA;;AAGD,IAAA,CAAC,aAAa,CAAC,aAAa,EAAE,+BAA+B,CAAC;AAC9D,IAAA,CAAC,aAAa,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;AACrE,IAAA,CAAC,aAAa,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;AACpE,IAAA,CAAC,aAAa,CAAC,aAAa,EAAE,+BAA+B,CAAC;AAC9D,IAAA,CAAC,aAAa,CAAC,eAAe,EAAE,kCAAkC,CAAC;AACnE,IAAA;AACE,QAAA,aAAa,CAAC,oBAAoB;QAClC,0CAA0C;AAC3C,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,eAAe,EAAE,iCAAiC,CAAC;AAClE,IAAA,CAAC,aAAa,CAAC,oBAAoB,EAAE,uCAAuC,CAAC;AAC7E,IAAA;AACE,QAAA,aAAa,CAAC,eAAe;QAC7B,4DAA4D;AAC7D,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,oBAAoB,EAAE,uCAAuC,CAAC;AAC7E,IAAA;AACE,QAAA,aAAa,CAAC,wBAAwB;QACtC,4CAA4C;AAC7C,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,eAAe,EAAE,iCAAiC,CAAC;AAClE,IAAA,CAAC,aAAa,CAAC,iBAAiB,EAAE,oCAAoC,CAAC;AACvE,IAAA,CAAC,aAAa,CAAC,cAAc,EAAE,oCAAoC,CAAC;AACpE,IAAA,CAAC,aAAa,CAAC,mBAAmB,EAAE,sCAAsC,CAAC;AAC3E,IAAA,CAAC,aAAa,CAAC,oBAAoB,EAAE,uCAAuC,CAAC;AAC7E,IAAA;AACE,QAAA,aAAa,CAAC,2BAA2B;QACzC,+CAA+C;AAChD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,6BAA6B;QAC3C,iDAAiD;AAClD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,sBAAsB;QACpC,yCAAyC;AAC1C,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,eAAe,EAAE,iCAAiC,CAAC;AAClE,IAAA;AACE,QAAA,aAAa,CAAC,uBAAuB;QACrC,+CAA+C;AAChD,KAAA;;AAGD,IAAA,CAAC,aAAa,CAAC,eAAe,EAAE,mCAAmC,CAAC;AACpE,IAAA,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAsC,CAAC;AACzE,IAAA;AACE,QAAA,aAAa,CAAC,qBAAqB;QACnC,2CAA2C;AAC5C,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,kBAAkB,EAAE,uCAAuC,CAAC;AAC3E,IAAA;AACE,QAAA,aAAa,CAAC,sBAAsB;QACpC,4CAA4C;AAC7C,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAsC,CAAC;AACzE,IAAA,CAAC,aAAa,CAAC,kBAAkB,EAAE,uCAAuC,CAAC;;AAG3E,IAAA;AACE,QAAA,aAAa,CAAC,8BAA8B;QAC5C,2DAA2D;AAC5D,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,4BAA4B;QAC1C,kDAAkD;AACnD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,4BAA4B;QAC1C,sDAAsD;AACvD,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,wBAAwB,EAAE,mCAAmC,CAAC;AAC7E,IAAA,CAAC,aAAa,CAAC,sBAAsB,EAAE,iCAAiC,CAAC;AACzE,IAAA;AACE,QAAA,aAAa,CAAC,oBAAoB;QAClC,kDAAkD;AACnD,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,iBAAiB,EAAE,kCAAkC,CAAC;AACrE,IAAA;AACE,QAAA,aAAa,CAAC,0BAA0B;QACxC,iEAAiE;AAClE,KAAA;AACD,IAAA,CAAC,aAAa,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;AAClE,IAAA,CAAC,aAAa,CAAC,wBAAwB,EAAE,mCAAmC,CAAC;AAC7E,IAAA,CAAC,aAAa,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;AAClE,IAAA;AACE,QAAA,aAAa,CAAC,qBAAqB;QACnC,sDAAsD;AACvD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,qBAAqB;QACnC,6DAA6D;AAC9D,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,qBAAqB;QACnC,iDAAiD;AAClD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,gCAAgC;QAC9C,uDAAuD;AACxD,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,qBAAqB;QACnC,yDAAyD;AAC1D,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,0BAA0B;QACxC,wEAAwE;AACzE,KAAA;AACD,IAAA;AACE,QAAA,aAAa,CAAC,wBAAwB;QACtC,2DAA2D;AAC5D,KAAA;;AAGD,IAAA;AACE,QAAA,aAAa,CAAC,yBAAyB;QACvC,8DAA8D;AAC/D,KAAA;;AAGD,IAAA;AACE,QAAA,aAAa,CAAC,UAAU;QACxB,+DAA+D;AAChE,KAAA;AACF,CAAA;;ACphBD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACO,IAAIC,UAAQ,GAAG,WAAW;AACjC,IAAIA,UAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzF,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,MAAK;AACL,IAAI,OAAOA,UAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC3C;;ACxCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACO,IAAI,QAAQ,GAAG,WAAW;AACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzF,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,MAAK;AACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC3C;;ACxCA;AACA;AACA;AAuCA;AACA;AACA;AACO,SAAS,SAAS,CAAC,GAAG,EAAE;AAC/B,IAAI,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC7B;;AC7CA;AACA,IAAI,oBAAoB,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,CAAC;AAC1E;AACA,IAAI,oBAAoB,GAAG,cAAc,CAAC;AAC1C;AACA;AACA;AACO,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE;AACvC,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,oBAAoB,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,oBAAoB,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC/S,IAAI,IAAI,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACnF,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI;AACxC,QAAQ,KAAK,EAAE,CAAC;AAChB,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI;AAC1C,QAAQ,GAAG,EAAE,CAAC;AACd;AACA,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/E,CAAC;AACD;AACA;AACA;AACA,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE;AACnC,IAAI,IAAI,EAAE,YAAY,MAAM;AAC5B,QAAQ,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACvF;;AC3BO,SAAS,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE;AACxC,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAChE;;ACHO,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AAC1C,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,OAAO,OAAO,CAAC,KAAK,EAAEA,UAAQ,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AACjE;;MCCa,QAAQ,CAAA;AACZ,IAAA,OAAO,WAAW,CACvB,KAAiD,EACjD,KAAoB,EAAA;QAEpB,MAAM,SAAS,GACb,KAAK,CAAC,IAAI,KAAK,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;QAC/D,QAAQ,KAAK,CAAC,IAAI;YAChB,KAAK,MAAM,EAAE;AACX,gBAAA,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,aAAA;YACD,KAAK,IAAI,EAAE;AACT,gBAAA,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AAC5B,aAAA;YACD,KAAK,IAAI,EAAE;AACT,gBAAA,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AAC5B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,KAAK,EAAE;AACV,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,MAAM,EAAE;AACX,gBAAA,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,aAAA;YACD,KAAK,MAAM,EAAE;AACX,gBAAA,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,aAAA;YACD,KAAK,MAAM,EAAE;AACX,gBAAA,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,aAAA;YACD,KAAK,MAAM,EAAE;AACX,gBAAA,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,aAAA;YACD,KAAK,OAAO,EAAE;AACZ,gBAAA,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC/B,aAAA;YACD,KAAK,QAAQ,EAAE;AACb,gBAAA,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7B,aAAA;YACD,KAAK,WAAW,EAAE;AAChB,gBAAA,OAAO,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACnC,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;AACvB,oBAAA,OAAO,KAAK,CAAC,GAAG,CACd,QAAQ,CAAC,WAAW,CAClB;AACE,wBAAA,IAAI,EAAE,SAAS;AACf,wBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;AACrB,qBAAA,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;AACH,iBAAA;AAAM,qBAAA,IAAI,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;AACjC,oBAAA,OAAO,KAAK,CAAC,MAAM,CACjB,QAAQ,CAAC,WAAW,CAClB;AACE,wBAAA,IAAI,EAAE,SAAS;AACf,wBAAA,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;AACxB,qBAAA,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;AACH,iBAAA;AAAM,qBAAA,IAAI,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE;AAClC,oBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;oBAEnC,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,wBAAA,MAAM,IAAI,QAAQ,CAAC,iCAAiC,CAAC,CAAC;AACvD,qBAAA;AACD,oBAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;AACzD,oBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,wBAAA,MAAM,IAAI,QAAQ,CAAC,CAAA,gBAAA,EAAmB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAE,CAAA,CAAC,CAAC;AAChE,qBAAA;AACD,oBAAA,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AAC9D,iBAAA;AAAM,qBAAA,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;oBAChC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnC,oBAAA,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,CACpC;AACE,wBAAA,IAAI,EAAE,SAAS;AACf,wBAAA,IAAI,EAAE,OAAO;qBACd,EACD,KAAK,CACN,CAAC;oBACF,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACtD,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAA,CAAE,CAAC,CAAC;AAClD,iBAAA;AACF,aAAA;AACF,SAAA;KACF;IAEM,OAAO,aAAa,CACzB,OAAmB,EACnB,KAAsB,GAAA,EAAE,EACxB,IAAa,EAAA;AAEb,QAAA,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAClC,YAAA,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAI;gBACrD,MAAM,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC7C,gBAAA,OAAO,CAAC,CAAC;AACX,aAAC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACzC,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACvC,YAAA,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAuB,KAAI;gBACnE,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACrC,gBAAA,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;oBAChC,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/B,iBAAA;AACD,gBAAA,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CACrC,CAAC,CAAqB,EAAE,CAAS,KAAI;AACnC,oBAAA,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AAC7B,wBAAA,OAAO,QAAQ,CAAC,WAAW,CACzB,EAAE,IAAI,EAAE,CAAY,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,EAC1C,KAAK,CACN,CAAC;AACH,qBAAA;;;;;oBAKD,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAa,EAAE,KAAK,CAAC,CAAC;AACpD,iBAAC,CACF,CAAC;gBACF,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC1C,aAAC,CAAC,CAAC;YAEH,IAAI,IAAI,KAAK,SAAS,EAAE;;;gBAGtB,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACjD,aAAA;YAED,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACvC,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,IAAI,KAAK,CAAC,sBAAsB,OAAO,CAAA,CAAE,CAAC,CAAC;AAClD,SAAA;KACF;AACF;;AC9ID;;;AAGG;AACI,MAAM,wBAAwB,GAAG,QAAQ,CAAC;AAEjD;;AAEG;MACU,qBAAqB,CAAA;AAOhC,IAAA,WAAA,CAA2B,GAAQ,EAAA;QAAR,IAAG,CAAA,GAAA,GAAH,GAAG,CAAK;QACjC,IAAI,CAAC,QAAQ,GAAG,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAEzD,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;YAC9B,MAAM,EAAE,GAAG,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YACtD,cAAc,CAAC,GAAG,CAACN,MAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;gBAClC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;gBAClC,IAAI,EAAE,EAAE,CAAC,IAAI;AACd,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;KACtC;AAED;;AAEG;IACI,MAAM,CAAC,MAAc,EAAE,EAAO,EAAA;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;KAC3D;AAEO,IAAA,OAAO,CAAC,SAAiB,EAAE,MAAc,EAAE,EAAO,EAAA;QACxD,MAAM,MAAM,GAAGD,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClC,QAAA,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,mBAAmB,UAAU,CAAA,CAAE,CAAC,CAAC;AAClD,SAAA;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,QAAA,OAAOA,QAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KAC1D;IAEO,OAAO,aAAa,CAAC,GAAQ,EAAA;QACnC,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,KAA+B;YACvE,IAAI,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAa,KAAI;;AAC/C,gBAAA,OAAA,QAAQ,CAAC,WAAW,CAClB,GAAG,EACH,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,MAAA,GAAG,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC,EAAE,IAAI,CAAA,EAAA,GAAA,GAAG,CAAC,KAAK,mCAAI,EAAE,CAAC,CAAC,CAAC,CAC5D,CAAA;AAAA,aAAA,CACF,CAAC;YACF,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC,YAAA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;AAClD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;KAC3B;AAED;;AAEG;AACI,IAAA,MAAM,CACX,EAAmB,EACnB,QAAA,GAA6B,KAAK,EAAA;AAElC,QAAA,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC1B,EAAE,GAAG,QAAQ,KAAK,KAAK,GAAGA,QAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,GAAGC,MAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACpE,SAAA;AACD,QAAA,IAAI,OAAO,GAAGA,MAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACD,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;YACjC,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAC;KACH;AAED;;AAEG;IACI,MAAM,CACX,EAAe,EACf,YAA2B,EAAA;AAE3B,QAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;KAChE;AACF,CAAA;AAiBD,MAAM,oBAAoB,CAAA;AACjB,IAAA,OAAO,MAAM,CAClB,EAAe,EACf,YAA2B,EAC3B,GAAQ,EAAA;QAER,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC3C,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAI;YACvC,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,oBAAoB,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;AACvD,gBAAA,IAAI,EAAE,oBAAoB,CAAC,aAAa,CACtC,QAAQ,EACR,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EACtB,GAAG,CAAC,KAAK,CACV;aACF,CAAC;AACJ,SAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,oBAAoB,CAAC,kBAAkB,CAC7D,KAAK,CAAC,QAAQ,CACf,CAAC;QAEF,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,KAAI;AAC9C,YAAA,IAAI,GAAG,GAAG,eAAe,CAAC,MAAM,EAAE;gBAChC,OAAO;AACL,oBAAA,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI;AAC/B,oBAAA,GAAG,IAAI;iBACR,CAAC;AACH,aAAA;;AAEI,iBAAA;gBACH,OAAO;AACL,oBAAA,IAAI,EAAE,SAAS;AACf,oBAAA,GAAG,IAAI;iBACR,CAAC;AACH,aAAA;AACH,SAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI;YACJ,QAAQ;SACT,CAAC;KACH;IAEO,OAAO,aAAa,CAAC,OAAgB,EAAA;AAC3C,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC/B,YAAA,OAAO,OAAiB,CAAC;AAC1B,SAAA;QAED,IAAI,KAAK,IAAI,OAAO,EAAE;YACpB,OAAO,CAAA,IAAA,EAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA,CAAG,CAAC;AAClD,SAAA;QACD,IAAI,QAAQ,IAAI,OAAO,EAAE;YACvB,OAAO,CAAA,OAAA,EAAU,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,CAAA,CAAG,CAAC;AACxD,SAAA;QACD,IAAI,SAAS,IAAI,OAAO,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC;AACxB,SAAA;QACD,IAAI,OAAO,IAAI,OAAO,EAAE;AACtB,YAAA,OAAO,CAAS,MAAA,EAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAK,EAAA,EAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAC1D,SAAA;AAED,QAAA,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,CAAA,CAAE,CAAC,CAAC;KACjD;AAEO,IAAA,OAAO,aAAa,CAC1B,QAAkB,EAClB,IAAY,EACZ,KAAoB,EAAA;AAEpB,QAAA,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AACxB,SAAA;QACD,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACvC,YAAA,QACE,GAAG;gBACe,IAAK;qBACpB,GAAG,CAAC,CAAC,CAAW,KACf,IAAI,CAAC,aAAa,CAChB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAe,QAAQ,CAAC,IAAK,CAAC,GAAG,EAAE,EACnD,CAAC,CACF,CACF;qBACA,IAAI,CAAC,IAAI,CAAC;AACb,gBAAA,GAAG,EACH;AACH,SAAA;QACD,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC1C,OAAO,IAAI,KAAK,IAAI;AAClB,kBAAE,MAAM;kBACN,IAAI,CAAC,aAAa,CAChB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAkB,QAAQ,CAAC,IAAK,CAAC,MAAM,EAAE,EACzD,IAAI,EACJ,KAAK,CACN,CAAC;AACP,SAAA;QACD,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;YAC3C,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,aAAA;YACD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAC3B,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAsB,QAAQ,CAAC,IAAK,CAAC,OAAO,CAC1D,CAAC;AACF,YAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,MAAM,IAAI,KAAK,CACb,CAAoC,gBAAA,EAAA,QAAQ,CAAC,IAAK,CAAC,OAAO,CAAE,CAAA,CAC7D,CAAC;AACH,aAAA;AACD,YAAA,OAAO,oBAAoB,CAAC,oBAAoB,CAC9C,QAAQ,CAAC,CAAC,CAAC,EACX,IAAI,EACJ,KAAK,CACN,CAAC;AACH,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AAEO,IAAA,OAAO,oBAAoB,CACjC,OAAmB,EACnB,IAAY,EACZ,KAAmB,EAAA;AAEnB,QAAA,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAClC,YAAA,MAAM,MAAM,GAAuB,OAAO,CAAC,IAAI,CAAC;AAChD,YAAA,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7B,iBAAA,GAAG,CAAC,CAAC,CAAC,KAAI;gBACT,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,KAAK,SAAS,EAAE;AACnB,oBAAA,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACxC,iBAAA;AACD,gBAAA,QACE,CAAC,GAAG,IAAI,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAChE;AACJ,aAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAC;AACd,YAAA,OAAO,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;AAC7B,SAAA;AAAM,aAAA;YACL,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;;YAED,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AACjC,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B,gBAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtC,qBAAA,GAAG,CAAC,CAAC,CAAC,KAAI;;AACT,oBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,QAAQ,CAAC,OAAO,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,CACxC,CAAC,CAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAA,CAC7B,CAAC,CAAC,CAAC;oBACL,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,wBAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC3C,qBAAA;AACD,oBAAA,QACE,CAAC;wBACD,IAAI;wBACJ,oBAAoB,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,EAC9D;AACJ,iBAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAC;AAEd,gBAAA,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7D,gBAAA,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,oBAAA,OAAO,WAAW,CAAC;AACpB,iBAAA;AACD,gBAAA,OAAO,CAAG,EAAA,WAAW,CAAM,GAAA,EAAA,WAAW,IAAI,CAAC;AAC5C,aAAA;;AAEI,iBAAA;;AAEH,gBAAA,OAAO,sCAAsC,CAAC;AAC/C,aAAA;AACF,SAAA;KACF;AAEO,IAAA,OAAO,kBAAkB,CAC/B,QAA0B,EAC1B,MAAe,EAAA;AAEf,QAAA,OAAO,QAAQ;AACZ,aAAA,GAAG,CAAC,CAAC,OAAO,KAAI;YACf,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3C,YAAA,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AACtC,gBAAA,MAAM,SAAS,GAAG,MAAM,GAAG,CAAA,EAAG,MAAM,CAAA,GAAA,EAAM,OAAO,CAAE,CAAA,GAAG,OAAO,CAAC;gBAC9D,OAAO,oBAAoB,CAAC,kBAAkB,CAC9B,OAAQ,CAAC,QAAQ,EAC/B,SAAS,CACV,CAAC;AACH,aAAA;AAAM,iBAAA;gBACL,OAAO;AACL,oBAAA,GAAgB,OAAQ;AACxB,oBAAA,IAAI,EAAE,MAAM,GAAG,CAAA,EAAG,MAAM,CAAA,GAAA,EAAM,OAAO,CAAA,CAAE,GAAG,OAAO;iBAClD,CAAC;AACH,aAAA;AACH,SAAC,CAAC;AACD,aAAA,IAAI,EAAE,CAAC;KACX;AACF,CAAA;AAED,SAAS,YAAY,CAAC,KAAa,EAAA;IACjC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAChD,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED;AACA;AACA,SAAS,OAAO,CAAC,SAAiB,EAAE,MAAc,EAAA;AAChD,IAAA,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,IAAA,IAAI,QAAQ,GAAG,CAAA,EAAG,SAAS,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;AACtC,IAAA,OAAOD,QAAM,CAAC,IAAI,CAACQ,QAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnD;;AChWgB,SAAA,WAAW,CAAC,GAAQ,EAAE,UAAsB,EAAA;AAC1D,IAAA,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACnC,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAI;AAC5D,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACnB,gBAAA,OAAO,CAAC,CAAC;AACV,aAAA;YAED,OAAO,OAAO,CAAC,MAAM;AAClB,iBAAA,GAAG,CAAC,CAAC,CAAqB,KAAI;;gBAE7B,IAAI,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE;AAC3C,oBAAA,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACzB,iBAAA;;gBAGD,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AAC/B,aAAC,CAAC;AACD,iBAAA,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AACtC,KAAA;AAED,IAAA,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM;AAC1B,SAAA,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;AACA;AACA,SAAS,QAAQ,CAAC,GAAQ,EAAE,EAAW,EAAA;;AACrC,IAAA,QAAQ,EAAE;AACR,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,IAAI;AACP,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,IAAI;AACP,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,KAAK;AACR,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,EAAE,CAAC;AACZ,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,EAAE,CAAC;AACZ,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,EAAE,CAAC;AACZ,QAAA,KAAK,MAAM;AACT,YAAA,OAAO,EAAE,CAAC;AACZ,QAAA,KAAK,OAAO;AACV,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,CAAC,CAAC;AACX,QAAA,KAAK,WAAW;AACd,YAAA,OAAO,EAAE,CAAC;AACZ,QAAA;YACE,IAAI,KAAK,IAAI,EAAE,EAAE;AACf,gBAAA,OAAO,CAAC,CAAC;AACV,aAAA;YACD,IAAI,QAAQ,IAAI,EAAE,EAAE;gBAClB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;AACrC,aAAA;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,GAAG,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AACvE,gBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,oBAAA,MAAM,IAAI,QAAQ,CAAC,CAAA,gBAAA,EAAmB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAE,CAAA,CAAC,CAAC;AAC7D,iBAAA;AACD,gBAAA,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAE1B,gBAAA,OAAO,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAClC,aAAA;YACD,IAAI,OAAO,IAAI,EAAE,EAAE;gBACjB,IAAI,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,OAAO,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;AAC3C,aAAA;AACD,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAE,CAAA,CAAC,CAAC;AACzD,KAAA;AACH;;AC/FA;;AAEG;AACI,MAAM,kBAAkB,GAAG,EAAE;AAE9B,SAAU,aAAa,CAAC,QAAgB,EAAA;AAC5C,IAAA,OAAO,MAAM,CAAC,IAAI,CAACA,QAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACpE;;ACCA;;AAEG;MACU,kBAAkB,CAAA;AAa7B,IAAA,WAAA,CAAmB,GAAQ,EAAA;AACzB,QAAA,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;YAChC,OAAO;AACR,SAAA;QACD,MAAM,OAAO,GAAkB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,KAAI;AACtD,YAAA,OAAO,CAAC,GAAG,CAAC,IAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAChB;AAEM,IAAA,MAAM,MAAM,CAAU,WAAc,EAAE,OAAU,EAAA;QACrD,MAAM,MAAM,GAAGR,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,CAAA,CAAE,CAAC,CAAC;AACpD,SAAA;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACvC,IAAI,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACzE,OAAOA,QAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;KACpD;IAEM,MAAM,CAAU,WAAc,EAAE,IAAY,EAAA;;QAEjD,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AAC3E,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AAC3C,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAClD,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;KAChD;AAEM,IAAA,SAAS,CAAU,IAAY,EAAA;QACpC,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAClE,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAC1E,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACpD,SAAA;QAED,OAAO,IAAI,CAAC,eAAe,CAAI,WAAkB,EAAE,IAAI,CAAC,CAAC;KAC1D;IAEM,eAAe,CAAU,WAAc,EAAE,EAAU,EAAA;;QAExD,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,CAAA,CAAE,CAAC,CAAC;AACpD,SAAA;AACD,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC5B;IAEM,MAAM,CAAC,WAAc,EAAE,UAAmB,EAAA;QAC/C,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3E,OAAO;AACL,YAAA,MAAM,EAAE,CAAC;YACT,KAAK,EAAEC,MAAI,CAAC,MAAM,CAChB,UAAU,GAAGD,QAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,GAAG,aAAa,CACxE;SACF,CAAC;KACH;AAEM,IAAA,IAAI,CAAC,UAAsB,EAAA;;AAChC,QAAA,OAAO,kBAAkB,IAAI,CAAA,EAAA,GAAA,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,CAAC,CAAC;KACtE;AAED;;;;AAIG;IACI,OAAO,oBAAoB,CAAC,IAAY,EAAA;AAC7C,QAAA,MAAM,qBAAqB,GAAG,CAAA,QAAA,EAAWS,SAAS,CAAC,IAAI,EAAE;AACvD,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,4BAA4B,EAAE,IAAI;AACnC,SAAA,CAAC,EAAE,CAAC;AACL,QAAA,OAAO,aAAa,CAAC,qBAAqB,CAAC,CAAC;KAC7C;AACF;;MCnGY,eAAe,CAAA;AAW1B,IAAA,WAAA,CAAmB,GAAQ,EAAA;AACzB,QAAA,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;YACzB,OAAO;AACR,SAAA;QACD,MAAM,OAAO,GAA4B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAI;AAChE,YAAA,IAAI,YAAY,GAAe;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI;AAChB,gBAAA,IAAI,EAAE;AACJ,oBAAA,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAI;AAC7B,wBAAA,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AACxC,qBAAC,CAAC;AACH,iBAAA;aACF,CAAC;AACF,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,YAAY,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhC,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAC3B,GAAG,CAAC,MAAM,KAAK,SAAS;AACtB,cAAE,EAAE;cACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;gBACpBP,QAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAChD,gBAAA,CAAC,CAAC,IAAI;AACP,aAAA,CAAC,CACP,CAAC;KACH;AAEM,IAAA,MAAM,CACX,GAAW,EAAA;AAEX,QAAA,IAAI,MAAc,CAAC;;QAEnB,IAAI;AACF,YAAA,MAAM,GAAGF,QAAM,CAAC,IAAI,CAACE,QAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,MAAM,IAAI,GAAGA,QAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAGtD,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,SAAS,CAAA,CAAE,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAGzC,CAAC;AACF,QAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KAClC;AACF,CAAA;AAEK,SAAU,kBAAkB,CAAC,IAAY,EAAA;AAC7C,IAAA,OAAO,aAAa,CAAC,CAAA,MAAA,EAAS,IAAI,CAAA,CAAE,CAAC,CAAC;AACxC;;AC3EA;;AAEG;MACU,eAAe,CAAA;AAW1B,IAAA,WAAA,CAAmB,GAAQ,EAAA;AACzB,QAAA,IAAI,GAAG,CAAC,KAAK,KAAK,SAAS,EAAE;AAC3B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;YAC7B,OAAO;AACR,SAAA;QACD,MAAM,OAAO,GAAkB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAI;AACnD,YAAA,OAAO,CAAC,GAAG,CAAC,IAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAChB;IAEM,MAAM,CAAU,QAAW,EAAE,IAAO,EAAA;QACzC,MAAM,MAAM,GAAGF,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAA,CAAE,CAAC,CAAC;AAC9C,SAAA;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;KAC7B;IAEM,MAAM,CAAU,QAAW,EAAE,QAAgB,EAAA;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAA,CAAE,CAAC,CAAC;AAC9C,SAAA;AACD,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAChC;AACF;;ACvCD;;;AAGG;MACU,UAAU,CAAA;AAuBrB,IAAA,WAAA,CAAY,GAAQ,EAAA;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;KACvC;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwFD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AACjC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qBAAqB,GAAG;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,QAAQ,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACzD,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5B,IAAI,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,CAAC;AACD,IAAc,QAAA,GAAG,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE;AACpC,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE;AACnB,IAAI,OAAO,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,QAAQ,GAAG,GAAG,CAAC;AAC1C,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AA4DD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,cAAc,SAAS,MAAM,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAClD,GAAG;AACH,CAAC;AAoDD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,SAAS,cAAc,CAAC;AAC1C,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AACxC,IAAI,IAAI,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE;AACrC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL;AACA,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC1C,MAAM,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;AACjE,KAAK;AACL;AACA,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;AACpD;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,YAAY,IAAI;AACxC,gBAAgB,IAAI,CAAC,MAAM,YAAY,MAAM,CAAC,EAAE;AAChD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACvD,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5D,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,MAAM,CAAC;AAC1B,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,SAAS,MAAM,CAAC;AAC5B,EAAE,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9B,IAAI,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AAqFD;AACA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9B;AACA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AACvC,EAAE,MAAM,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtB,CAAC;AACD;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE;AAClC,EAAE,OAAO,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;AAC7B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,SAAS,MAAM,CAAC;AAChC,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AAuCD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,MAAM,CAAC;AAC/B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,IAAI,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,CAAC;AA8RD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,SAAS,MAAM,CAAC;AAC/B,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE;AAChD,IAAI,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAC/B,aAAa,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;AAC7E,MAAM,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,QAAQ;AACtC,YAAY,SAAS,KAAK,cAAc,CAAC,EAAE;AAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC;AAChC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,KAAK;AACL;AACA;AACA,IAAI,KAAK,MAAM,EAAE,IAAI,MAAM,EAAE;AAC7B,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI;AACtB,cAAc,SAAS,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAChF,OAAO;AACP,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI;AACR,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;AAC3C,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI;AACR,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK;AAC9C,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1C,QAAQ,MAAM,IAAI,GAAG,CAAC;AACtB,QAAQ,OAAO,IAAI,GAAG,GAAG,CAAC;AAC1B,OAAO,EAAE,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,MAAM,IAAI,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACjD,OAAO;AACP,MAAM,MAAM,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,MAAM,IAAI,IAAI,CAAC,cAAc;AAC7B,cAAc,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE;AACpC,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC;AAC/B,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;AACtB,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACxC,MAAM,IAAI,SAAS,KAAK,EAAE,CAAC,QAAQ,EAAE;AACrC,QAAQ,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACpC,QAAQ,IAAI,SAAS,KAAK,EAAE,EAAE;AAC9B,UAAU,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,UAAU,IAAI,CAAC,GAAG,IAAI,EAAE;AACxB;AACA;AACA,YAAY,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzC,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,MAAM,MAAM,IAAI,IAAI,CAAC;AACrB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,UAAU,GAAG,SAAS,IAAI,WAAW,CAAC;AAClD,GAAG;AACH;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,QAAQ;AACpC,cAAc,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,QAAQ,EAAE;AACtB,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACpC,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACtC,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,IAAI,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACpC,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AACvB,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAC;AACpB,OAAO,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;AAC9B,QAAQ,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACtD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACtD,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,wBAAwB,SAAS,kBAAkB,CAAC;AAC1D,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,EAAE,CAAC,MAAM,YAAY,cAAc;AAC3C,aAAa,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;AAChC,MAAM,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC;AACpD;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9C,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,SAAS,MAAM,CAAC;AAC3B,EAAE,WAAW,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;AAC9C,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,YAAY,IAAI;AACvC,mBAAmB,KAAK,YAAY,MAAM,CAAC,CAAC,CAAC;AAC7C,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,KAAK,GAAG,IAAI,wBAAwB,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,KAAK,MAAM,IAAI,CAAC,KAAK,YAAY,cAAc;AAC/C,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE;AACnC,MAAM,KAAK,GAAG,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;AAClD,KAAK,MAAM,IAAI,EAAE,KAAK,YAAY,kBAAkB,CAAC,EAAE;AACvD,MAAM,MAAM,IAAI,SAAS,CAAC,qCAAqC;AAC/D,4BAA4B,+BAA+B,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,aAAa,EAAE;AACrC,MAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,KAAK;AACL,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,aAAa;AACjC,cAAc,aAAa,YAAY,MAAM,CAAC,CAAC,EAAE;AACjD,MAAM,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE;AAChC,MAAM,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,OAAO;AACP,MAAM,IAAI,SAAS,KAAK,aAAa,CAAC,QAAQ,EAAE;AAChD,QAAQ,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC3D,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;AAChC,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE;AAC9B,QAAQ,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAClC,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB;AACA;AACA,IAAI,IAAI,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,gBAAgB,GAAG,SAAS,GAAG,EAAE;AAC1C,MAAM,OAAO,qBAAqB,CAAC,GAAG,CAAC,CAAC;AACxC,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,sBAAsB,GAAG,SAAS,GAAG,EAAE;AAChD,MAAM,qBAAqB,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL;AACA;AACA;AACA,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,uBAAuB,CAAC,GAAG,EAAE;AAC/B,IAAI,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AACzD,MAAM,IAAI,IAAI,CAAC,aAAa;AAC5B,aAAa,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC9D,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClE,MAAM,IAAI,GAAG;AACb,cAAc,CAAC,CAAC,GAAG,CAAC,MAAM;AAC1B,iBAAiB,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;AACpD,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC9C,UAAU,OAAO,GAAG,CAAC;AACrB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,CAAC;AACb,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACnC,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACxC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnC,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;AAC3B,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC;AAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AAC/B,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACxC,QAAQ,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AACxC,OAAO;AACP,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC1C,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAChF,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC3C,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;AAC3B,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACxC,QAAQ,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AACxC,OAAO;AACP,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/C,MAAM,OAAO,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5D,wCAAwC,MAAM,GAAG,aAAa,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACtC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;AACxC,IAAI,MAAM,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AAChC,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC7B,MAAM,IAAI,SAAS,KAAK,MAAM,EAAE;AAChC,QAAQ,MAAM,GAAG,CAAC,CAAC;AACnB,OAAO;AACP,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAClC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,SAAS,MAAM,CAAC;AACnC,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;AAChD,IAAI,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE;AACnC,MAAM,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE;AACvD,MAAM,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,MAAM;AACnC,YAAY,SAAS,KAAK,QAAQ,CAAC,EAAE;AACrC,MAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAI,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE;AACvC,QAAQ,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACvD,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,aAAa;AACvC,cAAc,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC;AAC/B,cAAc,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACvD,QAAQ,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACzE,OAAO;AACP,MAAM,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AACxC,QAAQ,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;AACnE,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE;AACxB,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,uBAAuB,EAAE;AACxD,QAAQ,IAAI,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAChD,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;AACxB;AACA;AACA,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL;AACA,IAAI,OAAO,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC1E,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;AACnD,MAAM,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AACnD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM;AACnB,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AACjD,MAAM,MAAM,IAAI,SAAS,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7D,IAAI,IAAI,IAAI,GAAG,aAAa,CAAC;AAC7B,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AACxE,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;AAC7D,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI;AAC/B,cAAc,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACvC,QAAQ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AACrE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,CAAC;AAwUD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,MAAI,SAAS,MAAM,CAAC;AAC1B,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM,YAAY,cAAc,KAAK,MAAM,CAAC,OAAO,EAAE;AACjE,cAAc,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;AAC3D,MAAM,MAAM,IAAI,SAAS,CAAC,kCAAkC;AAC5D,4BAA4B,uCAAuC,CAAC,CAAC;AACrE,KAAK;AACL;AACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,EAAE,MAAM,YAAY,cAAc,CAAC,EAAE;AAC7C,MAAM,IAAI,GAAG,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AACrB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;AAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE;AACpB,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;AAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE;AACzB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,MAAM,YAAY,cAAc,EAAE;AAC/C,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC9B,cAAc,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE;AACrC,MAAM,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC;AAC/D,4BAA4B,oBAAoB,GAAG,IAAI,GAAG,iBAAiB,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE;AACpC,MAAM,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,IAAI,CAAC,MAAM,YAAY,cAAc,EAAE;AAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AA0OD;AACA;AACA,IAAA,MAAA,IAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,KAAK,IAAI,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC5F;AACA;AACA;AACA,IAAU,EAAA,GAAA,CAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AASjD;AACA;AACA;AACA,IAAW,GAAA,GAAA,CAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AASlD;AACA;AACA;AACA,IAAY,IAAA,GAAA,CAAI,QAAQ,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;AAiDtD;AACA;AACA;AACA,IAAY,IAAA,GAAA,CAAI,QAAQ,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AAqCrD;AACA;AACA,IAAA,MAAA,IAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;AAOzG;AACA;AACA,IAAA,KAAA,IAAiB,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;AAIhG;AACA;AACA,IAAA,IAAA,GAAgB,CAAA,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAIA,MAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;;MCxoFpD,sBAAsB,CAAA;;IAEjC,WAAY,CAAA,CAAM,KAAI;IAEtB,MAAM,CAAC,MAAc,EAAE,EAAO,EAAA;AAC5B,QAAA,QAAQ,SAAS,CAAC,MAAM,CAAC;YACvB,KAAK,eAAe,EAAE;AACpB,gBAAA,OAAO,mBAAmB,CAAC,EAAE,CAAC,CAAC;AAChC,aAAA;YACD,KAAK,QAAQ,EAAE;AACb,gBAAA,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC;AACzB,aAAA;YACD,KAAK,UAAU,EAAE;AACf,gBAAA,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;AAC3B,aAAA;YACD,KAAK,uBAAuB,EAAE;AAC5B,gBAAA,OAAO,2BAA2B,CAAC,EAAE,CAAC,CAAC;AACxC,aAAA;YACD,KAAK,qBAAqB,EAAE;AAC1B,gBAAA,OAAO,yBAAyB,CAAC,EAAE,CAAC,CAAC;AACtC,aAAA;YACD,KAAK,sBAAsB,EAAE;AAC3B,gBAAA,OAAO,0BAA0B,CAAC,EAAE,CAAC,CAAC;AACvC,aAAA;YACD,KAAK,wBAAwB,EAAE;AAC7B,gBAAA,OAAO,4BAA4B,CAAC,EAAE,CAAC,CAAC;AACzC,aAAA;YACD,KAAK,uBAAuB,EAAE;AAC5B,gBAAA,OAAO,2BAA2B,CAAC,EAAE,CAAC,CAAC;AACxC,aAAA;YACD,KAAK,UAAU,EAAE;AACf,gBAAA,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;AAC3B,aAAA;YACD,KAAK,kBAAkB,EAAE;AACvB,gBAAA,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACnC,aAAA;YACD,KAAK,gBAAgB,EAAE;AACrB,gBAAA,OAAO,oBAAoB,CAAC,EAAE,CAAC,CAAC;AACjC,aAAA;YACD,KAAK,kBAAkB,EAAE;AACvB,gBAAA,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACnC,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAA,CAAE,CAAC,CAAC;AACnD,aAAA;AACF,SAAA;KACF;IAED,WAAW,CAAC,OAAe,EAAE,GAAQ,EAAA;AACnC,QAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;AACF,CAAA;AAED,MAAM,gBAAiB,SAAQC,QAAkC,CAAA;AAgB/D,IAAA,WAAA,CAAmB,QAAiB,EAAA;AAClC,QAAA,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QADH,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;AAfpC,QAAA,IAAA,CAAA,MAAM,GAAGC,MAAmB,CAO1B;AACE,YAAAC,GAAgB,CAAC,QAAQ,CAAC;AAC1B,YAAAA,GAAgB,CAAC,eAAe,CAAC;AACjC,YAAAC,IAAiB,CAACC,MAAmB,CAACF,GAAgB,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AACxE,SAAA,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;KAID;AAED,IAAA,MAAM,CAAC,GAAkB,EAAE,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AAC9C,QAAA,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACzB,SAAA;AAED,QAAA,MAAM,IAAI,GAAG;YACX,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;SAChC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;KAC5C;AAED,IAAA,MAAM,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AAC1B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;KACjC;AAED,IAAA,OAAO,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AAC3B,QAAA,QACEA,GAAgB,EAAE,CAAC,IAAI;AACvB,YAAAA,GAAgB,EAAE,CAAC,IAAI;YACvB,IAAI,EAAE,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,EACxE;KACH;AACF,CAAA;AAED,SAAS,gBAAgB,CAAC,QAAgB,EAAA;AACxC,IAAA,OAAO,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED,SAASG,WAAS,CAAC,QAAgB,EAAA;IACjC,OAAOF,IAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAO,EAAA;AAC1D,IAAA,OAAO,UAAU,CAAC;AAChB,QAAA,aAAa,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE;AAC5D,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,EAAE,KAAK,EAAO,EAAA;AAClC,IAAA,OAAO,UAAU,CAAC;QAChB,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE;AACpC,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,QAAQ,EAAO,EAAA;AACvC,IAAA,OAAO,UAAU,CAAC;QAChB,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACvB,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAAC,EACnC,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,KAAK,GACD,EAAA;AACJ,IAAA,OAAO,UAAU,CACf;AACE,QAAA,qBAAqB,EAAE;AACrB,YAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;YACJ,QAAQ;YACR,KAAK;AACL,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACxB,SAAA;AACF,KAAA,EACD,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACxC,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CAAC,EAAE,UAAU,EAAO,EAAA;AACvD,IAAA,OAAO,UAAU,CAAC;QAChB,sBAAsB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;AAC9D,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAAC,EAAE,UAAU,EAAO,EAAA;AACpD,IAAA,OAAO,UAAU,CAAC;QAChB,mBAAmB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;AAC3D,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,0BAA0B,CAAC,EAAE,QAAQ,EAAO,EAAA;AACnD,IAAA,OAAO,UAAU,CAAC;QAChB,oBAAoB,EAAE,EAAE,QAAQ,EAAE;AACnC,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAAC,EAAE,UAAU,EAAO,EAAA;AACtD,IAAA,OAAO,UAAU,CAAC;QAChB,qBAAqB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;AAC7D,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,KAAK,EAAO,EAAA;AACpC,IAAA,OAAO,UAAU,CAAC;QAChB,QAAQ,EAAE,EAAE,KAAK,EAAE;AACpB,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAO,EAAA;AAC/D,IAAA,OAAO,UAAU,CACf;AACE,QAAA,gBAAgB,EAAE;AAChB,YAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;YACJ,KAAK;AACL,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACxB,SAAA;AACF,KAAA,EACD,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACxC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAO,EAAA;AACtD,IAAA,OAAO,UAAU,CACf;AACE,QAAA,cAAc,EAAE;AACd,YAAA,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;AACJ,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACxB,SAAA;AACF,KAAA,EACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACzC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAO,EAAA;AAC5D,IAAA,OAAO,UAAU,CACf;AACE,QAAA,gBAAgB,EAAE;YAChB,QAAQ;YACR,IAAI;AACJ,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACxB,SAAA;AACF,KAAA,EACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACzC,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAGG,KAAkB,CAACJ,GAAgB,CAAC,aAAa,CAAC,CAAC,CAAC;AACnE,MAAM,CAAC,UAAU,CACf,CAAC,EACDD,MAAmB,CAAC;AAClB,IAAAM,IAAiB,CAAC,UAAU,CAAC;AAC7B,IAAAA,IAAiB,CAAC,OAAO,CAAC;IAC1BF,WAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,eAAe,CAChB,CAAC;AACF,MAAM,CAAC,UAAU,CAAC,CAAC,EAAEJ,MAAmB,CAAC,CAACI,WAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1E,MAAM,CAAC,UAAU,CACf,CAAC,EACDJ,MAAmB,CAAC,CAACM,IAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,EACpD,UAAU,CACX,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDN,MAAmB,CAAC;IAClBI,WAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;AACxB,IAAAE,IAAiB,CAAC,UAAU,CAAC;AAC7B,IAAAA,IAAiB,CAAC,OAAO,CAAC;IAC1BF,WAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,uBAAuB,CACxB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDJ,MAAmB,CAAC,CAACI,WAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,qBAAqB,CACtB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDJ,MAAmB,CAAC,CAACM,IAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,EACpD,sBAAsB,CACvB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDN,MAAmB,CAAC,CAACI,WAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,wBAAwB,CACzB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDJ,MAAmB,CAAC,CAACI,WAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,uBAAuB,CACxB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDJ,MAAmB,CAAC,CAACM,IAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EACjD,UAAU,CACX,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACDN,MAAmB,CAAC;IAClBI,WAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;AACxB,IAAAE,IAAiB,CAAC,OAAO,CAAC;IAC1BF,WAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,kBAAkB,CACnB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,EAAE,EACFJ,MAAmB,CAAC;IAClBI,WAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;IACxBA,WAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,gBAAgB,CACjB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,EAAE,EACFJ,MAAmB,CAAC;AAClB,IAAAM,IAAiB,CAAC,UAAU,CAAC;IAC7B,gBAAgB,CAAC,MAAM,CAAC;IACxBF,WAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,kBAAkB,CACnB,CAAC;AAEF,SAAS,UAAU,CAAC,WAAgB,EAAE,OAAgB,EAAA;AACpD,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,kBAAkB,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAE3C,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACzB,KAAA;AAED,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CACjC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAC1D;;MCtTY,mBAAmB,CAAA;AAG9B,IAAA,WAAA,CAAoB,GAAQ,EAAA;QAAR,IAAG,CAAA,GAAA,GAAH,GAAG,CAAK;KAAI;AAEzB,IAAA,MAAM,MAAM,CAAU,WAAc,EAAE,OAAU,EAAA;AACrD,QAAA,QAAQ,WAAW;YACjB,KAAK,OAAO,EAAE;gBACZ,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBAClD,MAAM,GAAG,GAAG,oBAAoB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACzD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC7B,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,WAAW,CAAA,CAAE,CAAC,CAAC;AACzD,aAAA;AACF,SAAA;KACF;IAEM,MAAM,CAAU,WAAc,EAAE,EAAU,EAAA;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC9C;IAEM,eAAe,CAAU,WAAc,EAAE,EAAU,EAAA;AACxD,QAAA,QAAQ,WAAW;YACjB,KAAK,OAAO,EAAE;AACZ,gBAAA,OAAO,kBAAkB,CAAC,EAAE,CAAC,CAAC;AAC/B,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,WAAW,CAAA,CAAE,CAAC,CAAC;AACzD,aAAA;AACF,SAAA;KACF;;IAGM,MAAM,CAAC,WAAc,EAAE,WAAoB,EAAA;AAChD,QAAA,QAAQ,WAAW;YACjB,KAAK,OAAO,EAAE;gBACZ,OAAO;AACL,oBAAA,QAAQ,EAAE,oBAAoB;iBAC/B,CAAC;AACH,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,WAAW,CAAA,CAAE,CAAC,CAAC;AACzD,aAAA;AACF,SAAA;KACF;AAEM,IAAA,IAAI,CAAC,UAAsB,EAAA;;QAChC,OAAO,CAAA,EAAA,GAAA,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,CAAC;KAC/C;AACF,CAAA;AAED,SAAS,kBAAkB,CAAU,EAAU,EAAA;AAC7C,IAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAM,CAAC;AAC9C,CAAC;AAED,MAAMG,eAAoB,SAAQR,QAAsB,CAAA;AAKtD,IAAA,WAAA,CACE,MAA8B,EAC9B,OAAuB,EACvB,OAAsB,EACtB,QAAiB,EAAA;AAEjB,QAAA,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACxB;IAED,MAAM,CAAC,CAAS,EAAE,MAAe,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KACpD;AAED,IAAA,MAAM,CAAC,GAAM,EAAE,CAAS,EAAE,MAAe,EAAA;AACvC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;KACzD;IAED,OAAO,CAAC,CAAS,EAAE,MAAe,EAAA;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;KACvC;AACF,CAAA;AAED,SAASK,WAAS,CAAC,QAAiB,EAAA;AAClC,IAAA,OAAO,IAAIG,eAAa,CACtBL,IAAiB,CAAC,EAAE,CAAC,EACrB,CAAC,CAAS,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAc,KAAK,GAAG,CAAC,QAAQ,EAAE,EAClC,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB,GAAGF,MAAmB,CAAC;AAC/C,IAAAC,GAAgB,CAAC,SAAS,CAAC;AAC3B,IAAAA,GAAgB,CAAC,OAAO,CAAC;IACzBG,WAAS,CAAC,kBAAkB,CAAC;IAC7BA,WAAS,CAAC,OAAO,CAAC;AAClB,IAAAJ,MAAmB,CACjB,CAACQ,IAAiB,CAAC,sBAAsB,CAAC,CAAC,EAC3C,eAAe,CAChB;AACF,CAAA,CAAC;;MCzGW,iBAAiB,CAAA;IAC5B,WAAY,CAAA,IAAS,KAAI;AAEzB,IAAA,MAAM,CACJ,IAAY,EAAA;AAEZ,QAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;KACxD;AACF;;MCVY,gBAAgB,CAAA;IAC3B,WAAY,CAAA,IAAS,KAAI;IAEzB,MAAM,CAAU,KAAa,EAAE,KAAQ,EAAA;AACrC,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IACD,MAAM,CAAU,KAAa,EAAE,SAAiB,EAAA;AAC9C,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;AACF;;ACLD;;AAEG;MACU,WAAW,CAAA;AAMtB,IAAA,WAAA,CAAY,GAAQ,EAAA;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;KACxC;AACF;;ACpBK,SAAU,IAAI,CAAC,IAAY,EAAA;IAC/B,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAACZ,QAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD;;;;;;;ACCA;SACgB,kBAAkB,CAChC,aAAwB,EACxB,IAAY,EACZ,SAAoB,EAAA;AAEpB,IAAA,MAAM,MAAM,GAAGR,QAAM,CAAC,MAAM,CAAC;QAC3B,aAAa,CAAC,QAAQ,EAAE;AACxB,QAAAA,QAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACjB,SAAS,CAAC,QAAQ,EAAE;AACrB,KAAA,CAAC,CAAC;IACH,OAAO,IAAI,SAAS,CAACQ,QAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AACvC,CAAC;SAEe,UAAU,CACxB,SAAkB,EAClB,GAAG,IAA6B,EAAA;IAEhC,IAAI,KAAK,GAAG,CAACR,QAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,IAAA,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;QACnB,KAAK,CAAC,IAAI,CAAC,GAAG,YAAYA,QAAM,GAAG,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7E,KAAC,CAAC,CAAC;AACH,IAAA,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,sBAAsB,CAC9C,KAAK,EACL,gBAAgB,CAAC,SAAS,CAAC,CAC5B,CAAC;AACF,IAAA,OAAO,KAAK,CAAC;AACf;;;;;;;;AC9BO,MAAM,gBAAgB,GAAG,IAAI,SAAS,CAC3C,6CAA6C,CAC9C,CAAC;AACK,MAAM,qBAAqB,GAAG,IAAI,SAAS,CAChD,8CAA8C,CAC/C,CAAC;SAEc,iBAAiB,CAAC,EAChC,IAAI,EACJ,KAAK,GAIN,EAAA;IACC,OAAO,SAAS,CAAC,sBAAsB,CACrC,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,gBAAgB,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAChE,qBAAqB,CACtB,CAAC,CAAC,CAAC,CAAC;AACP;;;;;;;;;;;;ACpBA,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAGqB,cAAI,CAAC;AACvD,IAAI,QAAQ,GAAG,CAAC,YAAY;AAC5B,SAAS,CAAC,GAAG;AACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,aAAY;AACvC,CAAC;AACD,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC;AACrB,OAAO,IAAI,CAAC,EAAE,CAAC;AACf,CAAC,GAAG,CAAC;AACL,CAAC,SAAS,IAAI,EAAE;AAChB;AACiB,EAAC,UAAU,OAAO,EAAE;AACrC;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,YAAY,EAAE,iBAAiB,IAAI,IAAI;AAC3C,IAAI,QAAQ,EAAE,QAAQ,IAAI,IAAI,IAAI,UAAU,IAAI,MAAM;AACtD,IAAI,IAAI;AACR,MAAM,YAAY,IAAI,IAAI;AAC1B,MAAM,MAAM,IAAI,IAAI;AACpB,MAAM,CAAC,WAAW;AAClB,QAAQ,IAAI;AACZ,UAAU,IAAI,IAAI,EAAE,CAAC;AACrB,UAAU,OAAO,IAAI;AACrB,SAAS,CAAC,OAAO,CAAC,EAAE;AACpB,UAAU,OAAO,KAAK;AACtB,SAAS;AACT,OAAO,GAAG;AACV,IAAI,QAAQ,EAAE,UAAU,IAAI,IAAI;AAChC,IAAI,WAAW,EAAE,aAAa,IAAI,IAAI;AACtC,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE;AAC3B,IAAI,OAAO,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC;AACvD,GAAG;AACH;AACA,EAAE,IAAI,OAAO,CAAC,WAAW,EAAE;AAC3B,IAAI,IAAI,WAAW,GAAG;AACtB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,uBAAuB;AAC7B,KAAK,CAAC;AACN;AACA,IAAI,IAAI,iBAAiB;AACzB,MAAM,WAAW,CAAC,MAAM;AACxB,MAAM,SAAS,GAAG,EAAE;AACpB,QAAQ,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACnF,OAAO,CAAC;AACR,GAAG;AACH;AACA,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAChD,MAAM,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;AACnE,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B,GAAG;AACH;AACA,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE;AACjC,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA;AACA,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,IAAI,QAAQ,GAAG;AACnB,MAAM,IAAI,EAAE,WAAW;AACvB,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAClC,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;AACxD,OAAO;AACP,KAAK,CAAC;AACN;AACA,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC1B,MAAM,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW;AAC7C,QAAQ,OAAO,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,OAAO,QAAQ;AACnB,GAAG;AACH;AACA,EAAE,SAAS,OAAO,CAAC,OAAO,EAAE;AAC5B,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB;AACA,IAAI,IAAI,OAAO,YAAY,OAAO,EAAE;AACpC,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE;AAC5C,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACvC,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,MAAM,EAAE;AACvC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,KAAK,MAAM,IAAI,OAAO,EAAE;AACxB,MAAM,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE;AACjE,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE;AACnD,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAChE,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,SAAS,IAAI,EAAE;AAC/C,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AACzC,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE;AACzC,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI;AACjD,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE;AACzC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACvD,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE;AAChD,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1D,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,EAAE,OAAO,EAAE;AAC1D,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;AAC/B,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AACzC,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC3D,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW;AACtC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE;AACvC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;AAC7B,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW;AACxC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE;AACjC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;AAC7B,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,WAAW;AACzC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE;AACvC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;AAC7B,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;AACnE,GAAG;AACH;AACA,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,GAAG;AACH;AACA,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;AACnC,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;AACjD,MAAM,MAAM,CAAC,MAAM,GAAG,WAAW;AACjC,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC/B,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,OAAO,GAAG,WAAW;AAClC,QAAQ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7B,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,SAAS,qBAAqB,CAAC,IAAI,EAAE;AACvC,IAAI,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAClC,IAAI,IAAI,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,OAAO,OAAO;AAClB,GAAG;AACH;AACA,EAAE,SAAS,cAAc,CAAC,IAAI,EAAE;AAChC,IAAI,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAClC,IAAI,IAAI,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,OAAO,OAAO;AAClB,GAAG;AACH;AACA,EAAE,SAAS,qBAAqB,CAAC,GAAG,EAAE;AACtC,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACzB,GAAG;AACH;AACA,EAAE,SAAS,WAAW,CAAC,GAAG,EAAE;AAC5B,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE;AACnB,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,IAAI,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAChD,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,MAAM,OAAO,IAAI,CAAC,MAAM;AACxB,KAAK;AACL,GAAG;AACH;AACA,EAAE,SAAS,IAAI,GAAG;AAClB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE;AACpC,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC5B,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,OAAO,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC3C,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,OAAO,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACrE,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,OAAO,MAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC7E,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,OAAO,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACxF,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACzC,OAAO,MAAM,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;AAC1E,QAAQ,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3D,OAAO,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,EAAE;AAChH,QAAQ,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AAClD,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,OAAO;AACP;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AAC7C,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACtC,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;AACvE,SAAS,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AAC1D,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAChE,SAAS,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC1F,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iDAAiD,CAAC,CAAC;AAC9F,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN;AACA,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE;AACtB,MAAM,IAAI,CAAC,IAAI,GAAG,WAAW;AAC7B,QAAQ,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,OAAO,QAAQ;AACzB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;AAC5B,UAAU,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;AAChD,SAAS,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC1C,UAAU,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACnE,SAAS,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AACvC,UAAU,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC;AACjE,SAAS,MAAM;AACf,UAAU,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC5D,SAAS;AACT,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,WAAW,GAAG,WAAW;AACpC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACnC,UAAU,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACzE,SAAS,MAAM;AACf,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACxD,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW;AAC3B,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,OAAO,QAAQ;AACvB,OAAO;AACP;AACA,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AAC1B,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;AAC7C,OAAO,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACxC,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC5E,OAAO,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AACrC,QAAQ,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC;AAC/D,OAAO,MAAM;AACb,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9C,OAAO;AACP,KAAK,CAAC;AACN;AACA,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC1B,MAAM,IAAI,CAAC,QAAQ,GAAG,WAAW;AACjC,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;AACvC,OAAO,CAAC;AACR,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW;AAC3B,MAAM,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AACzC,KAAK,CAAC;AACN;AACA,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA;AACA,EAAE,IAAI,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACpE;AACA,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;AACnC,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AACvC,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM;AAC3D,GAAG;AACH;AACA,EAAE,SAAS,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE;AACnC,IAAI,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC5B,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC5B;AACA,IAAI,IAAI,KAAK,YAAY,OAAO,EAAE;AAClC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC1B,QAAQ,MAAM,IAAI,SAAS,CAAC,cAAc,CAAC;AAC3C,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAC3B,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;AAC3C,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC5B,QAAQ,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAClD,OAAO;AACP,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AACjC,MAAM,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7B,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AACjC,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE;AAC5C,QAAQ,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;AAC/B,QAAQ,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,KAAK;AACL;AACA,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,aAAa,CAAC;AAChF,IAAI,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC1C,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AAClD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AAChD,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,KAAK,IAAI,EAAE;AACnE,MAAM,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,WAAW;AACvC,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ;AACA,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE;AACxB,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;AAC9B,IAAI,IAAI;AACR,OAAO,IAAI,EAAE;AACb,OAAO,KAAK,CAAC,GAAG,CAAC;AACjB,OAAO,OAAO,CAAC,SAAS,KAAK,EAAE;AAC/B,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACvD,UAAU,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3E,SAAS;AACT,OAAO,CAAC,CAAC;AACT,IAAI,OAAO,IAAI;AACf,GAAG;AACH;AACA,EAAE,SAAS,YAAY,CAAC,UAAU,EAAE;AACpC,IAAI,IAAI,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;AACA;AACA,IAAI,IAAI,mBAAmB,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AACtE,IAAI,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE;AAC9D,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACrC,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC3C,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACnC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,OAAO;AAClB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC/B;AACA,EAAE,SAAS,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE;AACvC,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;AACtE,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACtD,IAAI,IAAI,CAAC,UAAU,GAAG,YAAY,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1E,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAChC;AACA,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,WAAW;AACxC,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE;AACxC,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,OAAO,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AACxC,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;AACA,EAAE,QAAQ,CAAC,KAAK,GAAG,WAAW;AAC9B,IAAI,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;AACnE,IAAI,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC;AAC5B,IAAI,OAAO,QAAQ;AACnB,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACnD;AACA,EAAE,QAAQ,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,MAAM,EAAE;AAC5C,IAAI,IAAI,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AACjD,MAAM,MAAM,IAAI,UAAU,CAAC,qBAAqB,CAAC;AACjD,KAAK;AACL;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AACzE,GAAG,CAAC;AACJ;AACA,EAAE,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC3C,EAAE,IAAI;AACN,IAAI,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;AAC/B,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,YAAY,GAAG,SAAS,OAAO,EAAE,IAAI,EAAE;AACnD,MAAM,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC7B,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC/B,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACpE,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;AACtE,GAAG;AACH;AACA,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE;AAC9B,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;AACjD,MAAM,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7C;AACA,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE;AACpD,QAAQ,OAAO,MAAM,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AACxE,OAAO;AACP;AACA,MAAM,IAAI,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;AACrC;AACA,MAAM,SAAS,QAAQ,GAAG;AAC1B,QAAQ,GAAG,CAAC,KAAK,EAAE,CAAC;AACpB,OAAO;AACP;AACA,MAAM,GAAG,CAAC,MAAM,GAAG,WAAW;AAC9B,QAAQ,IAAI,OAAO,GAAG;AACtB,UAAU,MAAM,EAAE,GAAG,CAAC,MAAM;AAC5B,UAAU,UAAU,EAAE,GAAG,CAAC,UAAU;AACpC,UAAU,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC;AAClE,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,GAAG,aAAa,IAAI,GAAG,GAAG,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACpG,QAAQ,IAAI,IAAI,GAAG,UAAU,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC;AACvE,QAAQ,OAAO,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAC7C,OAAO,CAAC;AACR;AACA,MAAM,GAAG,CAAC,OAAO,GAAG,WAAW;AAC/B,QAAQ,MAAM,CAAC,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;AACxD,OAAO,CAAC;AACR;AACA,MAAM,GAAG,CAAC,SAAS,GAAG,WAAW;AACjC,QAAQ,MAAM,CAAC,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;AACxD,OAAO,CAAC;AACR;AACA,MAAM,GAAG,CAAC,OAAO,GAAG,WAAW;AAC/B,QAAQ,MAAM,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AAClE,OAAO,CAAC;AACR;AACA,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAClD;AACA,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AAC7C,QAAQ,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;AACnC,OAAO,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE;AACjD,QAAQ,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;AACpC,OAAO;AACP;AACA,MAAM,IAAI,cAAc,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE;AACjD,QAAQ,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC;AAClC,OAAO;AACP;AACA,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE;AACpD,QAAQ,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1C,OAAO,CAAC,CAAC;AACT;AACA,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1B,QAAQ,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC3D;AACA,QAAQ,GAAG,CAAC,kBAAkB,GAAG,WAAW;AAC5C;AACA,UAAU,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;AACpC,YAAY,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAClE,WAAW;AACX,SAAS,CAAC;AACV,OAAO;AACP;AACA,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AACpF,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxB;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC5B,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC5B,EAAE,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC9B,EAAE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACxB;AACA,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE;AACA,EAAE,OAAO,OAAO,CAAC;AACjB;AACA,EAAC,EAAE,EAAE,EAAE;AACP,CAAC,EAAE,QAAQ,CAAC,CAAC;AACb,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC/B;AACA,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC/B;AACA;AACA,IAAI,GAAG,GAAG,QAAQ,CAAC;AACnB,OAAO,GAAG,GAAG,CAAC,MAAK;AACnB,OAAkB,CAAA,OAAA,GAAA,GAAG,CAAC,MAAK;AAC3B,OAAgB,CAAA,KAAA,GAAA,GAAG,CAAC,MAAK;AACzB,OAAkB,CAAA,OAAA,GAAA,GAAG,CAAC,QAAO;AAC7B,OAAkB,CAAA,OAAA,GAAA,GAAG,CAAC,QAAO;AAC7B,OAAmB,CAAA,QAAA,GAAA,GAAG,CAAC,SAAQ;AAC/B,MAAiB,CAAA,OAAA,GAAA,QAAA;;;;;ACpiBjB;;;;AAIG;AACI,eAAe,aAAa,CACjC,UAAsB,EACtB,SAAoB,EACpB,KAAA,GAAgB,CAAC,EAAA;IAEjB,MAAM,GAAG,GAAG,CAAA,mCAAA,EAAsC,SAAS,CAAC,QAAQ,EAAE,CAAA,cAAA,EAAiB,KAAK,CAAA,CAAE,CAAC;IAC/F,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACxD,QAAA,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC;QAChC,KAAK,CAAC,GAAG,CAAC;AACX,KAAA,CAAC,CAAC;;AAGH,IAAA,MAAM,YAAY,GAAG,CAAC,MAAM,gBAAgB,CAAC,IAAI,EAAE,EAAE,MAAM,CACzD,CAAC,CAAQ,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,CAAC,QAAQ,KAAK,UAAU,CAC7E,CAAC;AACF,IAAA,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7B,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;AAGD,IAAA,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;IAG9B,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,aAAa,EAAE;AACvD,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;AAGD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;AAGG;AACI,eAAe,SAAS,CAC7B,UAAsB,EACtB,SAAoB,EAAA;IAEpB,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC/D,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,QAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC9C,KAAA;IACD,MAAM,EAAE,OAAO,EAAE,GAAG,4BAA4B,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnE,MAAM,sBAAsB,GAAG,MAAM,UAAU,CAAC,cAAc,CAC5D,OAAO,CAAC,kBAAkB,CAC3B,CAAC;IACF,IAAI,sBAAsB,KAAK,IAAI,EAAE;AACnC,QAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACnD,KAAA;IACD,MAAM,EAAE,WAAW,EAAE,GAAG,4BAA4B,CAClD,sBAAsB,CAAC,IAAI,CAC5B,CAAC;AACF,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,+BAA+B,GAAG,KAAK,CAAC,QAAQ,CACpD;AACE,IAAA,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC;AACjC,IAAA,KAAK,CAAC,MAAM,CACV,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,kBAAkB,CAAC,CAAC,EACrD,QAAQ,CACT;AACD,IAAA,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,EAAE,SAAS,CAAC;IAChE,KAAK,CAAC,MAAM,CACV;AACE,QAAA,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;QACjB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,yBAAyB,CAAC;AAC3D,KAAA,EACD,aAAa,CACd;AACF,CAAA,EACD,SAAS,EACT,KAAK,CAAC,GAAG,EAAE,CACZ,CAAC;AAEI,SAAU,4BAA4B,CAAC,IAAY,EAAA;AACvD,IAAA,OAAO,+BAA+B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtD;;;;;;;;;;;;;;;;;;;;ACxCM,SAAU,aAAa,CAC3B,WAA2B,EAAA;IAE3B,OAAO,UAAU,IAAI,WAAW,CAAC;AACnC,CAAC;AAyHD;AACO,eAAe,UAAU,CAAC,SAAoB,EAAA;AACnD,IAAA,MAAM,IAAI,GAAG,CAAC,MAAM,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,IAAA,OAAO,MAAM,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,SAAS,CAAC,CAAC;AACjE,CAAC;AAED;SACgB,IAAI,GAAA;AAClB,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAQD,MAAM,kBAAkB,GAAoC,KAAK,CAAC,MAAM,CAAC;AACvE,IAAA,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;AAC5B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACpB,CAAA,CAAC,CAAC;AAEG,SAAU,gBAAgB,CAAC,IAAY,EAAA;AAC3C,IAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzC;;AC5HgB,SAAA,eAAe,CAC7B,KAAqB,EACrB,IAAW,EAAA;;IAEX,IAAI,OAAO,GAAG,EAAE,CAAC;AAEjB,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD,IAAA,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE;AAC1B,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC,EAAE;AAChC,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,4BAAA,EAA+B,IAAI,CACjC,gBAAA,EAAA,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,IACT,CAAA,YAAA,EAAe,MAAA,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,0CAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAA,CAAE,CACtD,CAAC;AACH,SAAA;AACD,QAAA,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzB;;AC/Dc,MAAO,2BAA2B,CAAA;AACvC,IAAA,OAAO,KAAK,CACjB,KAAQ,EACR,QAAgC,EAChC,SAAoB,EAAA;AAEpB,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC3B,YAAA,MAAM,IAAI,QAAQ,CAAC,6BAA6B,CAAC,CAAC;AACnD,SAAA;AAED,QAAA,MAAM,EAAE,GAAG,CACT,GAAG,IAAsC,KACf;AAC1B,YAAA,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACxD,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/C,YAAA,mBAAmB,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;YAEpC,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAEvC,YAAA,IAAI,GAAG,CAAC,iBAAiB,KAAK,SAAS,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACrC,aAAA;AAED,YAAA,IAAIf,KAAc,CAAC,YAAY,CAAC,EAAE;AAChC,gBAAA,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;AAC9C,aAAA;YAED,OAAO,IAAI,sBAAsB,CAAC;gBAChC,IAAI;gBACJ,SAAS;AACT,gBAAA,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC;AAC5D,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;;AAGF,QAAA,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAiD,KAAI;AACrE,YAAA,OAAO,2BAA2B,CAAC,aAAa,CAC9C,IAAI,EACJ,KAAK,CAAC,QAAQ,EACd,SAAS,EACT,KAAK,CAAC,IAAI,CACX,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,OAAO,EAAE,CAAC;KACX;IAEM,OAAO,aAAa,CACzB,GAAyB,EACzB,QAAmC,EACnC,SAAoB,EACpB,MAAe,EAAA;QAEf,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;AAED,QAAA,OAAO,QAAQ;AACZ,aAAA,GAAG,CAAC,CAAC,GAAmB,KAAI;;AAE3B,YAAA,MAAM,cAAc,GAClB,UAAU,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC/C,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAa,CAAC;AAC1C,gBAAA,OAAO,2BAA2B,CAAC,aAAa,CAC9C,OAAO,EACN,GAAmB,CAAC,QAAQ,EAC7B,SAAS,EACT,MAAM,CACP,CAAC,IAAI,EAAE,CAAC;AACV,aAAA;AAAM,iBAAA;gBACL,MAAM,OAAO,GAAe,GAAiB,CAAC;AAC9C,gBAAA,IAAI,MAAiB,CAAC;gBACtB,IAAI;oBACF,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAY,CAAC,CAAC;AACrD,iBAAA;AAAC,gBAAA,OAAO,GAAG,EAAE;oBACZ,MAAM,IAAI,KAAK,CACb,CACE,8BAAA,EAAA,GAAG,CAAC,IACN,CAAA,oCAAA,EACE,MAAM,KAAK,SAAS,GAAG,oBAAoB,GAAG,MAAM,GAAG,GAAG,GAAG,EAC/D,CAAiC,+BAAA,CAAA,CAClC,CAAC;AACH,iBAAA;AAED,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChE,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC;gBAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBACL,MAAM;oBACN,UAAU;oBACV,QAAQ;iBACT,CAAC;AACH,aAAA;AACH,SAAC,CAAC;AACD,aAAA,IAAI,EAAE,CAAC;KACX;AACF,CAAA;AAsED;AACA,SAAS,mBAAmB,CAAC,EAAkB,EAAE,GAAG,IAAW,EAAA;;AAE/D;;AC7Lc,MAAO,kBAAkB,CAAA;AAC9B,IAAA,OAAO,KAAK,CACjB,KAAQ,EACR,IAA2B,EAAA;AAE3B,QAAA,MAAM,IAAI,GAA0B,CAAC,GAAG,IAAI,KAAiB;;AAC3D,YAAA,MAAM,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClD,YAAA,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7B,YAAA,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,YAAY,EAAE;AAC3C,gBAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACpE,aAAA;AACD,YAAA,CAAA,EAAA,GAAA,GAAG,CAAC,eAAe,0CAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,YAAA,CAAA,EAAA,GAAA,GAAG,CAAC,YAAY,0CAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACtB,YAAA,CAAA,EAAA,GAAA,GAAG,CAAC,gBAAgB,0CAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,YAAA,OAAO,EAAE,CAAC;AACZ,SAAC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;AClBa,MAAO,UAAU,CAAA;IACtB,OAAO,KAAK,CACjB,KAAQ,EACR,IAA2B,EAC3B,SAA8B,EAC9B,QAAkB,EAAA;AAElB,QAAA,MAAM,GAAG,GAAkB,OAAO,GAAG,IAAI,KAAI;;AAC3C,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACzB,YAAA,MAAM,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAClD,YAAA,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE;AACzC,gBAAA,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;AACH,aAAA;YACD,IAAI;AACF,gBAAA,OAAO,MAAM,QAAQ,CAAC,cAAc,CAClC,EAAE,EACF,CAAA,EAAA,GAAA,GAAG,CAAC,OAAO,mCAAI,EAAE,EACjB,GAAG,CAAC,OAAO,CACZ,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAG,EAAE;AACZ,gBAAA,MAAM,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACtC,aAAA;AACH,SAAC,CAAC;AAEF,QAAA,OAAO,GAAG,CAAC;KACZ;AACF;;ACnBa,MAAO,cAAc,CAAA;IAC1B,OAAO,KAAK,CACjB,GAAQ,EACR,KAAY,EACZ,SAAoB,EACpB,QAAmB,EAAA;;QAEnB,MAAM,UAAU,GAAG,EAA2B,CAAC;QAE/C,CAAA,EAAA,GAAA,GAAG,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,CAAC,UAAU,KAAI;YACnC,MAAM,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,YAAA,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,aAAa,CAClC,GAAG,EACH,UAAU,EACV,SAAS,EACT,QAAQ,EACR,KAAK,CACN,CAAC;AACJ,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACnB;AACF,CAAA;MA8BY,aAAa,CAAA;AAMxB;;AAEG;AACH,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAGD;;AAEG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAGD;;AAEG;AACH,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAGD;;AAEG;AACH,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;AAGD;;AAEG;AACH,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IAGD,WACE,CAAA,GAAQ,EACR,UAAa,EACb,SAAoB,EACpB,QAAmB,EACnB,KAAa,EAAA;AAEb,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAR,QAAQ,GAAI,WAAW,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,aAAL,KAAK,KAAA,KAAA,CAAA,GAAL,KAAK,GAAI,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACpD;AAED;;;;AAIG;AACH,IAAA,MAAM,aAAa,CACjB,OAAgB,EAChB,UAAuB,EAAA;AAEvB,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACzE,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,MAAM,uBAAuB,CAC3B,OAAgB,EAChB,UAAuB,EAAA;QAEvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACrD,OAAO,EACP,UAAU,CACX,CAAC;AACF,QAAA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;QACvC,OAAO;YACL,IAAI,EACF,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAC9B,kBAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;AACnE,kBAAE,IAAI;YACV,OAAO;SACR,CAAC;KACH;AAED;;;;AAIG;AACH,IAAA,MAAM,KAAK,CAAC,OAAgB,EAAE,UAAuB,EAAA;AACnD,QAAA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CACb,CAAyC,sCAAA,EAAA,OAAO,CAAC,QAAQ,EAAE,CAAE,CAAA,CAC9D,CAAC;AACH,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;AACH,IAAA,MAAM,eAAe,CACnB,OAAgB,EAChB,UAAuB,EAAA;AAEvB,QAAA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAC1D,OAAO,EACP,UAAU,CACX,CAAC;QACF,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,CAA0B,uBAAA,EAAA,OAAO,CAAC,QAAQ,EAAE,CAAE,CAAA,CAAC,CAAC;AACjE,SAAA;AACD,QAAA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;KAC1B;AAED;;;;;AAKG;AACH,IAAA,MAAM,aAAa,CACjB,SAAoB,EACpB,UAAuB,EAAA;QAEvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;KACnE;AAED;;;;;AAKG;AACH,IAAA,MAAM,uBAAuB,CAC3B,SAAoB,EACpB,UAAuB,EAAA;AAEvB,QAAA,MAAM,QAAQ,GAAG,MAAMgB,6BAAqC,CAC1D,IAAI,CAAC,SAAS,CAAC,UAAU,EACzB,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,gBAAgB,CAAC,OAAO,CAAC,CAAC,EACrD,UAAU,CACX,CAAC;;AAGF,QAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;YAC7B,IAAI,MAAM,IAAI,IAAI,EAAE;AAClB,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACD,YAAA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;YACpC,OAAO;AACL,gBAAA,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;gBACtE,OAAO;aACR,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;;;;;;;;AAaG;IACH,MAAM,GAAG,CACP,OAA6C,EAAA;AAE7C,QAAA,MAAM,MAAM,GACV,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CACxB,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,OAAO,YAAY,MAAM,GAAG,OAAO,GAAG,SAAS,CAChD,CAAC;QACJ,MAAM,YAAY,GAA+B,EAAE,CAAC;QACpD,IAAI,CAAA,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,MAAM,KAAI,SAAS,IAAI,CAAA,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,KAAK,KAAI,SAAS,EAAE;YAC7D,YAAY,CAAC,IAAI,CAAC;AAChB,gBAAA,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;AACvD,aAAA,CAAC,CAAC;AACJ,SAAA;QACD,IAAI,CAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,QAAQ,KAAI,SAAS,EAAE;YACjC,YAAY,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClD,SAAA;AACD,QAAA,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAC3D,IAAI,CAAC,UAAU,EACf;AACE,YAAA,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU;YAChD,OAAO,EAAE,CAAC,GAAG,YAAY,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;AACvE,SAAA,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAI;YACtC,OAAO;AACL,gBAAA,SAAS,EAAE,MAAM;AACjB,gBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAClC,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,OAAO,CAAC,IAAI,CACb;aACF,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;IACH,SAAS,CAAC,OAAgB,EAAE,UAAuB,EAAA;QACjD,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClD,QAAA,IAAI,GAAG,EAAE;YACP,OAAO,GAAG,CAAC,EAAE,CAAC;AACf,SAAA;AAED,QAAA,MAAM,EAAE,GAAG,IAAI,YAAY,EAAE,CAAC;AAC9B,QAAA,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACpC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CACxD,OAAO,EACP,CAAC,GAAG,KAAI;YACN,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CACzC,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,GAAG,CAAC,IAAI,CACT,CAAC;AACF,YAAA,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC5B,EACD,UAAU,CACX,CAAC;AAEF,QAAA,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE;YACpC,EAAE;YACF,QAAQ;AACT,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;AAEG;IACH,MAAM,WAAW,CAAC,OAAgB,EAAA;QAChC,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO;AACR,SAAA;AACD,QAAA,IAAI,aAAa,EAAE;AACjB,YAAA,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU;AAC5B,iBAAA,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC;iBACzC,IAAI,CAAC,MAAK;gBACT,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC3C,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzB,SAAA;KACF;AAED;;AAEG;AACH,IAAA,MAAM,iBAAiB,CACrB,MAAc,EACd,YAAqB,EAAA;AAErB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;AAC1C,YAAA,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;AACH,SAAA;QAED,OAAO,aAAa,CAAC,aAAa,CAAC;AACjC,YAAA,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACpC,gBAAgB,EAAE,MAAM,CAAC,SAAS;AAClC,YAAA,KAAK,EAAE,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAZ,KAAA,CAAA,GAAA,YAAY,GAAI,IAAI;AAC3B,YAAA,QAAQ,EACN,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,iCAAiC,CAC/D,YAAY,KAAZ,IAAA,IAAA,YAAY,cAAZ,YAAY,GAAI,IAAI,CACrB;YACH,SAAS,EAAE,IAAI,CAAC,UAAU;AAC3B,SAAA,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACH,IAAA,MAAM,UAAU,CAAC,GAAG,IAA+B,EAAA;QACjD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,CAAC;AACnD,QAAA,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC/B;AAED;;;;;AAKG;AACH,IAAA,MAAM,iBAAiB,CACrB,GAAG,IAA+B,EAAA;AAElC,QAAA,OAAO,MAAMC,UAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;KAC9D;AAED,IAAA,MAAM,cAAc,CAClB,OAAgB,EAChB,UAAuB,EAAA;AAEvB,QAAA,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CACnD,gBAAgB,CAAC,OAAO,CAAC,EACzB,UAAU,CACX,CAAC;KACH;AAED,IAAA,MAAM,wBAAwB,CAC5B,OAAgB,EAChB,UAAuB,EAAA;AAEvB,QAAA,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,wBAAwB,CAC7D,gBAAgB,CAAC,OAAO,CAAC,EACzB,UAAU,CACX,CAAC;KACH;AACF,CAAA;AAYD;AACA,MAAM,aAAa,GAA8B,IAAI,GAAG,EAAE;;ACva1D,MAAM,WAAW,GAAG,eAAe,CAAC;AACpC,MAAM,YAAY,GAAG,gBAAgB,CAAC;AACtC,MAAM,uBAAuB,GAAG,WAAW,CAAC,MAAM,CAAC;AACnD,MAAM,wBAAwB,GAAG,YAAY,CAAC,MAAM,CAAC;MAiBxC,YAAY,CAAA;AAoCvB,IAAA,WAAA,CAAY,SAAoB,EAAE,QAAkB,EAAE,KAAY,EAAA;AAChE,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;KAC3B;IAEM,gBAAgB,CACrB,SAAiB,EACjB,QAA+D,EAAA;;AAE/D,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACrC,QAAA,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;;QAG3B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACzC,SAAA;QACD,IAAI,CAAC,eAAe,CAAC,GAAG,CACtB,SAAS,EACT,CAAC,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,CAC7D,CAAC;;AAGF,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;;AAG1D,QAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;AAC5C,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAU,CAAC,UAAU,CAAC,MAAM,CAC5D,IAAI,CAAC,UAAU,EACf,CAAC,IAAI,EAAE,GAAG,KAAI;YACZ,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,OAAO;AACR,aAAA;AAED,YAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC1D,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAE1D,gBAAA,IAAI,YAAY,EAAE;AAChB,oBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;wBAChC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAEtD,wBAAA,IAAI,UAAU,EAAE;AACd,4BAAA,MAAM,GAAG,QAAQ,CAAC,GAAG,UAAU,CAAC;AAChC,4BAAA,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAChD,yBAAA;AACH,qBAAC,CAAC,CAAC;AACJ,iBAAA;AACF,aAAA;AACH,SAAC,CACF,CAAC;AAEF,QAAA,OAAO,QAAQ,CAAC;KACjB;IAEM,MAAM,mBAAmB,CAAC,QAAgB,EAAA;;QAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,CAAA,eAAA,CAAiB,CAAC,CAAC;AAC9D,SAAA;AACD,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;;QAG7B,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,CAAA,CAAA,CAAG,CAAC,CAAC;AAClE,SAAA;;AAGD,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACtC,QAAA,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACxC,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE;AACnC,YAAA,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE;gBACnC,MAAM,IAAI,KAAK,CACb,CAAiD,8CAAA,EAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAE,CAAA,CAC7E,CAAC;AACH,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;AAC5C,gBAAA,MAAM,IAAI,CAAC,SAAU,CAAC,UAAU,CAAC,oBAAoB,CACnD,IAAI,CAAC,qBAAqB,CAC3B,CAAC;AACF,gBAAA,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;AACxC,aAAA;AACF,SAAA;KACF;AACF,CAAA;MAEY,WAAW,CAAA;IAItB,WAAY,CAAA,SAAoB,EAAE,KAAY,EAAA;AAC5C,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;;;;;;;;;;;;AAaM,IAAA,CAAC,SAAS,CAAC,IAAc,EAAE,uBAAgC,KAAK,EAAA;AACrE,QAAA,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,QAAA,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,CAAC;AACzC,QAAA,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QAC5B,OAAO,GAAG,KAAK,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAC9C,SAAS,EACT,GAAG,EACH,oBAAoB,CACrB,CAAC;AACF,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACD,YAAA,IAAI,UAAU,EAAE;AACd,gBAAA,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5B,aAAA;AACD,YAAA,IAAI,MAAM,EAAE;gBACV,SAAS,CAAC,GAAG,EAAE,CAAC;AACjB,aAAA;AACD,YAAA,GAAG,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;AACzB,SAAA;KACF;;;;;AAMO,IAAA,SAAS,CACf,SAA2B,EAC3B,GAAW,EACX,oBAA6B,EAAA;;AAG7B,QAAA,IACE,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YAC1B,SAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EACjD;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;AACzD,SAAA;;AAEI,aAAA;YACH,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,SAAA;KACF;;IAGO,gBAAgB,CACtB,GAAW,EACX,oBAA6B,EAAA;;AAG7B,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;AAC/D,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC;AACxC,kBAAE,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC;AACpC,kBAAE,GAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;AACxC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAE/C,YAAA,IAAI,oBAAoB,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1C,gBAAA,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAA,CAAE,CAAC,CAAC;AACrD,aAAA;AACD,YAAA,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7B,SAAA;;AAEI,aAAA;YACH,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,SAAA;KACF;;AAGO,IAAA,eAAe,CAAC,GAAW,EAAA;;QAEjC,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;QAGnC,IAAI,QAAQ,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;AACrD,YAAA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;AAErB,SAAA;AAAM,aAAA,IACL,QAAQ,CAAC,UAAU,CAAC,CAAW,QAAA,EAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA,OAAA,CAAS,CAAC,EAClE;YACA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;AAC3C,SAAA;;AAEI,aAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACpC,YAAA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACvB,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACtB,SAAA;KACF;AACF,CAAA;AAED;AACA;AACA,MAAM,gBAAgB,CAAA;AAAtB,IAAA,WAAA,GAAA;QACE,IAAK,CAAA,KAAA,GAAa,EAAE,CAAC;KAmBtB;IAjBC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACxD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC1C;AAED,IAAA,IAAI,CAAC,UAAkB,EAAA;AACrB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC7B;IAED,GAAG,GAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACxD,SAAA;AACD,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;KAClB;AACF,CAAA;AAED,MAAM,UAAU,CAAA;AACd,IAAA,WAAA,CAAmB,IAAc,EAAA;QAAd,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAU;KAAI;IAErC,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAA,OAAO,CAAC,CAAC;KACV;AACF;;ACpSa,MAAO,eAAe,CAAA;AAC3B,IAAA,OAAO,KAAK,CACjB,KAA2B,EAC3B,IAAwB,EACxB,SAA8B,EAC9B,QAAkB,EAClB,KAAY,EACZ,SAAoB,EACpB,GAAQ,EAAA;AAER,QAAA,MAAM,QAAQ,GAAoB,OAAO,GAAG,IAAI,KAAI;;AAClD,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACzB,YAAA,MAAM,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAClD,IAAI,IAAI,GAA+C,SAAS,CAAC;AACjE,YAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;AACnC,gBAAA,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;AACH,aAAA;YACD,IAAI;AACF,gBAAA,IAAI,GAAG,MAAM,QAAS,CAAC,QAAQ,CAC7B,EAAE,EACF,GAAG,CAAC,OAAO,EACX,MAAA,GAAG,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,CACxB,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAG,EAAE;AACZ,gBAAA,MAAM,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACtC,aAAA;YACD,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,gBAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACnD,aAAA;AACD,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,IAAI,EAAE;AACT,gBAAA,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAC7C,aAAA;YAED,MAAM,MAAM,GAAqC,EAAE,CAAC;YACpD,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC/C,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AAC1C,oBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpB,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAC/B,SAAC,CAAC;AAEF,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF;;AC3DD,SAAS,MAAM,CAAC,QAAiB,EAAA;AAC/B,IAAA,OAAO,IAAI,aAAa,CACtBT,IAAiB,CAAC,CAAC,CAAC,EACpB,CAAC,CAAS,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAChC,CAAC,CAAM,KAAK,CAAC,CAAC,QAAQ,EAAE,EACxB,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,QAAiB,EAAA;AAClC,IAAA,OAAO,IAAI,aAAa,CACtBA,IAAiB,CAAC,EAAE,CAAC,EACrB,CAAC,CAAS,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC,EAC/B,CAAC,GAAc,KAAK,GAAG,CAAC,QAAQ,EAAE,EAClC,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAI,MAAiB,EAAE,QAAiB,EAAA;AACtD,IAAA,OAAO,IAAI,aAAa,CAAI,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChD,CAAC;AAED,MAAM,aAAoB,SAAQU,QAAS,CAAA;AAKzC,IAAA,WAAA,CACE,MAAiB,EACjB,OAAuB,EACvB,OAAsB,EACtB,QAAiB,EAAA;AAEjB,QAAA,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACxB;IAED,MAAM,CAAC,CAAS,EAAE,MAAe,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KACpD;AAED,IAAA,MAAM,CAAC,GAAM,EAAE,CAAS,EAAE,MAAe,EAAA;AACvC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;KACzD;IAED,OAAO,CAAC,CAAS,EAAE,MAAe,EAAA;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;KACvC;AACF,CAAA;AAED,MAAM,aAAiB,SAAQA,QAAgB,CAAA;IAI7C,WAAY,CAAA,MAAiB,EAAE,QAAiB,EAAA;AAC9C,QAAA,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,GAAGX,GAAgB,EAAE,CAAC;KACzC;AAED,IAAA,MAAM,CAAC,GAAa,EAAE,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AACzC,QAAA,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACnE,SAAA;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AACxC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KACnD;AAED,IAAA,MAAM,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AAC1B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC3D,IAAI,aAAa,KAAK,CAAC,EAAE;AACvB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;aAAM,IAAI,aAAa,KAAK,CAAC,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC1C,SAAA;QACD,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC5D;AAED,IAAA,OAAO,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC,EAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KAC/C;AACF,CAAA;AAED,MAAM,GAAI,SAAQ,EAAE,CAAA;AAClB;;AAEG;IACH,QAAQ,GAAA;QACN,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;AACD,QAAA,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;AACjB,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AAClC,SAAA;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChB,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;AAEG;IACH,OAAO,UAAU,CAAC,MAAc,EAAA;AAC9B,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,CAAA,uBAAA,EAA0B,MAAM,CAAC,MAAM,CAAE,CAAA,CAAC,CAAC;AAC5D,SAAA;AACD,QAAA,OAAO,IAAI,GAAG,CACZ,CAAC,GAAG,MAAM,CAAC;AACR,aAAA,OAAO,EAAE;aACT,GAAG,CAAC,CAAC,CAAC,KAAK,CAAA,EAAA,EAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,aAAA,IAAI,CAAC,EAAE,CAAC,EACX,EAAE,CACH,CAAC;KACH;AACF,CAAA;AAED,MAAM,oBAAoB,GAAGD,MAAmB,CAAC;IAC/C,SAAS,CAAC,MAAM,CAAC;IACjB,SAAS,CAAC,OAAO,CAAC;IAClB,MAAM,CAAC,QAAQ,CAAC;AAChB,IAAA,OAAO,CAAC,SAAS,EAAE,EAAE,UAAU,CAAC;IAChC,CAAC,CAAC,CAAS,KAAI;AACb,QAAA,MAAM,CAAC,GAAGK,KAAkB,CAACQ,EAAe,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxE,QAAA,CAAC,CAAC,UAAU,CAAC,CAAC,EAAEb,MAAmB,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;AAC1D,QAAA,CAAC,CAAC,UAAU,CAAC,CAAC,EAAEA,MAAmB,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;AACxD,QAAA,CAAC,CAAC,UAAU,CAAC,CAAC,EAAEA,MAAmB,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AACnD,QAAA,OAAO,CAAC,CAAC;KACV,EAAE,OAAO,CAAC;AACX,IAAA,OAAO,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC;IAC7B,MAAM,CAAC,iBAAiB,CAAC;AACzB,IAAA,OAAO,CAAC,SAAS,EAAE,EAAE,gBAAgB,CAAC;AACvC,CAAA,CAAC,CAAC;AAEG,SAAU,kBAAkB,CAAC,CAAS,EAAA;AAC1C,IAAA,OAAO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC;;AC/FA;MACa,gBAAgB,CAAA;AAY3B,IAAA,WAAA,CACE,KAAiB,EACT,SAA0B,EAC1B,SAAmB,EACnB,UAAqB,EACrB,MAA4B,EACpC,iBAAwC,EAChC,SAAuB,EACvB,eAA4C,EAAA;QAN5C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;QAC1B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAsB;QAE5B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;QACvB,IAAe,CAAA,eAAA,GAAf,eAAe,CAA6B;AAEpD,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CACnC,SAAS,EACT,iBAAiB,EACjB,IAAI,CAAC,UAAU,CAChB,CAAC;KACH;AAEM,IAAA,IAAI,CAAC,KAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;;;;AAKM,IAAA,MAAM,OAAO,GAAA;QAClB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;AAG5C,QAAA,OACE,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;aAC1C,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnD,aAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC9B,YAAA,CAAC,EACD,GAAE;KACL;AAEO,IAAA,MAAM,aAAa,GAAA;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC;gBACxD,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,KAAK,EAAE,IAAI,CAAC,MAAM;AACnB,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;AACD,QAAA,OAAO,CAAC,CAAC;KACV;IAEO,sBAAsB,CAC5B,eAAgC,EAChC,YAA8B,EAAA;QAE9B,MAAM,qBAAqB,GAAoB,EAAE,CAAC;;;AAGlD,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AACtC,YAAA,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;AACrC,YAAA,MAAM,cAAc,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;;YAEpD,IAAI,cAAc,KAAK,SAAS;gBAAE,SAAS;AAC3C,YAAA,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE;;AAErC,gBAAA,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE;AAC9B,oBAAA,qBAAqB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAC9D,cAAc,EACd,WAAW,CAAC,UAAU,CAAqB,CAC5C,CAAC;AACH,iBAAA;AAAM,qBAAA;;oBAEL,qBAAqB,CAAC,WAAW,CAAC,GAAG,sBAAsB,CACzD,cAAc,EACd,IAAI,CACL,CAAC;AACH,iBAAA;AACF,aAAA;AAAM,iBAAA;;gBAEL,IAAI,cAAc,KAAK,IAAI,EAAE;oBAC3B,qBAAqB,CAAC,WAAW,CAAC,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACvE,iBAAA;AAAM,qBAAA,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE;AACpC,oBAAA,qBAAqB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACtD,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,qBAAqB,CAAC;KAC9B;AAEM,IAAA,gBAAgB,CAAC,QAAyB,EAAA;QAC/C,MAAM,CAAC,MAAM,CACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAC5D,CAAC;KACH;AAEO,IAAA,GAAG,CAAC,IAAc,EAAA;;QAExB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CACrB,CAAC,GAAG,EAAE,OAAO,KAAK,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,EACrC,IAAI,CAAC,SAAS,CACf,CAAC;AAEF,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE;AACvB,YAAA,OAAO,GAAgB,CAAC;AACzB,SAAA;KACF;IAEO,GAAG,CAAC,IAAc,EAAE,KAAgB,EAAA;AAC1C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,KAAI;YACtB,MAAM,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,YAAA,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACjB,aAAA;YAED,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACxB,YAAA,IAAI,GAAG,IAAI,CAAC,CAAC,CAAoB,CAAC;AACpC,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,MAAM,YAAY,CACxB,QAA0B,EAC1B,OAAiB,EAAE,EAAA;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAA,MAAM,qBAAqB,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1C,YAAA,MAAM,WAAW,GAAI,qBAAqC,CAAC,QAAQ,CAAC;AACpE,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;AACnC,oBAAA,GAAG,IAAI;AACP,oBAAA,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC;AACtC,iBAAA,CAAC,CAAC;AACJ,aAAA;YAED,MAAM,WAAW,GAAG,qBAAmC,CAAC;YACxD,MAAM,eAAe,GAAG,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;;AAG9D,YAAA,IAAI,WAAW,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE;;AAEjE,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE;AACvC,oBAAA,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;AACH,iBAAA;;AAED,gBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACvE,aAAA;;YAGD,IACE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,eAAe,CAAC;gBAC7D,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,CAAC,EACrC;AACA,gBAAA,IAAI,CAAC,GAAG,CACN,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,EAC1B,gBAAgB,CAAC,cAAc,CAAC,eAAe,CAAC,CACjD,CAAC;AACH,aAAA;AACF,SAAA;KACF;AAED;;;;;AAKG;AACK,IAAA,gBAAgB,CACtB,QAA0B,EAC1B,IAAA,GAAiB,EAAE,EAAA;AAEnB,QAAA,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;AACxB,YAAA,MAAM,qBAAqB,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1C,YAAA,MAAM,WAAW,GAAI,qBAAqC,CAAC,QAAQ,CAAC;AACpE,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AACjC,oBAAA,GAAG,IAAI;AACP,oBAAA,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC;AACtC,iBAAA,CAAC,CAAC;AACJ,aAAA;;AAGD,YAAA,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,YAAA,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM;gBAAE,OAAO;YAE1C,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC;;AAGrD,YAAA,IAAI,WAAW,KAAK,gBAAgB,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC9D,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC,CAAC;AAErC,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;oBAC1B,IAAI,CAAC,GAAG,CACN,WAAW,EACX,SAAS,CAAC,sBAAsB,CAC9B,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAClC,IAAI,CAAC,UAAU,CAChB,CAAC,CAAC,CAAC,CACL,CAAC;AACH,iBAAA;AACD,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACrC,iBAAA;gBAED,OAAO;AACR,aAAA;AACF,SAAA;KACF;AAEO,IAAA,MAAM,WAAW,CACvB,QAA0B,EAC1B,OAAiB,EAAE,EAAA;QAEnB,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChC,YAAA,MAAM,WAAW,GAAI,WAA2B,CAAC,QAAQ,CAAC;AAC1D,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,KAAK,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;AAC3C,oBAAA,GAAG,IAAI;AACP,oBAAA,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC;AAC5B,iBAAA,CAAC,CAAC;AACJ,aAAA;YAED,MAAM,iBAAiB,GAAe,WAAyB,CAAC;YAChE,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;YAGpD,IACE,iBAAiB,CAAC,GAAG;AACrB,gBAAA,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;gBACtC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,CAAC,EACrC;AACA,gBAAA,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EAAE;oBAChE,KAAK,IAAI,CAAC,CAAC;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,MAAM,gBAAgB,CAC5B,QAA0B,EAC1B,OAAiB,EAAE,EAAA;QAEnB,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChC,YAAA,MAAM,WAAW,GAAI,WAA2B,CAAC,QAAQ,CAAC;AAC1D,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,KAAK,IAAI,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AAChD,oBAAA,GAAG,IAAI;AACP,oBAAA,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC;AAC5B,iBAAA,CAAC,CAAC;AACJ,aAAA;AACD,YAAA,MAAM,SAAS,GAAI,WAA0B,CAAC,SAAS,IAAI,EAAE,CAAC;YAC9D,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,eAAe,CAAC,CAAC;;YAG3C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACrC,YAAA,IAAI,UAAU,EAAE;gBACd,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAC/B,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAC9C,CAAC;AAEF,gBAAA,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;AACzB,gBAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;AACpD,wBAAA,SAAS,EAAE,UAAU;AACtB,qBAAA,CAAC,CAAC;AACH,oBAAA,MAAM,OAAO,CAAC,GAAG,CACf,QAAQ,CAAC,GAAG,CAAC,OAAO,GAAG,KAAI;AACzB,wBAAA,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AAE/B,wBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/C,wBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;qBACzB,CAAC,CACH,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,MAAM,eAAe,CAAC,WAAuB,EAAE,OAAiB,EAAE,EAAA;QACxE,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK;AAC5C,YAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAErC,QAAA,MAAM,KAAK,GAA2B,MAAM,OAAO,CAAC,GAAG,CACrD,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAiB,KAC1C,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAC9B,CACF,CAAC;AAEF,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,WAAW,CAAC,EAAE;YACpD,OAAO;AACR,SAAA;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;AACR,SAAA;AACD,QAAA,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,SAAS,CAAC,kBAAkB,CACjD,KAAiB,EACjB,SAAS,CACV,CAAC;AAEF,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;KAC1D;AAEO,IAAA,MAAM,cAAc,CAC1B,WAAuB,EACvB,OAAiB,EAAE,EAAA;;QAEnB,IAAI,EAAC,CAAA,EAAA,GAAA,WAAW,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAA,EAAE;YAC/B,OAAO,IAAI,CAAC,UAAU,CAAC;AACxB,SAAA;AACD,QAAA,QAAQ,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI;AACpC,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,IAAI,SAAS,CAClB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CACpD,CAAC;AACJ,YAAA,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClD,YAAA,KAAK,SAAS;AACZ,gBAAA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAClE,YAAA;AACE,gBAAA,MAAM,IAAI,KAAK,CACb,CAAA,8BAAA,EAAiC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAA,CAAE,CAClE,CAAC;AACL,SAAA;KACF;AAEO,IAAA,MAAM,QAAQ,CACpB,QAAiB,EACjB,OAAiB,EAAE,EAAA;QAEnB,QAAQ,QAAQ,CAAC,IAAI;AACnB,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACtC,YAAA,KAAK,KAAK;AACR,gBAAA,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC1C,YAAA,KAAK,SAAS;gBACZ,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACpD,YAAA;gBACE,MAAM,IAAI,KAAK,CAAC,CAAA,sBAAA,EAAyB,QAAQ,CAAC,IAAI,CAAE,CAAA,CAAC,CAAC;AAC7D,SAAA;KACF;AAED;;AAEG;AACK,IAAA,OAAO,CAAC,IAAa,EAAE,IAAA,GAAiB,EAAE,EAAA;QAChD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAK,IAAY,CAAC,OAAO,EAAE;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CACjC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAM,IAAY,CAAC,OAAO,CACxC,CAAC;YACF,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,CAAA,iBAAA,EAAqB,IAAY,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AAC9D,aAAA;AAED,YAAA,MAAM,UAAU,GAAG,OAAO,CAAC,IAA0B,CAAC;YACtD,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAExE,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,SAAA;AAED,QAAA,OAAO,IAAc,CAAC;KACvB;AAEO,IAAA,aAAa,CAAC,QAAiB,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtE,QAAQ,CAAC,KAAK,CACf,CAAC;KACH;IAEO,MAAM,WAAW,CAAC,QAAiB,EAAA;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACzC,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,OAAO;AACR,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,aAAa,CACvB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtE,QAAQ,CACT,CAAC;KACH;AAEO,IAAA,QAAQ,CAAC,QAAiB,EAAA;QAChC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAExC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAC/C,CAAC,OAAY,KAAK,OAAO,CAAC,IAAI,KAAK,WAAW,CAC/C,CAAC;AACF,QAAA,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,WAAW,CAAA,CAAE,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,OAAO,KAAK;aACT,KAAK,CAAC,CAAC,CAAC;aACR,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;KAC3E;AAEO,IAAA,MAAM,eAAe,CAC3B,QAAiB,EACjB,OAAiB,EAAE,EAAA;QAEnB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO;AACR,SAAA;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KACxD;AAEO,IAAA,MAAM,YAAY,CACxB,QAAiB,EACjB,OAAiB,EAAE,EAAA;QAEnB,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEhD,QAAA,MAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACpC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAE9D,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,mBAAA,CAAqB,CAAC,CAAC;AACxC,SAAA;;AAGD,QAAA,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,YAAA,OAAO,WAAW,CAAC;AACpB,SAAA;;;;QAKD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;AACpD,YAAA,SAAS,EAAE,WAAwB;YACnC,IAAI,EAAE,QAAQ,CAAC,OAAO;AACvB,SAAA,CAAC,CAAC;;;AAIH,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,QAAA,OAAO,UAAU,CAAC;KACnB;IAEO,iBAAiB,CAAU,OAAU,EAAE,IAAmB,EAAA;AAChE,QAAA,IAAI,YAAiB,CAAC;AACtB,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,YAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,SAAA;AACD,QAAA,OAAO,YAAY,CAAC;KACrB;;;;;IAMO,aAAa,CAAC,IAAkB,EAAE,KAAU,EAAA;AAClD,QAAA,QAAQ,IAAI;AACV,YAAA,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9B,YAAA,KAAK,KAAK;gBACR,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxB,gBAAA,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACvB,gBAAA,OAAO,CAAC,CAAC;AACX,YAAA,KAAK,KAAK;gBACR,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,gBAAA,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACzB,gBAAA,OAAO,GAAG,CAAC;AACb,YAAA,KAAK,KAAK;gBACR,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACrC,gBAAA,OAAO,IAAI,CAAC;AACd,YAAA,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,IAAI,CAACc,QAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,YAAA,KAAK,WAAW;AACd,gBAAA,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1B,YAAA;gBACE,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,oBAAA,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,iBAAA;AACD,gBAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAA,CAAE,CAAC,CAAC;AACpD,SAAA;KACF;;AAvfe,gBAAA,CAAA,cAAc,GAAG;AAC/B,IAAA,sBAAsB,EAAE,qBAAqB;AAC7C,IAAA,IAAI,EAAE,kBAAkB;IACxB,aAAa,EAAE,aAAa,CAAC,SAAS;AACtC,IAAA,YAAY,EAAE,gBAAgB;AAC9B,IAAA,KAAK,EAAE,mBAAmB;CAC3B,CAAC;AAofJ;MACa,YAAY,CAAA;;AAKvB,IAAA,WAAA,CACU,SAAmB,EAC3B,SAAgC,EACxB,UAAqB,EAAA;QAFrB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAEnB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;AAPvB,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAAe,CAAC;QAChC,IAAK,CAAA,KAAA,GAA0C,EAAE,CAAC;QAQxD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC;KAC/C;IAEO,MAAM,SAAS,CACrB,SAAoB,EAAA;QAEpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE;AACrC,YAAA,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9D,YAAA,IAAI,GAAG,EAAE;AACP,gBAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,gBAAA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;AACpD,aAAA;AACF,SAAA;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;KACzC;AAEM,IAAA,MAAM,YAAY,CAAU,EACjC,SAAS,EACT,IAAI,EACJ,SAAS,GAAG,IAAI,CAAC,UAAU,GAK5B,EAAA;AACC,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC7B,IAAI,IAAI,KAAK,cAAc,EAAE;AAC3B,gBAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CAChE,SAAS,CACV,CAAC;gBACF,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,oBAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,OAAO,CAAA,CAAE,CAAC,CAAC;AACxD,iBAAA;gBACD,MAAM,IAAI,GAAG,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAChC,aAAA;AAAM,iBAAA,IAAI,IAAI,EAAE;gBACf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACjD,gBAAA,IAAI,QAAQ,EAAE;oBACZ,MAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,oBAAA,IAAI,cAAc,EAAE;wBAClB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACnC,qBAAA;AACF,iBAAA;AACF,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CAC5D,SAAS,CACV,CAAC;gBACF,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,oBAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,OAAO,CAAA,CAAE,CAAC,CAAC;AACxD,iBAAA;AACD,gBAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBAC1B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrD,gBAAA,IAAI,QAAQ,EAAE;oBACZ,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAQ,CAAC;oBAC7D,IAAI,CAAC,kBAAkB,EAAE;AACvB,wBAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACjD,qBAAA;AAED,oBAAA,MAAM,MAAM,GACV,kBAAkB,CAAC,KAAK,CAAC,QAC1B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAClC,iBAAA;AACF,aAAA;AACF,SAAA;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KACjC;AACF;;MC5lBY,qBAAqB,CAAA;IACzB,OAAO,KAAK,CACjB,QAAkB,EAClB,SAAoB,EACpB,KAA2B,EAC3B,IAAwB,EACxB,IAAwB,EACxB,KAAiB,EACjB,UAA2B,EAC3B,MAA+B,EAC/B,gBAAuC,EACvC,QAAsB,EACtB,cAA2C,EAAA;AAE3C,QAAA,OAAO,CAAC,GAAG,IAAI,KACb,IAAI,cAAc,CAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,UAAU,EACV,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,cAAc,CACf,CAAC;KACL;AACF,CAAA;AAaK,SAAU,iBAAiB,CAC/B,cAA8C,EAAA;AAE9C,IAAA,QACE,OAAO,cAAc,KAAK,QAAQ;AAClC,QAAA,cAAc,KAAK,IAAI;AACvB,QAAA,EAAE,KAAK,IAAI,cAAc,CAAC;MAC1B;AACJ,CAAC;AAEe,SAAA,sBAAsB,CACpC,eAAmC,EACnC,WAAoB,EAAA;IAEpB,MAAM,QAAQ,GAAoB,EAAE,CAAC;AACrC,IAAA,KAAK,MAAM,WAAW,IAAI,eAAe,EAAE;AACzC,QAAA,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,IAAI,WAAW;AACb,gBAAA,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;YACJ,SAAS;AACV,SAAA;AACD,QAAA,QAAQ,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC,OAAO,CAAC;AAChD,cAAE,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC;AACvC,cAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC/B,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;MAEY,cAAc,CAAA;IAUzB,WACE,CAAA,KAAiB,EACT,KAAyB,EACzB,KAAyB,EACzB,MAAkB,EAClB,WAA4B,EAC5B,OAAgC,EACxC,SAAmB,EACX,UAAqB,EAC7B,MAA4B,EAC5B,iBAAwC,EACxC,SAAuB,EACvB,eAA4C,EAAA;QAVpC,IAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;QACzB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;QACzB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAY;QAClB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAiB;QAC5B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAyB;QAEhC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QAjBd,IAAS,CAAA,SAAA,GAAoB,EAAE,CAAC;QACzC,IAAkB,CAAA,kBAAA,GAAuB,EAAE,CAAC;QAC5C,IAAQ,CAAA,QAAA,GAAkB,EAAE,CAAC;QAC7B,IAAgB,CAAA,gBAAA,GAAkC,EAAE,CAAC;QACrD,IAAiB,CAAA,iBAAA,GAAkC,EAAE,CAAC;QAEtD,IAAoB,CAAA,oBAAA,GAAY,IAAI,CAAC;AAiB3C,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAC3C,KAAK,EACL,IAAI,CAAC,SAAS,EACd,SAAS,EACT,UAAU,EACV,MAAM,EACN,iBAAiB,EACjB,SAAS,EACT,eAAe,CAChB,CAAC;KACH;AAEM,IAAA,IAAI,CAAC,KAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACpC;AAEM,IAAA,MAAM,OAAO,GAAA;QAGlB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;QACD,OAAO,IAAI,CAAC,SAEX,CAAC;KACH;AAEM,IAAA,QAAQ,CACb,QAAgD,EAAA;AAEhD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAClD,QAAA,OAAO,IAAI,CAAC;KACb;AAEM,IAAA,cAAc,CACnB,QAAyC,EAAA;AAEzC,QAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;AAClC,QAAA,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAClD,QAAA,OAAO,IAAI,CAAC;KACb;AAEM,IAAA,OAAO,CAAC,OAAsB,EAAA;QACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC9C,QAAA,OAAO,IAAI,CAAC;KACb;AAEM,IAAA,iBAAiB,CACtB,QAA4B,EAAA;QAE5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnE,QAAA,OAAO,IAAI,CAAC;KACb;AAEM,IAAA,eAAe,CACpB,GAAkC,EAAA;QAElC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC;KACb;AAEM,IAAA,gBAAgB,CACrB,GAAkC,EAAA;QAElC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5D,QAAA,OAAO,IAAI,CAAC;KACb;IAEM,MAAM,GAAG,CAAC,OAAwB,EAAA;QACvC,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;;QAGD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAChC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;AACxC,YAAA,OAAO,EAAE,OAAO;AACjB,SAAA,CAAC,CAAC;KACJ;IAEM,MAAM,UAAU,CAAC,OAAwB,EAAA;AAI9C,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACrC,OAAO;YACL,OAAO;AACP,YAAA,SAAS,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;SACnC,CAAC;KACH;IAEM,MAAM,IAAI,CAAC,OAAwB,EAAA;QACxC,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAClD,SAAA;;QAGD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;AACxC,YAAA,OAAO,EAAE,OAAO;AACjB,SAAA,CAAC,CAAC;KACJ;IAEM,MAAM,QAAQ,CACnB,OAAwB,EAAA;QAExB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;;QAGD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;AACxC,YAAA,OAAO,EAAE,OAAO;AACjB,SAAA,CAAC,CAAC;KACJ;AAEM,IAAA,MAAM,WAAW,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;;QAGD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;AACzC,SAAA,CAAC,CAAC;KACJ;AAED;;;AAGG;AACI,IAAA,MAAM,OAAO,GAAA;QAKlB,OAAO;AACL,YAAA,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;AACrC,YAAA,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;AAC7B,YAAA,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ;SAC7B,CAAC;KACH;AAEM,IAAA,MAAM,WAAW,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACxC,SAAA;;QAGD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;AACzC,SAAA,CAAC,CAAC;KACJ;AACF;;AChTa,MAAO,WAAW,CAAA;IACvB,OAAO,KAAK,CACjB,SAAoB,EACpB,KAA2B,EAC3B,UAA2B,EAC3B,GAAQ,EAAA;AAER,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAa,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9D,QAAA,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAClC,IAAI,KAAK,IAAI,CAAC,SAAS;YAAE,OAAO;AAEhC,QAAA,MAAM,IAAI,GAAgB,OAAO,GAAG,IAAI,KAAI;;YAC1C,IAAI,gBAAgB,GAAG,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;AACjD,YAAA,MAAM,YAAY,GAAG,CAAmB,gBAAA,EAAA,SAAS,GAAG,CAAC;YACrD,IAAI,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAC1C,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAC3B,CAAC;YACF,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAC7C,aAAA;AACD,YAAA,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,YAAA,IAAI,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,UAAU,EAAE;AACf,gBAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC9C,aAAA;AACD,YAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAChC,EAAE,IAAI,EAAE,UAAU,EAAE,EACpB,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA,EAAA,GAAA,GAAG,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC,EAAE,IAAI,CAAA,EAAA,GAAA,GAAG,CAAC,KAAK,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC,CAC5D,CAAC;AACF,YAAA,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAClC,SAAC,CAAC;AACF,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;ACnBa,MAAO,gBAAgB,CAAA;AACnC;;AAEG;IACI,OAAO,KAAK,CACjB,GAAQ,EACR,KAAY,EACZ,SAAoB,EACpB,QAAkB,EAClB,iBAE2C,EAAA;QAU3C,MAAM,GAAG,GAAiB,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAAsB,EAAE,CAAC;QACvC,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,IAAI,GAAkB,EAAE,CAAC;AAE/B,QAAA,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAEtC,QAAA,MAAM,OAAO,GAA0B,GAAG,CAAC,QAAQ;AACjD,cAAE,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC;cACpD,EAA4B,CAAC;QAElC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACjC,YAAA,MAAM,MAAM,GAAGC,2BAAkB,CAAC,KAAK,CACrC,KAAK,EACL,CAAC,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EACpD,SAAS,CACV,CAAC;YACF,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACvD,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CACxC,KAAK,EACL,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,SAAS,EACT,GAAG,CACJ,CAAC;AACF,YAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;AACxE,YAAA,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAC5C,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,GAAG,CAAC,KAAK,IAAI,EAAE,EACf,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAC9C,CAAC;YACF,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAEnC,YAAA,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3B,YAAA,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3B,YAAA,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AACpB,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;AAC9B,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AAC3B,YAAA,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;AACvB,aAAA;AACH,SAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAwB;YACxB,WAAwC;YACxC,WAAwC;YACxC,OAAO;YACP,QAAkC;YAClC,OAAgC;YAChC,IAA0B;SAC3B,CAAC;KACH;AACF;;ACxFD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;MACU,OAAO,CAAA;AAsKlB;;AAEG;AACH,IAAA,IAAW,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAGD;;AAEG;AACH,IAAA,IAAW,GAAG,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;AAGD;;AAEG;AACH,IAAA,IAAW,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;AAGD;;AAEG;AACH,IAAA,IAAW,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAQD;;;;;;;;AAQG;IACH,WACE,CAAA,GAAQ,EACR,SAAkB,EAClB,QAAmB,EACnB,KAAa,EACb,iBAE2C,EAAA;AAE3C,QAAA,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAExC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,WAAW,EAAE,CAAC;AAC1B,SAAA;;AAGD,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAChB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,aAAL,KAAK,KAAA,KAAA,CAAA,GAAL,KAAK,GAAI,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;AAGxE,QAAA,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,GACtE,gBAAgB,CAAC,KAAK,CACpB,GAAG,EACH,IAAI,CAAC,MAAM,EACX,SAAS,EACT,QAAQ,EACR,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,cAAjB,iBAAiB,IAAK,MAAM,SAAS,CAAC,CACvC,CAAC;AACJ,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;AAED;;;;;;;;AAQG;AACI,IAAA,aAAa,EAAE,CACpB,OAAgB,EAChB,QAAmB,EAAA;AAEnB,QAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE5C,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAM,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,CAA8B,2BAAA,EAAA,OAAO,CAAC,QAAQ,EAAE,CAAE,CAAA,CAAC,CAAC;AACrE,SAAA;QAED,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC9C;AAED;;;;;;;;AAQG;AACI,IAAA,aAAa,QAAQ,CAC1B,OAAgB,EAChB,QAAmB,EAAA;QAEnB,QAAQ,GAAG,QAAQ,KAAR,IAAA,IAAA,QAAQ,cAAR,QAAQ,GAAI,WAAW,EAAE,CAAC;AACrC,QAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAE5C,QAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;;AAED,QAAA,IAAI,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,KAAK,CAACC,QAAW,CAAC,WAAW,CAAC,CAAC,CAAC;KAC7C;AAED;;;;;;AAMG;IACI,gBAAgB,CACrB,SAAiB,EACjB,QAA+D,EAAA;QAE/D,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC3D;AAED;;AAEG;IACI,MAAM,mBAAmB,CAAC,QAAgB,EAAA;QAC/C,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;KACzD;AACF;;AC/WD,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;AAEtE,SAAU,OAAO,CAAC,QAAmB,EAAA;AACzC,IAAA,OAAO,IAAI,OAAO,CAAgB,GAAG,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/E,CAAC;SAEe,KAAK,GAAA;AACnB,IAAA,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAC9B,CAAC;AAmYM,MAAM,GAAG,GAAkB;AAChC,IAAA,OAAO,EAAE,OAAO;AAChB,IAAA,IAAI,EAAE,gBAAgB;AACtB,IAAA,YAAY,EAAE;AACZ,QAAA;AACE,YAAA,IAAI,EAAE,eAAe;AACrB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,QAAQ;AACd,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,QAAQ;AACd,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,uBAAuB;AAC7B,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,qBAAqB;AAC3B,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,mBAAmB;AACzB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,sBAAsB;AAC5B,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,mBAAmB;AACzB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,wBAAwB;AAC9B,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,mBAAmB;AACzB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,uBAAuB;AAC7B,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,YAAY;AAClB,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,QAAQ;AACd,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,kBAAkB;AACxB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,SAAS;AACf,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,gBAAgB;AACtB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,SAAS;AACf,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA;AACE,YAAA,IAAI,EAAE,kBAAkB;AACxB,YAAA,QAAQ,EAAE;AACR,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,QAAQ,EAAE,IAAI;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,QAAQ,EAAE,KAAK;AAChB,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA;AACE,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;AACD,gBAAA;AACE,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,QAAQ,EAAE;AACR,QAAA;AACE,YAAA,IAAI,EAAE,OAAO;AACb,YAAA,IAAI,EAAE;AACJ,gBAAA,IAAI,EAAE,QAAQ;AACd,gBAAA,MAAM,EAAE;AACN,oBAAA;AACE,wBAAA,IAAI,EAAE,SAAS;AACf,wBAAA,IAAI,EAAE,KAAK;AACZ,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,OAAO;AACb,wBAAA,IAAI,EAAE,KAAK;AACZ,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,kBAAkB;AACxB,wBAAA,IAAI,EAAE,WAAW;AAClB,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,OAAO;AACb,wBAAA,IAAI,EAAE,WAAW;AAClB,qBAAA;AACD,oBAAA;AACE,wBAAA,IAAI,EAAE,eAAe;AACrB,wBAAA,IAAI,EAAE;AACJ,4BAAA,OAAO,EAAE,eAAe;AACzB,yBAAA;AACF,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,KAAK,EAAE;AACL,QAAA;AACE,YAAA,IAAI,EAAE,eAAe;AACrB,YAAA,IAAI,EAAE;AACJ,gBAAA,IAAI,EAAE,QAAQ;AACd,gBAAA,MAAM,EAAE;AACN,oBAAA;AACE,wBAAA,IAAI,EAAE,sBAAsB;AAC5B,wBAAA,IAAI,EAAE,KAAK;AACZ,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;CACF;;MCvwBY,MAAM,CAAA;IACV,OAAO,MAAM,CAAC,QAAmB,EAAA;AACtC,QAAA,OAAOC,OAAa,CAAC,QAAQ,CAAC,CAAC;KAChC;AACF;;;;"}