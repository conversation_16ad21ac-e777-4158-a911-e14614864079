{"name": "@agario-solana/anchor-program", "version": "0.1.0", "private": true, "scripts": {"build": "anchor build && node scripts/copy-idl.js", "build:program": "anchor build", "test": "anchor test", "deploy": "anchor deploy", "clean": "anchor clean", "lint": "cargo clippy -- -D warnings", "format": "cargo fmt", "copy-idl": "node scripts/copy-idl.js"}, "devDependencies": {"@coral-xyz/anchor": "^0.28.0", "@solana/web3.js": "^1.87.6", "chai": "^4.3.7", "mocha": "^10.2.0", "ts-mocha": "^10.0.0", "typescript": "^5.1.6"}}