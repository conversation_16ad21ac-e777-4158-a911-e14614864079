use anchor_lang::prelude::*;
use anchor_spl::token::{<PERSON><PERSON>, TokenAccount};
use pyth_sdk_solana::load_price_feed_from_account_info;

declare_id!("Fg6PaFpoGXkYsidMpWTK6W2BeZ7FEfcYkg476zPFsLnS");

#[program]
pub mod agar_escrow {
    use super::*;

    /// Initialize the global configuration (admin only)
    pub fn initialize_global(
        ctx: Context<InitializeGlobal>,
        platform_fee_bps: u16,
        oracle_safety_margin_bps: u16,
    ) -> Result<()> {
        let global_config = &mut ctx.accounts.global_config;
        global_config.authority = ctx.accounts.authority.key();
        global_config.platform_fee_bps = platform_fee_bps;
        global_config.oracle_safety_margin_bps = oracle_safety_margin_bps;
        global_config.paused = false;
        global_config.bump = *ctx.bumps.get("global_config").unwrap();
        
        msg!("Global configuration initialized");
        Ok(())
    }

    /// Create a new lobby for a specific wager tier (admin only)
    pub fn create_lobby(
        ctx: Context<CreateLobby>,
        tier: u8,
        min_wager_lamports: u64,
    ) -> Result<()> {
        let lobby = &mut ctx.accounts.lobby;
        lobby.tier = tier;
        lobby.min_wager_lamports = min_wager_lamports;
        lobby.total_pool = 0;
        lobby.active_players = 0;
        lobby.bump = *ctx.bumps.get("lobby").unwrap();
        
        msg!("Lobby created for tier {} with min wager {} lamports", tier, min_wager_lamports);
        Ok(())
    }

    /// Initialize the treasury PDA (admin only)
    pub fn initialize_treasury(ctx: Context<InitializeTreasury>) -> Result<()> {
        msg!("Treasury PDA initialized");
        Ok(())
    }

    /// Deposit SOL to user's treasury balance
    pub fn deposit(ctx: Context<Deposit>, amount: u64) -> Result<()> {
        require!(!ctx.accounts.global_config.paused, ErrorCode::GlobalPaused);
        require!(amount > 0, ErrorCode::InvalidDepositAmount);

        // Transfer SOL from user to treasury PDA
        let cpi_context = CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            anchor_lang::system_program::Transfer {
                from: ctx.accounts.user.to_account_info(),
                to: ctx.accounts.treasury.to_account_info(),
            },
        );
        anchor_lang::system_program::transfer(cpi_context, amount)?;

        // Initialize or update user state
        let user_state = &mut ctx.accounts.user_state;
        if user_state.wallet == Pubkey::default() {
            // First time initialization
            user_state.wallet = ctx.accounts.user.key();
            user_state.bump = *ctx.bumps.get("user_state").unwrap();
        }
        user_state.deposited_balance = user_state.deposited_balance.checked_add(amount)
            .ok_or(ErrorCode::MathOverflow)?;
        user_state.total_deposited = user_state.total_deposited.checked_add(amount)
            .ok_or(ErrorCode::MathOverflow)?;

        msg!("Deposited {} lamports to treasury", amount);
        Ok(())
    }

    /// Enter a lobby with a wager
    pub fn enter_lobby(ctx: Context<EnterLobby>, wager_amount: u64, session_token: [u8; 32]) -> Result<()> {
        require!(!ctx.accounts.global_config.paused, ErrorCode::GlobalPaused);
        require!(wager_amount >= ctx.accounts.lobby.min_wager_lamports, ErrorCode::InvalidWagerAmount);
        require!(ctx.accounts.user_state.deposited_balance >= wager_amount, ErrorCode::InsufficientBalance);

        // Deduct wager from user's deposited balance
        let user_state = &mut ctx.accounts.user_state;
        user_state.deposited_balance = user_state.deposited_balance.checked_sub(wager_amount)
            .ok_or(ErrorCode::InsufficientBalance)?;

        // Update lobby state
        let lobby = &mut ctx.accounts.lobby;
        lobby.total_pool = lobby.total_pool.checked_add(wager_amount)
            .ok_or(ErrorCode::MathOverflow)?;
        lobby.active_players = lobby.active_players.checked_add(1)
            .ok_or(ErrorCode::MathOverflow)?;

        // Calculate initial mass based on wager (base formula: mass = 100 + wager_sol * 50)
        let base_mass = 100u32;
        let wager_sol = wager_amount / 1_000_000_000; // Convert lamports to SOL
        let mass_multiplier = 50u32;
        let initial_mass = base_mass.checked_add(
            (wager_sol as u32).checked_mul(mass_multiplier).ok_or(ErrorCode::MathOverflow)?
        ).ok_or(ErrorCode::MathOverflow)?;

        // Initialize session
        let session = &mut ctx.accounts.session;
        session.user = ctx.accounts.user.key();
        session.lobby = ctx.accounts.lobby.key();
        session.wager_amount = wager_amount;
        session.initial_mass = initial_mass;
        session.session_token = session_token;
        session.created_at = Clock::get()?.unix_timestamp;
        session.bump = *ctx.bumps.get("session").unwrap();

        msg!("User entered lobby with wager {} lamports, initial mass: {}", wager_amount, initial_mass);
        Ok(())
    }

    /// Cash out current session value
    pub fn cash_out(ctx: Context<CashOut>, current_mass: u32) -> Result<()> {
        require!(!ctx.accounts.global_config.paused, ErrorCode::GlobalPaused);

        // Load and validate Pyth price feed
        let price_feed = load_price_feed_from_account_info(&ctx.accounts.pyth_price_account.to_account_info())
            .map_err(|_| ErrorCode::InvalidPriceAccount)?;

        let price = price_feed.get_current_price()
            .ok_or(ErrorCode::StalePriceData)?;

        // Check price staleness (5 minutes = 300 seconds)
        let current_time = Clock::get()?.unix_timestamp;
        require!(
            current_time - price.publish_time <= 300,
            ErrorCode::StalePriceData
        );

        // Check confidence interval (should be within safety margin)
        let confidence_pct = (price.conf as u64 * 10000) / (price.price as u64);
        require!(
            confidence_pct <= ctx.accounts.global_config.oracle_safety_margin_bps as u64,
            ErrorCode::WideConfidenceInterval
        );

        // Calculate SOL value from mass
        // Formula: sol_value = (current_mass / initial_mass) * wager_amount
        let session = &ctx.accounts.session;
        let mass_ratio = (current_mass as u64 * 1_000_000) / (session.initial_mass as u64); // Scale for precision
        let gross_sol_value = (session.wager_amount * mass_ratio) / 1_000_000;

        // Apply platform fee
        let platform_fee = (gross_sol_value * ctx.accounts.global_config.platform_fee_bps as u64) / 10_000;
        let net_sol_value = gross_sol_value.checked_sub(platform_fee)
            .ok_or(ErrorCode::MathOverflow)?;

        require!(net_sol_value > 0, ErrorCode::InsufficientCashoutValue);

        // Transfer SOL from treasury to user
        let treasury_info = ctx.accounts.treasury.to_account_info();
        let user_info = ctx.accounts.user.to_account_info();

        **treasury_info.try_borrow_mut_lamports()? = treasury_info.lamports()
            .checked_sub(net_sol_value)
            .ok_or(ErrorCode::InsufficientTreasuryBalance)?;
        **user_info.try_borrow_mut_lamports()? = user_info.lamports()
            .checked_add(net_sol_value)
            .ok_or(ErrorCode::MathOverflow)?;

        // Update lobby state
        let lobby = &mut ctx.accounts.lobby;
        lobby.total_pool = lobby.total_pool.checked_sub(session.wager_amount)
            .ok_or(ErrorCode::MathOverflow)?;
        lobby.active_players = lobby.active_players.checked_sub(1)
            .ok_or(ErrorCode::MathOverflow)?;

        // Update user state
        let user_state = &mut ctx.accounts.user_state;
        user_state.total_withdrawn = user_state.total_withdrawn.checked_add(net_sol_value)
            .ok_or(ErrorCode::MathOverflow)?;
        user_state.last_cashout_timestamp = current_time;

        msg!("Cashed out {} lamports (fee: {} lamports) for mass {}", net_sol_value, platform_fee, current_mass);
        Ok(())
    }

    /// Withdraw unused deposited SOL
    pub fn withdraw_unused(ctx: Context<WithdrawUnused>, amount: u64) -> Result<()> {
        require!(!ctx.accounts.global_config.paused, ErrorCode::GlobalPaused);
        require!(amount > 0, ErrorCode::InvalidWithdrawAmount);
        require!(ctx.accounts.user_state.deposited_balance >= amount, ErrorCode::InsufficientBalance);

        // Transfer SOL from treasury to user
        let treasury_info = ctx.accounts.treasury.to_account_info();
        let user_info = ctx.accounts.user.to_account_info();

        **treasury_info.try_borrow_mut_lamports()? = treasury_info.lamports()
            .checked_sub(amount)
            .ok_or(ErrorCode::InsufficientTreasuryBalance)?;
        **user_info.try_borrow_mut_lamports()? = user_info.lamports()
            .checked_add(amount)
            .ok_or(ErrorCode::MathOverflow)?;

        // Update user state
        let user_state = &mut ctx.accounts.user_state;
        user_state.deposited_balance = user_state.deposited_balance.checked_sub(amount)
            .ok_or(ErrorCode::InsufficientBalance)?;
        user_state.total_withdrawn = user_state.total_withdrawn.checked_add(amount)
            .ok_or(ErrorCode::MathOverflow)?;

        msg!("Withdrew {} lamports from deposited balance", amount);
        Ok(())
    }

    /// Set pause state (admin only)
    pub fn set_pause(ctx: Context<SetPause>, paused: bool) -> Result<()> {
        let global_config = &mut ctx.accounts.global_config;
        global_config.paused = paused;

        msg!("Global pause state set to: {}", paused);
        Ok(())
    }

    /// Update platform fee (admin only)
    pub fn update_fees(ctx: Context<UpdateFees>, platform_fee_bps: u16, oracle_safety_margin_bps: u16) -> Result<()> {
        require!(platform_fee_bps <= 1000, ErrorCode::InvalidFeeAmount); // Max 10%
        require!(oracle_safety_margin_bps <= 1000, ErrorCode::InvalidMarginAmount); // Max 10%

        let global_config = &mut ctx.accounts.global_config;
        global_config.platform_fee_bps = platform_fee_bps;
        global_config.oracle_safety_margin_bps = oracle_safety_margin_bps;

        msg!("Updated fees - platform: {} bps, oracle margin: {} bps", platform_fee_bps, oracle_safety_margin_bps);
        Ok(())
    }

    /// Record elimination (server authority only)
    pub fn record_elimination(ctx: Context<RecordElimination>, victim_session_id: Pubkey, killer_session_id: Pubkey, transferred_mass: u32) -> Result<()> {
        // This instruction is called by the server to record eliminations
        // The actual mass transfer logic is handled by the game server
        // This is mainly for audit trail and potential future on-chain verification

        msg!("Elimination recorded: victim {} killed by {} with {} mass transferred",
             victim_session_id, killer_session_id, transferred_mass);
        Ok(())
    }
}

#[derive(Accounts)]
pub struct InitializeGlobal<'info> {
    #[account(
        init,
        payer = authority,
        space = 8 + GlobalConfig::INIT_SPACE,
        seeds = [b"global_config"],
        bump
    )]
    pub global_config: Account<'info, GlobalConfig>,
    
    #[account(mut)]
    pub authority: Signer<'info>,
    
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
#[instruction(tier: u8)]
pub struct CreateLobby<'info> {
    #[account(
        init,
        payer = authority,
        space = 8 + Lobby::INIT_SPACE,
        seeds = [b"lobby", tier.to_le_bytes().as_ref()],
        bump
    )]
    pub lobby: Account<'info, Lobby>,
    
    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump,
        has_one = authority
    )]
    pub global_config: Account<'info, GlobalConfig>,
    
    #[account(mut)]
    pub authority: Signer<'info>,
    
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct InitializeTreasury<'info> {
    #[account(
        init,
        payer = authority,
        space = 0,
        seeds = [b"treasury"],
        bump
    )]
    pub treasury: SystemAccount<'info>,

    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump,
        has_one = authority
    )]
    pub global_config: Account<'info, GlobalConfig>,

    #[account(mut)]
    pub authority: Signer<'info>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Deposit<'info> {
    #[account(
        init_if_needed,
        payer = user,
        space = 8 + UserState::INIT_SPACE,
        seeds = [b"user_state", user.key().as_ref()],
        bump
    )]
    pub user_state: Account<'info, UserState>,

    #[account(
        mut,
        seeds = [b"treasury"],
        bump
    )]
    pub treasury: SystemAccount<'info>,

    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump
    )]
    pub global_config: Account<'info, GlobalConfig>,

    #[account(mut)]
    pub user: Signer<'info>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct EnterLobby<'info> {
    #[account(
        init,
        payer = user,
        space = 8 + Session::INIT_SPACE,
        seeds = [b"session", user.key().as_ref(), lobby.key().as_ref()],
        bump
    )]
    pub session: Account<'info, Session>,

    #[account(
        mut,
        seeds = [b"user_state", user.key().as_ref()],
        bump = user_state.bump
    )]
    pub user_state: Account<'info, UserState>,

    #[account(
        mut,
        seeds = [b"lobby", lobby.tier.to_le_bytes().as_ref()],
        bump = lobby.bump
    )]
    pub lobby: Account<'info, Lobby>,

    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump
    )]
    pub global_config: Account<'info, GlobalConfig>,

    #[account(mut)]
    pub user: Signer<'info>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct CashOut<'info> {
    #[account(
        mut,
        close = user,
        seeds = [b"session", user.key().as_ref(), lobby.key().as_ref()],
        bump = session.bump,
        has_one = user
    )]
    pub session: Account<'info, Session>,

    #[account(
        mut,
        seeds = [b"user_state", user.key().as_ref()],
        bump = user_state.bump,
        has_one = wallet @ ErrorCode::InvalidUserState
    )]
    pub user_state: Account<'info, UserState>,

    #[account(
        mut,
        seeds = [b"lobby", lobby.tier.to_le_bytes().as_ref()],
        bump = lobby.bump
    )]
    pub lobby: Account<'info, Lobby>,

    #[account(
        mut,
        seeds = [b"treasury"],
        bump
    )]
    pub treasury: SystemAccount<'info>,

    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump
    )]
    pub global_config: Account<'info, GlobalConfig>,

    /// Pyth SOL/USD price account
    /// CHECK: Validated in instruction logic using Pyth SDK
    pub pyth_price_account: AccountInfo<'info>,

    #[account(mut)]
    pub user: Signer<'info>,
}

#[derive(Accounts)]
pub struct WithdrawUnused<'info> {
    #[account(
        mut,
        seeds = [b"user_state", user.key().as_ref()],
        bump = user_state.bump,
        has_one = wallet @ ErrorCode::InvalidUserState
    )]
    pub user_state: Account<'info, UserState>,

    #[account(
        mut,
        seeds = [b"treasury"],
        bump
    )]
    pub treasury: SystemAccount<'info>,

    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump
    )]
    pub global_config: Account<'info, GlobalConfig>,

    #[account(mut)]
    pub user: Signer<'info>,
}

#[derive(Accounts)]
pub struct SetPause<'info> {
    #[account(
        mut,
        seeds = [b"global_config"],
        bump = global_config.bump,
        has_one = authority
    )]
    pub global_config: Account<'info, GlobalConfig>,

    pub authority: Signer<'info>,
}

#[derive(Accounts)]
pub struct UpdateFees<'info> {
    #[account(
        mut,
        seeds = [b"global_config"],
        bump = global_config.bump,
        has_one = authority
    )]
    pub global_config: Account<'info, GlobalConfig>,

    pub authority: Signer<'info>,
}

#[derive(Accounts)]
pub struct RecordElimination<'info> {
    #[account(
        seeds = [b"global_config"],
        bump = global_config.bump,
        has_one = authority
    )]
    pub global_config: Account<'info, GlobalConfig>,

    pub authority: Signer<'info>,
}

#[account]
#[derive(InitSpace)]
pub struct GlobalConfig {
    pub authority: Pubkey,
    pub platform_fee_bps: u16,
    pub oracle_safety_margin_bps: u16,
    pub paused: bool,
    pub bump: u8,
}

#[account]
#[derive(InitSpace)]
pub struct Lobby {
    pub tier: u8,
    pub min_wager_lamports: u64,
    pub total_pool: u64,
    pub active_players: u32,
    pub bump: u8,
}

#[account]
#[derive(InitSpace)]
pub struct UserState {
    pub wallet: Pubkey,
    pub deposited_balance: u64,
    pub last_cashout_timestamp: i64,
    pub total_deposited: u64,
    pub total_withdrawn: u64,
    pub bump: u8,
}

impl UserState {
    pub fn new(wallet: Pubkey, bump: u8) -> Self {
        Self {
            wallet,
            deposited_balance: 0,
            last_cashout_timestamp: 0,
            total_deposited: 0,
            total_withdrawn: 0,
            bump,
        }
    }
}

#[account]
#[derive(InitSpace)]
pub struct Session {
    pub user: Pubkey,
    pub lobby: Pubkey,
    pub wager_amount: u64,
    pub initial_mass: u32,
    pub session_token: [u8; 32],
    pub created_at: i64,
    pub bump: u8,
}

#[error_code]
pub enum ErrorCode {
    #[msg("Global configuration is paused")]
    GlobalPaused,
    #[msg("Insufficient balance")]
    InsufficientBalance,
    #[msg("Invalid wager amount")]
    InvalidWagerAmount,
    #[msg("Oracle price is stale")]
    StalePriceData,
    #[msg("Oracle confidence interval too wide")]
    WideConfidenceInterval,
    #[msg("Unauthorized")]
    Unauthorized,
    #[msg("Invalid deposit amount")]
    InvalidDepositAmount,
    #[msg("Invalid withdraw amount")]
    InvalidWithdrawAmount,
    #[msg("Invalid fee amount")]
    InvalidFeeAmount,
    #[msg("Invalid margin amount")]
    InvalidMarginAmount,
    #[msg("Math overflow")]
    MathOverflow,
    #[msg("Invalid price account")]
    InvalidPriceAccount,
    #[msg("Insufficient cashout value")]
    InsufficientCashoutValue,
    #[msg("Insufficient treasury balance")]
    InsufficientTreasuryBalance,
    #[msg("Invalid user state")]
    InvalidUserState,
}
