import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { PublicKey, Connection, Transaction } from '@solana/web3.js'

export interface WalletAdapter {
  name: string
  icon: string
  url: string
  readyState: 'Installed' | 'NotDetected' | 'Loadable' | 'Unsupported'
  publicKey: PublicKey | null
  connecting: boolean
  connected: boolean
  
  connect(): Promise<void>
  disconnect(): Promise<void>
  signTransaction(transaction: Transaction): Promise<Transaction>
  signAllTransactions(transactions: Transaction[]): Promise<Transaction[]>
  signMessage(message: Uint8Array): Promise<Uint8Array>
}

interface WalletState {
  // Connection state
  wallet: WalletAdapter | null
  publicKey: PublicKey | null
  connected: boolean
  connecting: boolean
  
  // Available wallets
  wallets: WalletAdapter[]
  
  // Balance and account info
  balance: number // SOL
  balanceUSD: number
  solPrice: number // USD per SOL
  
  // Transaction state
  pendingTransactions: string[]
  
  // Error state
  error: string | null
}

interface WalletStore extends WalletState {
  // Wallet actions
  setWallet: (wallet: WalletAdapter | null) => void
  connect: (walletName?: string) => Promise<void>
  disconnect: () => Promise<void>
  
  // Balance actions
  updateBalance: (balance: number) => void
  updateSolPrice: (price: number) => void
  refreshBalance: () => Promise<void>
  
  // Transaction actions
  addPendingTransaction: (signature: string) => void
  removePendingTransaction: (signature: string) => void
  
  // Error actions
  setError: (error: string | null) => void
  clearError: () => void
  
  // Initialization
  initialize: () => Promise<void>
}

const initialState: WalletState = {
  wallet: null,
  publicKey: null,
  connected: false,
  connecting: false,
  wallets: [],
  balance: 0,
  balanceUSD: 0,
  solPrice: 0,
  pendingTransactions: [],
  error: null,
}

// Mock wallet adapters for development
const createMockWalletAdapter = (name: string, icon: string, url: string): WalletAdapter => ({
  name,
  icon,
  url,
  readyState: 'NotDetected',
  publicKey: null,
  connecting: false,
  connected: false,
  
  async connect() {
    // Mock connection logic
    console.log(`Connecting to ${name}...`)
    await new Promise(resolve => setTimeout(resolve, 1000))
    throw new Error(`${name} wallet not available in development`)
  },
  
  async disconnect() {
    console.log(`Disconnecting from ${name}...`)
  },
  
  async signTransaction(transaction: Transaction) {
    throw new Error('Not implemented in mock')
  },
  
  async signAllTransactions(transactions: Transaction[]) {
    throw new Error('Not implemented in mock')
  },
  
  async signMessage(message: Uint8Array) {
    throw new Error('Not implemented in mock')
  }
})

export const useWalletStore = create<WalletStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    // Wallet actions
    setWallet: (wallet: WalletAdapter | null) => {
      set({ 
        wallet,
        publicKey: wallet?.publicKey || null,
        connected: wallet?.connected || false,
        connecting: wallet?.connecting || false
      })
    },
    
    connect: async (walletName?: string) => {
      const { wallets } = get()
      
      try {
        set({ connecting: true, error: null })
        
        let targetWallet: WalletAdapter | undefined
        
        if (walletName) {
          targetWallet = wallets.find(w => w.name === walletName)
        } else {
          // Try to find an installed wallet
          targetWallet = wallets.find(w => w.readyState === 'Installed')
        }
        
        if (!targetWallet) {
          throw new Error('No wallet available')
        }
        
        await targetWallet.connect()
        
        set({
          wallet: targetWallet,
          publicKey: targetWallet.publicKey,
          connected: targetWallet.connected,
          connecting: false
        })
        
        // Refresh balance after connection
        get().refreshBalance()
        
      } catch (error) {
        set({
          connecting: false,
          error: error instanceof Error ? error.message : 'Failed to connect wallet'
        })
        throw error
      }
    },
    
    disconnect: async () => {
      const { wallet } = get()
      
      try {
        if (wallet) {
          await wallet.disconnect()
        }
        
        set({
          wallet: null,
          publicKey: null,
          connected: false,
          connecting: false,
          balance: 0,
          balanceUSD: 0,
          pendingTransactions: [],
          error: null
        })
        
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to disconnect wallet'
        })
      }
    },
    
    // Balance actions
    updateBalance: (balance: number) => {
      const { solPrice } = get()
      set({ 
        balance,
        balanceUSD: balance * solPrice
      })
    },
    
    updateSolPrice: (price: number) => {
      const { balance } = get()
      set({ 
        solPrice: price,
        balanceUSD: balance * price
      })
    },
    
    refreshBalance: async () => {
      const { publicKey } = get()
      
      if (!publicKey) return
      
      try {
        // TODO: Replace with actual RPC endpoint
        const connection = new Connection('https://api.devnet.solana.com')
        const balance = await connection.getBalance(publicKey)
        const solBalance = balance / 1e9 // Convert lamports to SOL
        
        get().updateBalance(solBalance)
        
      } catch (error) {
        console.error('Failed to refresh balance:', error)
        set({
          error: error instanceof Error ? error.message : 'Failed to refresh balance'
        })
      }
    },
    
    // Transaction actions
    addPendingTransaction: (signature: string) => {
      set(state => ({
        pendingTransactions: [...state.pendingTransactions, signature]
      }))
    },
    
    removePendingTransaction: (signature: string) => {
      set(state => ({
        pendingTransactions: state.pendingTransactions.filter(sig => sig !== signature)
      }))
    },
    
    // Error actions
    setError: (error: string | null) => {
      set({ error })
    },
    
    clearError: () => {
      set({ error: null })
    },
    
    // Initialization
    initialize: async () => {
      try {
        // Initialize available wallets
        const wallets = [
          createMockWalletAdapter('Phantom', '/phantom-icon.svg', 'https://phantom.app/'),
          createMockWalletAdapter('Solflare', '/solflare-icon.svg', 'https://solflare.com/'),
          createMockWalletAdapter('Backpack', '/backpack-icon.svg', 'https://backpack.app/'),
        ]
        
        set({ wallets })
        
        // TODO: Check for installed wallets and update readyState
        // TODO: Auto-connect if previously connected
        
      } catch (error) {
        console.error('Failed to initialize wallets:', error)
        set({
          error: error instanceof Error ? error.message : 'Failed to initialize wallets'
        })
      }
    }
  }))
)

// Selectors for common wallet state
export const useWalletConnection = () => {
  return useWalletStore(state => ({
    wallet: state.wallet,
    publicKey: state.publicKey,
    connected: state.connected,
    connecting: state.connecting
  }))
}

export const useWalletBalance = () => {
  return useWalletStore(state => ({
    balance: state.balance,
    balanceUSD: state.balanceUSD,
    solPrice: state.solPrice
  }))
}

export const useWalletError = () => {
  return useWalletStore(state => ({
    error: state.error,
    clearError: state.clearError
  }))
}
