import { GameState } from '../state.js'
import { PlayerInput, GameEventType } from '../types.js'
import { GAME_CONFIG } from '../../config/lobby.js'
import { oracleService } from '../../solana/oracle.js'
import { transactionBuilder, transactionQueue } from '../../solana/transactions.js'
import { PublicKey } from '@solana/web3.js'
import pino from 'pino'

const logger = pino({ name: 'cashout-system' })

export class CashoutSystem {
  private gameState: GameState

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  update(deltaTime: number): void {
    const world = this.gameState.getWorld()
    const now = Date.now()

    // Check all players for cashout progress
    for (const player of world.players.values()) {
      if (!player.isAlive) continue

      this.updatePlayerCashout(player, now)
    }
  }

  processCashoutInput(input: PlayerInput): void {
    const player = this.gameState.getPlayer(input.playerId)
    if (!player || !player.isAlive) return

    const now = Date.now()

    if (player.cashoutStartTime) {
      // Player is already cashing out, cancel it
      this.cancelCashout(player, now)
    } else {
      // Start cashout process
      this.startCashout(player, now)
    }
  }

  private updatePlayerCashout(player: any, now: number): void {
    if (!player.cashoutStartTime) return

    const cashoutDuration = now - player.cashoutStartTime
    const requiredDuration = GAME_CONFIG.CASHOUT_DURATION

    // Check if cashout is complete
    if (cashoutDuration >= requiredDuration) {
      this.completeCashout(player, now)
      return
    }

    // Check if player moved during cashout (cancel if they did)
    if (this.hasPlayerMovedDuringCashout(player)) {
      this.cancelCashout(player, now, 'Player moved during cashout')
      return
    }

    // Check if player lost mass below minimum (cancel if they did)
    if (player.totalMass < GAME_CONFIG.CASHOUT_MIN_MASS) {
      this.cancelCashout(player, now, 'Mass fell below minimum')
      return
    }
  }

  private startCashout(player: any, now: number): void {
    // Validate cashout eligibility
    if (!this.canPlayerCashout(player)) {
      logger.warn(`Player ${player.id} attempted invalid cashout`)
      return
    }

    player.cashoutStartTime = now
    player.canCashout = false

    // Store position at start of cashout for movement detection
    player.cashoutStartPosition = {
      x: player.cells[0]?.position.x || 0,
      y: player.cells[0]?.position.y || 0,
    }

    logger.info(`Player ${player.id} started cashout process`)

    // Create event
    // This would be handled by the GameState class to emit to clients
  }

  private cancelCashout(player: any, now: number, reason: string = 'Player cancelled'): void {
    if (!player.cashoutStartTime) return

    player.cashoutStartTime = undefined
    player.cashoutStartPosition = undefined
    player.canCashout = player.totalMass >= GAME_CONFIG.CASHOUT_MIN_MASS

    logger.info(`Player ${player.id} cashout cancelled: ${reason}`)

    // Create event
    // This would be handled by the GameState class
  }

  private async completeCashout(player: any, now: number): Promise<void> {
    try {
      const finalValue = await this.calculateFinalPayout(player)

      logger.info(`Player ${player.id} completed cashout: ${finalValue} SOL`)

      // Create cashout transaction
      const playerWallet = new PublicKey(player.wallet)
      const serverWallet = new PublicKey('********************************') // TODO: Use actual server wallet

      const transaction = await transactionBuilder.createCashoutTransaction(
        playerWallet,
        player.sessionToken,
        finalValue,
        serverWallet
      )

      // Queue transaction for processing
      const signature = await transactionQueue.addTransaction(
        transaction,
        [], // TODO: Add server keypair
        'high' // High priority for cashouts
      )

      logger.info(`Cashout transaction queued: ${signature}`)

      // Remove player from game
      this.gameState.removePlayer(player.id, 'cashout')

    } catch (error) {
      logger.error(error, `Failed to complete cashout for player ${player.id}`)
      // Reset cashout state on failure
      this.cancelCashout(player, now, 'Transaction failed')
    }
  }

  private hasPlayerMovedDuringCashout(player: any): boolean {
    if (!player.cashoutStartPosition || player.cells.length === 0) return false

    const currentPosition = player.cells[0].position
    const startPosition = player.cashoutStartPosition

    const dx = currentPosition.x - startPosition.x
    const dy = currentPosition.y - startPosition.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    // Allow small movement tolerance
    return distance > GAME_CONFIG.CASHOUT_MOVEMENT_TOLERANCE
  }

  private canPlayerCashout(player: any): boolean {
    // Check if player is alive
    if (!player.isAlive) return false

    // Check if player has minimum mass
    if (player.totalMass < GAME_CONFIG.CASHOUT_MIN_MASS) return false

    // Check if player is not already cashing out
    if (player.cashoutStartTime) return false

    // Check if player has been in game long enough
    const gameTime = Date.now() - player.lastValidatedTime
    if (gameTime < GAME_CONFIG.MIN_GAME_TIME_FOR_CASHOUT) return false

    // Check if player has positive value
    if (player.currentValue <= 0) return false

    return true
  }

  // Public methods for external access
  getCashoutProgress(playerId: string): number {
    const player = this.gameState.getPlayer(playerId)
    if (!player || !player.cashoutStartTime) return 0

    const elapsed = Date.now() - player.cashoutStartTime
    const progress = Math.min(elapsed / GAME_CONFIG.CASHOUT_DURATION, 1)
    return progress
  }

  isCashingOut(playerId: string): boolean {
    const player = this.gameState.getPlayer(playerId)
    return player ? !!player.cashoutStartTime : false
  }

  canCashout(playerId: string): boolean {
    const player = this.gameState.getPlayer(playerId)
    return player ? this.canPlayerCashout(player) : false
  }

  forceCancelCashout(playerId: string, reason: string = 'Force cancelled'): void {
    const player = this.gameState.getPlayer(playerId)
    if (!player) return

    this.cancelCashout(player, Date.now(), reason)
  }

  // Get all players currently cashing out
  getCashingOutPlayers(): string[] {
    const world = this.gameState.getWorld()
    const cashingOut: string[] = []

    for (const player of world.players.values()) {
      if (player.cashoutStartTime) {
        cashingOut.push(player.id)
      }
    }

    return cashingOut
  }

  // Calculate estimated cashout value for a player
  async getEstimatedCashoutValue(playerId: string): Promise<number> {
    const player = this.gameState.getPlayer(playerId)
    if (!player || !player.isAlive) return 0

    return await this.calculateFinalPayout(player)
  }

  // Calculate final payout using oracle prices
  private async calculateFinalPayout(player: any): Promise<number> {
    try {
      // Get current SOL price with safety margin
      const currentValueUSD = player.currentValue * 100 // Assuming currentValue is in some game units
      const finalPayoutSOL = await oracleService.usdToSOL(currentValueUSD, 0.02) // 2% safety margin

      return Math.max(0, finalPayoutSOL)
    } catch (error) {
      logger.error(error, `Failed to calculate final payout for player ${player.id}`)
      // Fallback to simple calculation
      return Math.max(0, player.currentValue)
    }
  }

  // Validate cashout transaction (called by Solana integration)
  validateCashoutTransaction(playerId: string, amount: number): boolean {
    const player = this.gameState.getPlayer(playerId)
    if (!player) return false

    // Check if player completed cashout
    if (!player.cashoutStartTime) return false

    const cashoutDuration = Date.now() - player.cashoutStartTime
    if (cashoutDuration < GAME_CONFIG.CASHOUT_DURATION) return false

    // Validate amount is correct
    const expectedAmount = player.currentValue
    const tolerance = expectedAmount * 0.01 // 1% tolerance for price fluctuations

    return Math.abs(amount - expectedAmount) <= tolerance
  }
}
