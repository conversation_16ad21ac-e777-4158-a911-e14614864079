import { Entity, Bounds, QuadTree, QuadTreeNode } from './types.js'

export class QuadTreeImpl implements QuadTree {
  root: QuadTreeNode

  constructor(bounds: Bounds, maxObjects = 10, maxLevels = 5) {
    this.root = {
      bounds,
      entities: [],
      children: [],
      level: 0,
      maxObjects,
      maxLevels,
    }
  }

  insert(entity: Entity): void {
    this.insertIntoNode(this.root, entity)
  }

  remove(entity: Entity): void {
    this.removeFromNode(this.root, entity)
  }

  retrieve(bounds: Bounds): Entity[] {
    const entities: Entity[] = []
    this.retrieveFromNode(this.root, bounds, entities)
    return entities
  }

  clear(): void {
    this.clearNode(this.root)
  }

  update(entity: Entity): void {
    // For simplicity, remove and re-insert
    // In a production system, you might want to optimize this
    this.remove(entity)
    this.insert(entity)
  }

  private insertIntoNode(node: QuadTreeNode, entity: Entity): void {
    // If entity doesn't fit in this node, don't insert
    if (!this.entityFitsInBounds(entity, node.bounds)) {
      return
    }

    // If we have children, try to insert into appropriate child
    if (node.children.length > 0) {
      const childIndex = this.getChildIndex(node, entity)
      if (childIndex !== -1) {
        this.insertIntoNode(node.children[childIndex], entity)
        return
      }
    }

    // Add to this node
    node.entities.push(entity)

    // If we exceed max objects and haven't reached max levels, subdivide
    if (node.entities.length > node.maxObjects && node.level < node.maxLevels) {
      if (node.children.length === 0) {
        this.subdivide(node)
      }

      // Try to move entities to children
      let i = 0
      while (i < node.entities.length) {
        const childIndex = this.getChildIndex(node, node.entities[i])
        if (childIndex !== -1) {
          const entityToMove = node.entities.splice(i, 1)[0]
          this.insertIntoNode(node.children[childIndex], entityToMove)
        } else {
          i++
        }
      }
    }
  }

  private removeFromNode(node: QuadTreeNode, entity: Entity): boolean {
    // Try to remove from this node
    const index = node.entities.findIndex(e => e.id === entity.id)
    if (index !== -1) {
      node.entities.splice(index, 1)
      return true
    }

    // Try to remove from children
    for (const child of node.children) {
      if (this.removeFromNode(child, entity)) {
        return true
      }
    }

    return false
  }

  private retrieveFromNode(node: QuadTreeNode, bounds: Bounds, entities: Entity[]): void {
    // Add entities from this node that intersect with bounds
    for (const entity of node.entities) {
      if (this.boundsIntersect(this.getEntityBounds(entity), bounds)) {
        entities.push(entity)
      }
    }

    // Check children
    for (const child of node.children) {
      if (this.boundsIntersect(child.bounds, bounds)) {
        this.retrieveFromNode(child, bounds, entities)
      }
    }
  }

  private subdivide(node: QuadTreeNode): void {
    const { x, y, width, height } = node.bounds
    const halfWidth = width / 2
    const halfHeight = height / 2

    // Create four children
    node.children = [
      // Top-right
      {
        bounds: { x: x + halfWidth, y, width: halfWidth, height: halfHeight },
        entities: [],
        children: [],
        level: node.level + 1,
        maxObjects: node.maxObjects,
        maxLevels: node.maxLevels,
      },
      // Top-left
      {
        bounds: { x, y, width: halfWidth, height: halfHeight },
        entities: [],
        children: [],
        level: node.level + 1,
        maxObjects: node.maxObjects,
        maxLevels: node.maxLevels,
      },
      // Bottom-left
      {
        bounds: { x, y: y + halfHeight, width: halfWidth, height: halfHeight },
        entities: [],
        children: [],
        level: node.level + 1,
        maxObjects: node.maxObjects,
        maxLevels: node.maxLevels,
      },
      // Bottom-right
      {
        bounds: { x: x + halfWidth, y: y + halfHeight, width: halfWidth, height: halfHeight },
        entities: [],
        children: [],
        level: node.level + 1,
        maxObjects: node.maxObjects,
        maxLevels: node.maxLevels,
      },
    ]
  }

  private getChildIndex(node: QuadTreeNode, entity: Entity): number {
    if (node.children.length === 0) return -1

    const entityBounds = this.getEntityBounds(entity)
    
    // Check which child the entity fits completely within
    for (let i = 0; i < node.children.length; i++) {
      if (this.entityFitsInBounds(entity, node.children[i].bounds)) {
        return i
      }
    }

    return -1 // Entity doesn't fit completely in any child
  }

  private entityFitsInBounds(entity: Entity, bounds: Bounds): boolean {
    const entityBounds = this.getEntityBounds(entity)
    return (
      entityBounds.x >= bounds.x &&
      entityBounds.y >= bounds.y &&
      entityBounds.x + entityBounds.width <= bounds.x + bounds.width &&
      entityBounds.y + entityBounds.height <= bounds.y + bounds.height
    )
  }

  private getEntityBounds(entity: Entity): Bounds {
    return {
      x: entity.position.x - entity.radius,
      y: entity.position.y - entity.radius,
      width: entity.radius * 2,
      height: entity.radius * 2,
    }
  }

  private boundsIntersect(bounds1: Bounds, bounds2: Bounds): boolean {
    return (
      bounds1.x < bounds2.x + bounds2.width &&
      bounds1.x + bounds1.width > bounds2.x &&
      bounds1.y < bounds2.y + bounds2.height &&
      bounds1.y + bounds1.height > bounds2.y
    )
  }

  private clearNode(node: QuadTreeNode): void {
    node.entities = []
    for (const child of node.children) {
      this.clearNode(child)
    }
    node.children = []
  }
}

// Utility functions for quadtree operations
export class QuadTreeUtils {
  static createBounds(x: number, y: number, width: number, height: number): Bounds {
    return { x, y, width, height }
  }

  static expandBounds(bounds: Bounds, margin: number): Bounds {
    return {
      x: bounds.x - margin,
      y: bounds.y - margin,
      width: bounds.width + margin * 2,
      height: bounds.height + margin * 2,
    }
  }

  static getViewBounds(centerX: number, centerY: number, viewWidth: number, viewHeight: number): Bounds {
    return {
      x: centerX - viewWidth / 2,
      y: centerY - viewHeight / 2,
      width: viewWidth,
      height: viewHeight,
    }
  }

  static pointInBounds(x: number, y: number, bounds: Bounds): boolean {
    return x >= bounds.x && x <= bounds.x + bounds.width && y >= bounds.y && y <= bounds.y + bounds.height
  }

  static boundsContainEntity(bounds: Bounds, entity: Entity): boolean {
    return (
      entity.position.x - entity.radius >= bounds.x &&
      entity.position.x + entity.radius <= bounds.x + bounds.width &&
      entity.position.y - entity.radius >= bounds.y &&
      entity.position.y + entity.radius <= bounds.y + bounds.height
    )
  }

  static getEntitiesInRadius(quadTree: QuadTree, centerX: number, centerY: number, radius: number): Entity[] {
    const searchBounds = this.createBounds(centerX - radius, centerY - radius, radius * 2, radius * 2)
    const candidates = quadTree.retrieve(searchBounds)
    
    // Filter by actual distance
    return candidates.filter(entity => {
      const dx = entity.position.x - centerX
      const dy = entity.position.y - centerY
      const distance = Math.sqrt(dx * dx + dy * dy)
      return distance <= radius + entity.radius
    })
  }
}
