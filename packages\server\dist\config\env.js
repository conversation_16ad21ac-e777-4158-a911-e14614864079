import { z } from 'zod';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
// Environment validation schema
const envSchema = z.object({
    // Server Configuration
    NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
    PORT: z.coerce.number().default(8080),
    JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
    CORS_ORIGIN: z.string().url().default('http://localhost:3000'),
    // Database Configuration
    DATABASE_URL: z.string().url('Invalid database URL'),
    REDIS_URL: z.string().url('Invalid Redis URL'),
    // Solana Configuration
    SOLANA_CLUSTER: z.enum(['devnet', 'testnet', 'mainnet-beta']).default('devnet'),
    SOLANA_RPC_URL: z.string().url('Invalid Solana RPC URL'),
    ANCHOR_WALLET: z.string().optional(),
    ANCHOR_PROVIDER_URL: z.string().url('Invalid Anchor provider URL'),
    // Pyth Oracle Configuration
    PYTH_SOL_PRICE_ACCOUNT: z.string().min(32, 'Invalid Pyth price account'),
    // Game Configuration
    PLATFORM_FEE_BPS: z.coerce.number().min(0).max(10000).default(500),
    ORACLE_SAFETY_MARGIN_BPS: z.coerce.number().min(0).max(1000).default(50),
    CASHOUT_HOLD_MS: z.coerce.number().min(1000).max(30000).default(5000),
    MERGE_COOLDOWN_MS: z.coerce.number().min(5000).max(60000).default(20000),
    MAX_CELLS: z.coerce.number().min(1).max(32).default(16),
    TICK_RATE: z.coerce.number().min(10).max(120).default(30),
    // Compliance Configuration
    GEO_GATING_ENABLED: z.coerce.boolean().default(true),
    RESTRICTED_COUNTRIES: z.string().default('US,CN'),
});
// Validate and export environment variables
export const env = envSchema.parse(process.env);
// Export validation function
export const validateEnv = () => env;
// Helper function to check if we're in development
export const isDevelopment = () => env.NODE_ENV === 'development';
export const isProduction = () => env.NODE_ENV === 'production';
export const isTest = () => env.NODE_ENV === 'test';
