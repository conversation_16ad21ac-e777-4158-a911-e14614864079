import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { ClientState, GameSnapshot, GameDelta, Player, PlayerInput, RenderState, ParticleEffect } from '../types/game.js'

interface GameStore extends ClientState {
  // Connection actions
  connect: () => void
  disconnect: () => void
  setConnected: (connected: boolean) => void
  
  // Player actions
  setPlayerId: (playerId: string) => void
  setSessionToken: (token: string) => void
  updateLocalPlayer: (player: Partial<Player>) => void
  
  // Game state actions
  updateGameSnapshot: (snapshot: GameSnapshot) => void
  applyGameDelta: (delta: GameDelta) => void
  updateServerTime: (time: number) => void
  updatePing: (ping: number) => void
  
  // Input actions
  addPendingInput: (input: PlayerInput) => void
  removePendingInput: (sequenceNumber: number) => void
  clearPendingInputs: () => void
  getNextInputSequence: () => number
  
  // UI actions
  setShowHUD: (show: boolean) => void
  setShowMenu: (show: boolean) => void
  setInGame: (inGame: boolean) => void
  
  // Reset
  reset: () => void
}

const initialState: ClientState = {
  isConnected: false,
  currentTick: 0,
  serverTime: 0,
  ping: 0,
  inputSequence: 0,
  pendingInputs: [],
  showHUD: false,
  showMenu: true,
  isInGame: false,
}

export const useGameStore = create<GameStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    // Connection actions
    connect: () => {
      set({ isConnected: true })
    },
    
    disconnect: () => {
      set({ 
        isConnected: false,
        playerId: undefined,
        sessionToken: undefined,
        gameSnapshot: undefined,
        localPlayer: undefined,
        pendingInputs: [],
        isInGame: false,
        showHUD: false,
        showMenu: true
      })
    },
    
    setConnected: (connected: boolean) => {
      set({ isConnected: connected })
    },
    
    // Player actions
    setPlayerId: (playerId: string) => {
      set({ playerId })
    },
    
    setSessionToken: (token: string) => {
      set({ sessionToken: token })
    },
    
    updateLocalPlayer: (player: Partial<Player>) => {
      set(state => ({
        localPlayer: state.localPlayer ? { ...state.localPlayer, ...player } : player as Player
      }))
    },
    
    // Game state actions
    updateGameSnapshot: (snapshot: GameSnapshot) => {
      set({
        gameSnapshot: snapshot,
        currentTick: snapshot.tick,
        serverTime: snapshot.timestamp
      })
    },
    
    applyGameDelta: (delta: GameDelta) => {
      set(state => {
        if (!state.gameSnapshot) return state
        
        // Apply delta to current snapshot
        const updatedSnapshot: GameSnapshot = {
          ...state.gameSnapshot,
          timestamp: delta.timestamp,
          tick: delta.tick,
          
          // Update entities
          players: state.gameSnapshot.players.map(player => {
            const changed = delta.playersChanged.find(p => p.id === player.id)
            return changed ? { ...player, ...changed } : player
          }).filter(player => !delta.playersRemoved.includes(player.id!)),
          
          pellets: state.gameSnapshot.pellets.map(pellet => {
            const changed = delta.pelletsChanged.find(p => p.id === pellet.id)
            return changed ? { ...pellet, ...changed } : pellet
          }).filter(pellet => !delta.pelletsRemoved.includes(pellet.id)),
          
          viruses: state.gameSnapshot.viruses.map(virus => {
            const changed = delta.virusesChanged.find(v => v.id === virus.id)
            return changed ? { ...virus, ...changed } : virus
          }).filter(virus => !delta.virusesRemoved.includes(virus.id)),
          
          ejectedMass: state.gameSnapshot.ejectedMass.map(mass => {
            const changed = delta.ejectedMassChanged.find(m => m.id === mass.id)
            return changed ? { ...mass, ...changed } : mass
          }).filter(mass => !delta.ejectedMassRemoved.includes(mass.id))
        }
        
        // Add new entities
        updatedSnapshot.players.push(...delta.playersChanged.filter(p => 
          !state.gameSnapshot!.players.find(existing => existing.id === p.id)
        ))
        updatedSnapshot.pellets.push(...delta.pelletsChanged.filter(p => 
          !state.gameSnapshot!.pellets.find(existing => existing.id === p.id)
        ))
        updatedSnapshot.viruses.push(...delta.virusesChanged.filter(v => 
          !state.gameSnapshot!.viruses.find(existing => existing.id === v.id)
        ))
        updatedSnapshot.ejectedMass.push(...delta.ejectedMassChanged.filter(m => 
          !state.gameSnapshot!.ejectedMass.find(existing => existing.id === m.id)
        ))
        
        return {
          gameSnapshot: updatedSnapshot,
          currentTick: delta.tick,
          serverTime: delta.timestamp
        }
      })
    },
    
    updateServerTime: (time: number) => {
      set({ serverTime: time })
    },
    
    updatePing: (ping: number) => {
      set({ ping })
    },
    
    // Input actions
    addPendingInput: (input: PlayerInput) => {
      set(state => ({
        pendingInputs: [...state.pendingInputs, input]
      }))
    },
    
    removePendingInput: (sequenceNumber: number) => {
      set(state => ({
        pendingInputs: state.pendingInputs.filter(input => input.sequenceNumber !== sequenceNumber)
      }))
    },
    
    clearPendingInputs: () => {
      set({ pendingInputs: [] })
    },
    
    getNextInputSequence: () => {
      const current = get().inputSequence
      set({ inputSequence: current + 1 })
      return current + 1
    },
    
    // UI actions
    setShowHUD: (show: boolean) => {
      set({ showHUD: show })
    },
    
    setShowMenu: (show: boolean) => {
      set({ showMenu: show })
    },
    
    setInGame: (inGame: boolean) => {
      set({ isInGame: inGame })
    },
    
    // Reset
    reset: () => {
      set(initialState)
    }
  }))
)

// Render store for visual state
interface RenderStore extends RenderState {
  // Camera actions
  setCameraPosition: (x: number, y: number) => void
  setCameraZoom: (zoom: number) => void
  setTargetZoom: (zoom: number) => void
  updateCamera: (deltaTime: number) => void
  
  // Viewport actions
  setViewportSize: (width: number, height: number) => void
  
  // Entity actions
  updateEntities: (snapshot: GameSnapshot) => void
  
  // Effects actions
  addParticleEffect: (effect: ParticleEffect) => void
  updateParticleEffects: (deltaTime: number) => void
  clearParticleEffects: () => void
}

const initialRenderState: RenderState = {
  camera: {
    x: 0,
    y: 0,
    zoom: 1,
    targetZoom: 1
  },
  viewport: {
    width: window.innerWidth,
    height: window.innerHeight
  },
  entities: {
    players: new Map(),
    pellets: new Map(),
    viruses: new Map(),
    ejectedMass: new Map()
  },
  effects: []
}

export const useRenderStore = create<RenderStore>()((set, get) => ({
  ...initialRenderState,
  
  // Camera actions
  setCameraPosition: (x: number, y: number) => {
    set(state => ({
      camera: { ...state.camera, x, y }
    }))
  },
  
  setCameraZoom: (zoom: number) => {
    set(state => ({
      camera: { ...state.camera, zoom }
    }))
  },
  
  setTargetZoom: (zoom: number) => {
    set(state => ({
      camera: { ...state.camera, targetZoom: zoom }
    }))
  },
  
  updateCamera: (deltaTime: number) => {
    set(state => {
      const { camera } = state
      const zoomSpeed = 5 // zoom units per second
      const zoomDiff = camera.targetZoom - camera.zoom
      const newZoom = Math.abs(zoomDiff) < 0.01 
        ? camera.targetZoom 
        : camera.zoom + zoomDiff * zoomSpeed * deltaTime
      
      return {
        camera: { ...camera, zoom: newZoom }
      }
    })
  },
  
  // Viewport actions
  setViewportSize: (width: number, height: number) => {
    set({ viewport: { width, height } })
  },
  
  // Entity actions
  updateEntities: (snapshot: GameSnapshot) => {
    set(state => {
      const entities = { ...state.entities }
      
      // Update players
      entities.players.clear()
      snapshot.players.forEach(player => {
        if (player.cells) {
          entities.players.set(player.id!, player.cells)
        }
      })
      
      // Update pellets
      entities.pellets.clear()
      snapshot.pellets.forEach(pellet => {
        entities.pellets.set(pellet.id, pellet)
      })
      
      // Update viruses
      entities.viruses.clear()
      snapshot.viruses.forEach(virus => {
        entities.viruses.set(virus.id, virus)
      })
      
      // Update ejected mass
      entities.ejectedMass.clear()
      snapshot.ejectedMass.forEach(mass => {
        entities.ejectedMass.set(mass.id, mass)
      })
      
      return { entities }
    })
  },
  
  // Effects actions
  addParticleEffect: (effect: ParticleEffect) => {
    set(state => ({
      effects: [...state.effects, effect]
    }))
  },
  
  updateParticleEffects: (deltaTime: number) => {
    set(state => ({
      effects: state.effects
        .map(effect => ({
          ...effect,
          life: effect.life - deltaTime
        }))
        .filter(effect => effect.life > 0)
    }))
  },
  
  clearParticleEffects: () => {
    set({ effects: [] })
  }
}))
