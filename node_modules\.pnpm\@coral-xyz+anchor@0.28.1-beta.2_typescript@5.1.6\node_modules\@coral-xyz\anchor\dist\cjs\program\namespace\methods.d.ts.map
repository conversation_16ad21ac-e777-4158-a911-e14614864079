{"version": 3, "file": "methods.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/methods.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EACX,cAAc,EACd,SAAS,EACT,MAAM,EACN,WAAW,EACX,sBAAsB,EACtB,oBAAoB,EACrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC5E,OAAO,QAAQ,MAAM,mBAAmB,CAAC;AACzC,OAAO,EACL,eAAe,EAEf,qBAAqB,EACtB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,OAAO,EAAoB,MAAM,cAAc,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EACL,eAAe,EACf,2BAA2B,EAC3B,oBAAoB,EACpB,SAAS,EACV,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AAEpC,MAAM,MAAM,gBAAgB,CAC1B,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,IACnD,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAEjC,qBAAa,qBAAqB;WAClB,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC,EACjE,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,EAC3B,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,EACxB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,EACxB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EACjB,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,EAC/B,gBAAgB,EAAE,gBAAgB,CAAC,GAAG,CAAC,EACvC,QAAQ,EAAE,UAAU,EAAE,EACtB,cAAc,CAAC,EAAE,qBAAqB,CAAC,GAAG,CAAC,GAC1C,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAiB7C;AAED,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,cAAc,GAAG,cAAc,IACnE,OAAO,CAAC;KACL,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;QAAE,IAAI,EAAE,CAAC,CAAA;KAAE,CAAC;CAClD,CAAC,CAAC;AAEL,KAAK,cAAc,CAAC,CAAC,SAAS,cAAc,IAAI,CAAC,SAAS,WAAW,GACjE,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GACtC,CAAC,SAAS;IAAE,UAAU,EAAE,IAAI,CAAA;CAAE,GAC9B,OAAO,GAAG,IAAI,GACd,OAAO,CAAC;AAEZ,wBAAgB,iBAAiB,CAC/B,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,GAC7C,cAAc,IAAI,eAAe,CAMnC;AAED,wBAAgB,sBAAsB,CAAC,CAAC,SAAS,cAAc,EAC7D,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,EACnC,WAAW,EAAE,OAAO,GACnB,eAAe,CAgBjB;AAED,qBAAa,cAAc,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,SAAS,eAAe,CAAC,GAAG,CAAC;IAYvE,OAAO,CAAC,KAAK;IACb,OAAO,CAAC,KAAK;IACb,OAAO,CAAC,MAAM;IACd,OAAO,CAAC,WAAW;IACnB,OAAO,CAAC,OAAO;IAEf,OAAO,CAAC,UAAU;IAjBpB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAuB;IACjD,OAAO,CAAC,kBAAkB,CAA0B;IACpD,OAAO,CAAC,QAAQ,CAAqB;IACrC,OAAO,CAAC,gBAAgB,CAAqC;IAC7D,OAAO,CAAC,iBAAiB,CAAqC;IAC9D,OAAO,CAAC,iBAAiB,CAAwB;IACjD,OAAO,CAAC,oBAAoB,CAAiB;IAC7C,OAAO,CAAC,KAAK,CAAa;gBAGxB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EACT,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,EACzB,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,EACzB,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,EAClB,WAAW,EAAE,UAAU,CAAC,GAAG,CAAC,EAC5B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,EACxC,SAAS,EAAE,QAAQ,EACX,UAAU,EAAE,SAAS,EAC7B,MAAM,EAAE,eAAe,CAAC,GAAG,CAAC,EAC5B,iBAAiB,EAAE,gBAAgB,CAAC,GAAG,CAAC,EACxC,SAAS,EAAE,UAAU,EAAE,EACvB,eAAe,CAAC,EAAE,qBAAqB,CAAC,GAAG,CAAC;IAevC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI;IAKvB,OAAO,IAAI,OAAO,CAC7B,OAAO,CAAC,2BAA2B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC7C;IASM,QAAQ,CACb,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAC/C,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAMlB,cAAc,CACnB,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GACxC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAMlB,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAKvD,iBAAiB,CACtB,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,GAC3B,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAKlB,eAAe,CACpB,GAAG,EAAE,KAAK,CAAC,sBAAsB,CAAC,GACjC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAKlB,gBAAgB,CACrB,GAAG,EAAE,KAAK,CAAC,sBAAsB,CAAC,GACjC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;IAKZ,GAAG,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAgB5D,UAAU,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC;QACzD,OAAO,EAAE,OAAO,CAAC,2BAA2B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACtD,SAAS,EAAE,oBAAoB,CAAC;KACjC,CAAC;IAQW,IAAI,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;IAoB5C,QAAQ,CACnB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAgBzB,WAAW,IAAI,OAAO,CAAC,sBAAsB,CAAC;IAe3D;;;OAGG;IACU,OAAO,IAAI,OAAO,CAAC;QAC9B,OAAO,EAAE,OAAO,CAAC,2BAA2B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACtD,WAAW,EAAE,sBAAsB,CAAC;QACpC,OAAO,EAAE,MAAM,EAAE,CAAC;KACnB,CAAC;IAQW,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC;CAcjD"}