import { Connection, PublicKey } from '@solana/web3.js'
import { PriceServiceConnection } from '@pythnetwork/price-service-client'
import { solanaClient } from './client.js'
import { validateEnv } from '../config/env.js'
import pino from 'pino'

const logger = pino({ name: 'oracle-service' })
const env = validateEnv()

export class OracleService {
  private priceService: PriceServiceConnection
  private priceCache = new Map<string, CachedPrice>()
  private updateInterval: NodeJS.Timeout | null = null
  private circuitBreaker: CircuitBreaker
  private isInitialized = false

  // Pyth price feed IDs
  private readonly PRICE_FEEDS = {
    'SOL/USD': '0xef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d', // Mainnet SOL/USD
    'BTC/USD': '0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43', // Mainnet BTC/USD
    'ETH/USD': '0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace', // Mainnet ETH/USD
  }

  constructor() {
    this.priceService = new PriceServiceConnection('https://hermes.pyth.network', {
      logger: logger.child({ component: 'pyth-client' }),
    })
    
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      recoveryTimeout: 30000,
      monitorTimeout: 5000,
    })
  }

  async initialize(): Promise<void> {
    try {
      // Start price updates
      await this.startPriceUpdates()
      
      // Initialize circuit breaker
      this.circuitBreaker.initialize()
      
      this.isInitialized = true
      logger.info('Oracle service initialized successfully')
      
    } catch (error) {
      logger.error(error, 'Failed to initialize oracle service')
      throw error
    }
  }

  private async startPriceUpdates(): Promise<void> {
    // Update prices every 5 seconds
    this.updateInterval = setInterval(async () => {
      await this.updatePrices()
    }, 5000)

    // Initial price fetch
    await this.updatePrices()
  }

  private async updatePrices(): Promise<void> {
    if (!this.circuitBreaker.canExecute()) {
      logger.warn('Circuit breaker is open, skipping price update')
      return
    }

    try {
      const priceFeeds = Object.values(this.PRICE_FEEDS)
      const priceUpdates = await this.priceService.getLatestPriceFeeds(priceFeeds)

      for (const priceUpdate of priceUpdates || []) {
        const symbol = this.getPriceSymbol(priceUpdate.id)
        if (symbol) {
          const price = this.parsePriceData(priceUpdate)
          if (price) {
            this.priceCache.set(symbol, {
              price: price.price,
              confidence: price.confidence,
              timestamp: Date.now(),
              publishTime: price.publishTime,
            })
          }
        }
      }

      this.circuitBreaker.recordSuccess()
      logger.debug(`Updated ${priceUpdates?.length || 0} price feeds`)

    } catch (error) {
      this.circuitBreaker.recordFailure()
      logger.error(error, 'Failed to update prices')
    }
  }

  private getPriceSymbol(feedId: string): string | null {
    for (const [symbol, id] of Object.entries(this.PRICE_FEEDS)) {
      if (id === feedId) {
        return symbol
      }
    }
    return null
  }

  private parsePriceData(priceData: any): ParsedPrice | null {
    try {
      if (!priceData.price) return null

      return {
        price: Number(priceData.price.price) * Math.pow(10, priceData.price.expo),
        confidence: Number(priceData.price.conf) * Math.pow(10, priceData.price.expo),
        publishTime: Number(priceData.price.publish_time) * 1000, // Convert to milliseconds
      }
    } catch (error) {
      logger.error(error, 'Failed to parse price data')
      return null
    }
  }

  // Get current SOL/USD price
  async getSOLPrice(): Promise<PriceData> {
    const cached = this.priceCache.get('SOL/USD')
    
    if (!cached) {
      throw new Error('SOL price not available')
    }

    // Check if price is stale (older than 30 seconds)
    const age = Date.now() - cached.timestamp
    if (age > 30000) {
      logger.warn(`SOL price is stale: ${age}ms old`)
    }

    return {
      price: cached.price,
      confidence: cached.confidence,
      timestamp: cached.timestamp,
      publishTime: cached.publishTime,
      isStale: age > 30000,
    }
  }

  // Convert SOL to USD with safety margins
  async solToUSD(solAmount: number, safetyMargin: number = 0.02): Promise<number> {
    const priceData = await this.getSOLPrice()
    
    // Apply safety margin (reduce price by margin percentage)
    const safePrice = priceData.price * (1 - safetyMargin)
    
    return solAmount * safePrice
  }

  // Convert USD to SOL with safety margins
  async usdToSOL(usdAmount: number, safetyMargin: number = 0.02): Promise<number> {
    const priceData = await this.getSOLPrice()
    
    // Apply safety margin (increase price by margin percentage)
    const safePrice = priceData.price * (1 + safetyMargin)
    
    return usdAmount / safePrice
  }

  // Get price with confidence interval
  async getSOLPriceWithConfidence(): Promise<PriceWithConfidence> {
    const priceData = await this.getSOLPrice()
    
    return {
      price: priceData.price,
      lowerBound: priceData.price - priceData.confidence,
      upperBound: priceData.price + priceData.confidence,
      confidence: priceData.confidence,
      confidencePercent: (priceData.confidence / priceData.price) * 100,
      timestamp: priceData.timestamp,
      isStale: priceData.isStale,
    }
  }

  // Validate price is within acceptable bounds
  validatePrice(price: number, expectedRange: { min: number; max: number }): boolean {
    return price >= expectedRange.min && price <= expectedRange.max
  }

  // Get circuit breaker status
  getCircuitBreakerStatus(): {
    isOpen: boolean
    failureCount: number
    lastFailure: number | null
    nextRetry: number | null
  } {
    return this.circuitBreaker.getStatus()
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const solPrice = await this.getSOLPrice()
      return !solPrice.isStale && this.circuitBreaker.canExecute()
    } catch (error) {
      logger.error(error, 'Oracle health check failed')
      return false
    }
  }

  // Cleanup
  async cleanup(): Promise<void> {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    
    this.priceCache.clear()
    logger.info('Oracle service cleanup completed')
  }
}

// Circuit breaker implementation
class CircuitBreaker {
  private failureCount = 0
  private lastFailureTime: number | null = null
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  private config: CircuitBreakerConfig

  constructor(config: CircuitBreakerConfig) {
    this.config = config
  }

  initialize(): void {
    // Monitor circuit breaker state
    setInterval(() => {
      this.checkState()
    }, this.config.monitorTimeout)
  }

  canExecute(): boolean {
    return this.state !== 'OPEN'
  }

  recordSuccess(): void {
    this.failureCount = 0
    this.lastFailureTime = null
    this.state = 'CLOSED'
  }

  recordFailure(): void {
    this.failureCount++
    this.lastFailureTime = Date.now()
    
    if (this.failureCount >= this.config.failureThreshold) {
      this.state = 'OPEN'
      logger.warn(`Circuit breaker opened after ${this.failureCount} failures`)
    }
  }

  private checkState(): void {
    if (this.state === 'OPEN' && this.lastFailureTime) {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime
      
      if (timeSinceLastFailure >= this.config.recoveryTimeout) {
        this.state = 'HALF_OPEN'
        logger.info('Circuit breaker moved to half-open state')
      }
    }
  }

  getStatus(): {
    isOpen: boolean
    failureCount: number
    lastFailure: number | null
    nextRetry: number | null
  } {
    const nextRetry = this.lastFailureTime 
      ? this.lastFailureTime + this.config.recoveryTimeout
      : null

    return {
      isOpen: this.state === 'OPEN',
      failureCount: this.failureCount,
      lastFailure: this.lastFailureTime,
      nextRetry,
    }
  }
}

// Types
interface CachedPrice {
  price: number
  confidence: number
  timestamp: number
  publishTime: number
}

interface ParsedPrice {
  price: number
  confidence: number
  publishTime: number
}

interface PriceData {
  price: number
  confidence: number
  timestamp: number
  publishTime: number
  isStale: boolean
}

interface PriceWithConfidence extends PriceData {
  lowerBound: number
  upperBound: number
  confidencePercent: number
}

interface CircuitBreakerConfig {
  failureThreshold: number
  recoveryTimeout: number
  monitorTimeout: number
}

// Singleton instance
export const oracleService = new OracleService()
