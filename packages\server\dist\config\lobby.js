import { env } from './env';
// Default lobby tiers (SOL amounts will be calculated dynamically)
export const LOBBY_TIERS = [
    {
        id: 1,
        name: 'Micro Stakes',
        usdAmount: 1.0,
        minWagerLamports: 0n,
        maxPlayers: 100,
        description: '$1 USD equivalent wager',
    },
    {
        id: 2,
        name: 'Low Stakes',
        usdAmount: 10.0,
        minWagerLamports: 0n,
        maxPlayers: 50,
        description: '$10 USD equivalent wager',
    },
];
// Game physics constants
export const GAME_CONFIG = {
    // World settings
    WORLD_WIDTH: 5000,
    WORLD_HEIGHT: 5000,
    WORLD_BORDER_BUFFER: 100,
    // Player settings
    BASE_MASS: 10,
    MIN_MASS: 10,
    MAX_MASS: 22500,
    MASS_TO_RADIUS_FACTOR: 1.0,
    // Movement settings
    BASE_SPEED: 100,
    SPEED_MASS_EXPONENT: 0.449,
    MIN_SPEED: 10,
    // Split mechanics
    SPLIT_MASS_RATIO: 0.5,
    SPLIT_VELOCITY: 150,
    SPLIT_DISTANCE: 50,
    MERGE_COOLDOWN_MS: env.MERGE_COOLDOWN_MS,
    MAX_CELLS_PER_PLAYER: env.MAX_CELLS,
    // Eject mechanics
    EJECT_MASS: 12,
    EJECT_VELOCITY: 200,
    EJECT_COOLDOWN_MS: 100,
    // Decay settings
    DECAY_RATE: 0.002,
    DECAY_MIN_MASS: 50,
    // Pellet settings
    PELLET_MASS: 1,
    PELLET_COUNT: 1000,
    PELLET_RESPAWN_RATE: 10,
    // Virus settings
    VIRUS_MASS: 100,
    VIRUS_COUNT: 50,
    VIRUS_SPLIT_THRESHOLD: 120,
    VIRUS_FEED_THRESHOLD: 7,
    VIRUS_POP_MASS_BONUS: 50,
    // Collision settings
    CONSUME_MASS_RATIO: 1.25,
    QUADTREE_MAX_OBJECTS: 10,
    QUADTREE_MAX_LEVELS: 5,
    // Network settings
    TICK_RATE: env.TICK_RATE,
    SNAPSHOT_RATE: 20,
    DELTA_RATE: 60,
    // Anti-cheat settings
    MAX_INPUT_RATE: 60,
    INPUT_VALIDATION_WINDOW_MS: 1000,
    MAX_VELOCITY_MULTIPLIER: 2.0,
    // Input reconciliation settings
    MAX_INPUT_AGE: 5000,
    MIN_INPUT_INTERVAL: 25,
    POSITION_TOLERANCE: 10,
    // Cash-out settings
    CASHOUT_HOLD_MS: env.CASHOUT_HOLD_MS,
    CASHOUT_MIN_MASS: 15,
    CASHOUT_DURATION: 5000,
    CASHOUT_MOVEMENT_TOLERANCE: 50,
    MIN_GAME_TIME_FOR_CASHOUT: 30000, // 30 seconds minimum game time before cashout
};
// Wager to mass conversion
export const WAGER_CONFIG = {
    // Base mass given for minimum wager
    BASE_MASS_PER_USD: 10,
    // Oracle price safety margin
    ORACLE_SAFETY_MARGIN_BPS: env.ORACLE_SAFETY_MARGIN_BPS,
    // Platform fee
    PLATFORM_FEE_BPS: env.PLATFORM_FEE_BPS,
    // Mass to SOL conversion factor (calculated dynamically)
    MASS_TO_SOL_FACTOR: 0.0001, // Will be updated based on oracle price
};
// Rate limiting configuration
export const RATE_LIMITS = {
    // Per-socket limits
    MESSAGES_PER_SECOND: 60,
    BURST_LIMIT: 10,
    // Per-IP limits
    CONNECTIONS_PER_IP: 5,
    // Input-specific limits
    MOVEMENT_INPUTS_PER_SECOND: 30,
    ACTION_INPUTS_PER_SECOND: 10, // Split, eject, cashout
};
// Spatial partitioning configuration
export const SPATIAL_CONFIG = {
    QUADTREE_BOUNDS: {
        x: 0,
        y: 0,
        width: GAME_CONFIG.WORLD_WIDTH,
        height: GAME_CONFIG.WORLD_HEIGHT,
    },
    MAX_OBJECTS_PER_NODE: GAME_CONFIG.QUADTREE_MAX_OBJECTS,
    MAX_LEVELS: GAME_CONFIG.QUADTREE_MAX_LEVELS,
};
