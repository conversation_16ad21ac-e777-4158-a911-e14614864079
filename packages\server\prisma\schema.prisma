// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User account linked to Solana wallet
model User {
  id        String   @id @default(cuid())
  wallet    String   @unique // Solana wallet public key
  username  String?  // Optional display name
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Compliance and geo-gating
  ipAddress    String?
  countryCode  String?
  isRestricted Boolean @default(false)
  kycStatus    String  @default("none") // none, pending, approved, rejected

  // Game statistics
  totalDeposited  Decimal @default(0) @db.Decimal(20, 9) // SOL amount
  totalWithdrawn  Decimal @default(0) @db.Decimal(20, 9) // SOL amount
  totalWagered    Decimal @default(0) @db.Decimal(20, 9) // SOL amount
  totalWinnings   Decimal @default(0) @db.Decimal(20, 9) // SOL amount
  gamesPlayed     Int     @default(0)
  highestMass     Int     @default(0)
  longestSession  Int     @default(0) // seconds

  // Relationships
  sessions     GameSession[]
  transactions Transaction[]
  auditLogs    AuditLog[]

  @@index([wallet])
  @@index([createdAt])
  @@index([isRestricted])
  @@map("users")
}

// Active game session
model GameSession {
  id        String   @id @default(cuid())
  userId    String
  lobbyTier Int      // 1 = $1, 2 = $10, etc.
  
  // Session details
  sessionToken String   @unique // Server-issued session token
  socketId     String?  // Current socket connection ID
  startedAt    DateTime @default(now())
  endedAt      DateTime?
  
  // Wager and game state
  wagerAmount    Decimal @db.Decimal(20, 9) // SOL amount wagered
  initialMass    Int     // Starting mass
  currentMass    Int     // Current mass (updated in real-time)
  currentValue   Decimal @db.Decimal(20, 9) // Current SOL value
  
  // Game position and state
  positionX Float?
  positionY Float?
  cellCount Int   @default(1)
  
  // Session outcome
  status        String  @default("active") // active, eliminated, cashed_out, disconnected
  eliminatedBy  String? // User ID who eliminated this player
  cashoutAmount Decimal? @db.Decimal(20, 9) // Final cashout amount
  
  // Anti-cheat and validation
  lastInputAt    DateTime?
  inputCount     Int       @default(0)
  suspiciousFlag Boolean   @default(false)
  
  // Relationships
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  auditLogs    AuditLog[]

  @@index([userId])
  @@index([lobbyTier])
  @@index([status])
  @@index([startedAt])
  @@index([sessionToken])
  @@map("game_sessions")
}

// Transaction log for all SOL movements
model Transaction {
  id        String   @id @default(cuid())
  userId    String
  sessionId String?
  
  // Transaction details
  type           String  // deposit, wager, cashout, withdraw, fee
  amount         Decimal @db.Decimal(20, 9) // SOL amount
  feeAmount      Decimal @default(0) @db.Decimal(20, 9) // Platform fee
  
  // Solana transaction details
  signature      String? @unique // Solana transaction signature
  blockHeight    BigInt? // Block height when confirmed
  confirmations  Int     @default(0)
  
  // Status and timing
  status    String   @default("pending") // pending, confirmed, failed
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Oracle price at time of transaction
  solPriceUsd Decimal? @db.Decimal(10, 6) // SOL price in USD
  
  // Relationships
  user    User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  session GameSession? @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([signature])
  @@index([createdAt])
  @@map("transactions")
}

// Audit log for important game events
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  sessionId String?
  
  // Event details
  eventType String   // elimination, cashout, suspicious_activity, admin_action
  eventData Json     // Flexible JSON data for event details
  severity  String   @default("info") // info, warning, error, critical
  
  // Context
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())
  
  // Relationships
  user    User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
  session GameSession? @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@index([eventType])
  @@index([severity])
  @@index([timestamp])
  @@index([userId])
  @@map("audit_logs")
}

// System configuration and feature flags
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String @default("string") // string, number, boolean, json
  
  description String?
  updatedAt   DateTime @updatedAt
  updatedBy   String? // Admin user who made the change

  @@index([key])
  @@map("system_config")
}

// Lobby state and statistics
model LobbyState {
  id       Int @id // Lobby tier ID
  
  // Current state
  activePlayers    Int     @default(0)
  totalPoolValue   Decimal @default(0) @db.Decimal(20, 9) // Total SOL in lobby
  averageMass      Float   @default(0)
  
  // Statistics
  totalSessions    Int     @default(0)
  totalEliminations Int    @default(0)
  totalCashouts    Int     @default(0)
  
  // Timing
  lastActivity DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("lobby_states")
}

// Price oracle history for auditing
model PriceHistory {
  id        String   @id @default(cuid())
  
  // Price data
  solPriceUsd   Decimal @db.Decimal(10, 6)
  confidence    Decimal @db.Decimal(10, 6) // Pyth confidence interval
  publishTime   DateTime
  
  // Oracle metadata
  oracleAccount String // Pyth price account
  slot          BigInt // Solana slot
  
  // System metadata
  createdAt DateTime @default(now())

  @@index([publishTime])
  @@index([createdAt])
  @@map("price_history")
}


