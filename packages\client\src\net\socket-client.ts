import { io, Socket } from 'socket.io-client'
import { 
  Player<PERSON>n<PERSON>, 
  GameS<PERSON>shot, 
  GameDelta, 
  GameEvent, 
  SocketEvents,
  JoinGameData,
  ClientStateData
} from '../types/game.js'
import { useGameStore } from '../store/game-store.js'

export interface SocketConfig {
  url: string
  autoConnect: boolean
  reconnection: boolean
  reconnectionAttempts: number
  reconnectionDelay: number
  timeout: number
}

export interface ConnectionState {
  connected: boolean
  connecting: boolean
  error: string | null
  ping: number
  lastPingTime: number
}

export class SocketClient {
  private socket: Socket | null = null
  private config: SocketConfig
  private state: ConnectionState
  private pingInterval: number = 0
  private reconnectAttempts: number = 0
  
  // Event handlers
  private eventHandlers: Map<string, Function[]> = new Map()
  
  constructor(config: Partial<SocketConfig> = {}) {
    this.config = {
      url: 'http://localhost:3001',
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000,
      ...config
    }
    
    this.state = {
      connected: false,
      connecting: false,
      error: null,
      ping: 0,
      lastPingTime: 0
    }
  }
  
  /**
   * Connect to the server
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) return
    
    this.state.connecting = true
    this.state.error = null
    
    try {
      this.socket = io(this.config.url, {
        autoConnect: this.config.autoConnect,
        reconnection: this.config.reconnection,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
        timeout: this.config.timeout,
        transports: ['websocket', 'polling']
      })
      
      this.setupSocketHandlers()
      
      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'))
        }, this.config.timeout)
        
        this.socket!.on('connect', () => {
          clearTimeout(timeout)
          resolve()
        })
        
        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
      
      this.state.connected = true
      this.state.connecting = false
      this.reconnectAttempts = 0
      
      // Start ping monitoring
      this.startPingMonitoring()
      
      // Update game store
      const gameStore = useGameStore.getState()
      gameStore.setConnected(true)
      
    } catch (error) {
      this.state.connecting = false
      this.state.error = error instanceof Error ? error.message : 'Connection failed'
      throw error
    }
  }
  
  /**
   * Disconnect from the server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    
    this.stopPingMonitoring()
    this.state.connected = false
    this.state.connecting = false
    
    // Update game store
    const gameStore = useGameStore.getState()
    gameStore.disconnect()
  }
  
  private setupSocketHandlers(): void {
    if (!this.socket) return
    
    // Connection events
    this.socket.on('connect', this.handleConnect)
    this.socket.on('disconnect', this.handleDisconnect)
    this.socket.on('connect_error', this.handleConnectError)
    this.socket.on('reconnect', this.handleReconnect)
    this.socket.on('reconnect_error', this.handleReconnectError)
    this.socket.on('reconnect_failed', this.handleReconnectFailed)
    
    // Game events
    this.socket.on('game_snapshot', this.handleGameSnapshot)
    this.socket.on('game_delta', this.handleGameDelta)
    this.socket.on('player_joined', this.handlePlayerJoined)
    this.socket.on('player_left', this.handlePlayerLeft)
    this.socket.on('game_event', this.handleGameEvent)
    this.socket.on('error', this.handleError)
    this.socket.on('pong', this.handlePong)
  }
  
  private handleConnect = (): void => {
    console.log('Connected to server')
    this.state.connected = true
    this.state.connecting = false
    this.state.error = null
    
    const gameStore = useGameStore.getState()
    gameStore.setConnected(true)
    
    this.emit('custom_event', 'connected')
  }
  
  private handleDisconnect = (reason: string): void => {
    console.log('Disconnected from server:', reason)
    this.state.connected = false
    
    const gameStore = useGameStore.getState()
    gameStore.setConnected(false)
    
    this.emit('custom_event', 'disconnected', reason)
  }
  
  private handleConnectError = (error: Error): void => {
    console.error('Connection error:', error)
    this.state.error = error.message
    this.state.connecting = false
    
    this.emit('custom_event', 'connect_error', error)
  }
  
  private handleReconnect = (attemptNumber: number): void => {
    console.log('Reconnected after', attemptNumber, 'attempts')
    this.reconnectAttempts = 0
    
    this.emit('custom_event', 'reconnected', attemptNumber)
  }
  
  private handleReconnectError = (error: Error): void => {
    this.reconnectAttempts++
    console.error(`Reconnect attempt ${this.reconnectAttempts} failed:`, error)
    
    this.emit('custom_event', 'reconnect_error', error, this.reconnectAttempts)
  }
  
  private handleReconnectFailed = (): void => {
    console.error('Failed to reconnect after maximum attempts')
    this.state.error = 'Failed to reconnect'
    
    this.emit('custom_event', 'reconnect_failed')
  }
  
  private handleGameSnapshot = (snapshot: GameSnapshot): void => {
    const gameStore = useGameStore.getState()
    gameStore.updateGameSnapshot(snapshot)
    
    this.emit('game_snapshot', snapshot)
  }
  
  private handleGameDelta = (delta: GameDelta): void => {
    const gameStore = useGameStore.getState()
    gameStore.applyGameDelta(delta)
    
    this.emit('game_delta', delta)
  }
  
  private handlePlayerJoined = (player: any): void => {
    console.log('Player joined:', player)
    this.emit('player_joined', player)
  }
  
  private handlePlayerLeft = (playerId: string): void => {
    console.log('Player left:', playerId)
    this.emit('player_left', playerId)
  }
  
  private handleGameEvent = (event: GameEvent): void => {
    console.log('Game event:', event)
    this.emit('game_event', event)
  }
  
  private handleError = (error: { message: string; code?: string }): void => {
    console.error('Server error:', error)
    this.state.error = error.message
    
    this.emit('error', error)
  }
  
  private handlePong = (data: { timestamp: number }): void => {
    const now = Date.now()
    this.state.ping = now - this.state.lastPingTime
    
    const gameStore = useGameStore.getState()
    gameStore.updatePing(this.state.ping)
    gameStore.updateServerTime(data.timestamp)
  }
  
  private startPingMonitoring(): void {
    this.pingInterval = window.setInterval(() => {
      if (this.socket?.connected) {
        this.state.lastPingTime = Date.now()
        this.socket.emit('ping')
      }
    }, 5000) // Ping every 5 seconds
  }
  
  private stopPingMonitoring(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = 0
    }
  }
  
  /**
   * Join a game lobby
   */
  joinGame(data: JoinGameData): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server')
    }
    
    this.socket.emit('join_game', data)
  }
  
  /**
   * Send player input
   */
  sendInput(input: PlayerInput): void {
    if (!this.socket?.connected) return
    
    this.socket.emit('player_input', input)
  }
  
  /**
   * Send client state for reconciliation
   */
  sendClientState(state: ClientStateData): void {
    if (!this.socket?.connected) return
    
    this.socket.emit('client_state', state)
  }
  
  /**
   * Leave the current game
   */
  leaveGame(): void {
    if (!this.socket?.connected) return
    
    this.socket.emit('leave_game')
  }
  
  /**
   * Add event listener
   */
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }
  
  /**
   * Remove event listener
   */
  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }
  
  /**
   * Emit custom event to listeners
   */
  private emit(event: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error('Error in event handler:', error)
        }
      })
    }
  }
  
  /**
   * Get connection state
   */
  getState(): ConnectionState {
    return { ...this.state }
  }
  
  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.state.connected
  }
  
  /**
   * Get current ping
   */
  getPing(): number {
    return this.state.ping
  }
  
  /**
   * Update configuration
   */
  updateConfig(config: Partial<SocketConfig>): void {
    this.config = { ...this.config, ...config }
  }
}
