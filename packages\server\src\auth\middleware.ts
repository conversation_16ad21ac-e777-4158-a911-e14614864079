import { Request, Response, NextFunction } from 'express'
import { Socket } from 'socket.io'
import { walletAuthService } from './wallet-auth.js'
import { ipRateLimiter, antiSpam } from '../network/rate-limiter.js'
import pino from 'pino'

const logger = pino({ name: 'auth-middleware' })

// Express middleware for JWT authentication
export function authenticateJWT(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    res.status(401).json({ error: 'Access token required' })
    return
  }

  const validation = walletAuthService.validateJWT(token)
  
  if (!validation.isValid) {
    res.status(403).json({ error: validation.error || 'Invalid token' })
    return
  }

  // Add user info to request
  ;(req as any).user = validation.payload
  next()
}

// Express middleware for session token validation
export function authenticateSession(req: Request, res: Response, next: NextFunction): void {
  const sessionToken = req.headers['x-session-token'] as string

  if (!sessionToken) {
    res.status(401).json({ error: 'Session token required' })
    return
  }

  const validation = walletAuthService.validateSessionToken(sessionToken)
  
  if (!validation.isValid) {
    res.status(403).json({ error: validation.error || 'Invalid session' })
    return
  }

  // Add session info to request
  ;(req as any).session = validation.sessionData
  next()
}

// Socket.IO middleware for authentication
export function authenticateSocket(socket: Socket, next: (err?: Error) => void): void {
  const token = socket.handshake.auth.token
  const sessionToken = socket.handshake.auth.sessionToken

  // Check IP rate limiting first
  const ip = socket.handshake.address
  if (!ipRateLimiter.checkIPLimit(ip, 'connection')) {
    logger.warn(`Socket connection rate limit exceeded for IP: ${ip}`)
    next(new Error('Rate limit exceeded'))
    return
  }

  // Check if IP is banned
  if (antiSpam.isIPBanned(ip)) {
    logger.warn(`Blocked connection from banned IP: ${ip}`)
    next(new Error('Access denied'))
    return
  }

  // Validate JWT token
  if (!token) {
    next(new Error('Authentication token required'))
    return
  }

  const jwtValidation = walletAuthService.validateJWT(token)
  if (!jwtValidation.isValid) {
    antiSpam.reportViolation(ip, 'low')
    next(new Error(jwtValidation.error || 'Invalid token'))
    return
  }

  // Validate session token
  if (!sessionToken) {
    next(new Error('Session token required'))
    return
  }

  const sessionValidation = walletAuthService.validateSessionToken(sessionToken)
  if (!sessionValidation.isValid) {
    antiSpam.reportViolation(ip, 'low')
    next(new Error(sessionValidation.error || 'Invalid session'))
    return
  }

  // Ensure JWT and session match
  if (jwtValidation.payload?.sessionToken !== sessionToken) {
    antiSpam.reportViolation(ip, 'medium')
    next(new Error('Token mismatch'))
    return
  }

  // Add authentication data to socket
  socket.data.user = jwtValidation.payload
  socket.data.session = sessionValidation.sessionData
  socket.data.ip = ip

  logger.info(`Socket authenticated for wallet ${jwtValidation.payload?.walletAddress}`)
  next()
}

// Middleware to check if user can join game
export function canJoinGame(socket: Socket, next: (err?: Error) => void): void {
  const sessionData = socket.data.session
  
  if (!sessionData) {
    next(new Error('No session data'))
    return
  }

  // Check if session is still active
  if (!sessionData.isActive) {
    next(new Error('Session is inactive'))
    return
  }

  // Check wager amount is valid
  if (sessionData.wagerAmount <= 0) {
    next(new Error('Invalid wager amount'))
    return
  }

  // Additional game-specific checks could go here
  // For example: checking if user is already in a game, etc.

  next()
}

// Middleware for admin routes
export function requireAdmin(req: Request, res: Response, next: NextFunction): void {
  const user = (req as any).user
  
  if (!user) {
    res.status(401).json({ error: 'Authentication required' })
    return
  }

  // TODO: Implement admin role checking
  // For now, we'll use a simple wallet address check
  const adminWallets = process.env.ADMIN_WALLETS?.split(',') || []
  
  if (!adminWallets.includes(user.walletAddress)) {
    res.status(403).json({ error: 'Admin access required' })
    return
  }

  next()
}

// Rate limiting middleware for sensitive operations
export function rateLimitSensitive(req: Request, res: Response, next: NextFunction): void {
  const ip = req.ip || req.connection.remoteAddress || 'unknown'
  
  if (!ipRateLimiter.checkIPLimit(ip, 'request')) {
    res.status(429).json({ error: 'Rate limit exceeded' })
    return
  }

  next()
}

// Middleware to log authentication events
export function logAuthEvents(req: Request, res: Response, next: NextFunction): void {
  const user = (req as any).user
  const ip = req.ip || req.connection.remoteAddress
  
  logger.info({
    event: 'auth_request',
    wallet: user?.walletAddress,
    ip,
    path: req.path,
    method: req.method,
    userAgent: req.headers['user-agent'],
  })

  next()
}

// Middleware to validate request origin
export function validateOrigin(req: Request, res: Response, next: NextFunction): void {
  const origin = req.headers.origin
  const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000']
  
  if (origin && !allowedOrigins.includes(origin)) {
    logger.warn(`Request from unauthorized origin: ${origin}`)
    res.status(403).json({ error: 'Unauthorized origin' })
    return
  }

  next()
}

// Socket middleware to validate player input
export function validatePlayerInput(socket: Socket, next: (err?: Error) => void): void {
  const sessionData = socket.data.session
  const ip = socket.data.ip
  
  if (!sessionData) {
    next(new Error('No session data'))
    return
  }

  // Check for suspicious activity
  if (antiSpam.isIPSuspicious(ip)) {
    logger.warn(`Suspicious activity detected from IP: ${ip}`)
    // Don't block, but flag for monitoring
  }

  next()
}

// Cleanup expired authentication data
export function cleanupAuthData(): void {
  // This would be called periodically to clean up expired sessions
  logger.debug('Cleaning up expired authentication data')
}

// Health check for authentication system
export function authHealthCheck(): {
  status: 'healthy' | 'degraded' | 'unhealthy'
  details: {
    sessionCount: number
    activeSessions: number
    errors: string[]
  }
} {
  try {
    const stats = walletAuthService.getSessionStats()
    
    return {
      status: 'healthy',
      details: {
        sessionCount: stats.totalSessions,
        activeSessions: stats.activeSessions,
        errors: [],
      },
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        sessionCount: 0,
        activeSessions: 0,
        errors: [String(error)],
      },
    }
  }
}

// Export middleware functions
export const authMiddleware = {
  authenticateJWT,
  authenticateSession,
  authenticateSocket,
  canJoinGame,
  requireAdmin,
  rateLimitSensitive,
  logAuthEvents,
  validateOrigin,
  validatePlayerInput,
  cleanupAuthData,
  authHealthCheck,
}
