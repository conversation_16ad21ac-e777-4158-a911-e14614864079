import { PlayerInput, Player } from '../game/types.js'
import { RATE_LIMITS } from '../config/lobby.js'
import pino from 'pino'

const logger = pino({ name: 'input-validation' })

export function validatePlayerInput(data: any, player: Player): PlayerInput | null {
  try {
    // Basic structure validation
    if (!data || typeof data !== 'object') {
      return null
    }

    // Required fields
    if (data.playerId !== player.id) {
      logger.warn(`Player ID mismatch: expected ${player.id}, got ${data.playerId}`)
      return null
    }

    if (data.sessionToken !== player.sessionToken) {
      logger.warn(`Session token mismatch for player ${player.id}`)
      return null
    }

    if (typeof data.timestamp !== 'number') {
      return null
    }

    if (typeof data.sequenceNumber !== 'number') {
      return null
    }

    // Timestamp validation (prevent old or future inputs)
    const now = Date.now()
    const timeDiff = Math.abs(now - data.timestamp)
    
    if (timeDiff > RATE_LIMITS.MAX_INPUT_AGE) {
      logger.warn(`Input too old/future for player ${player.id}: ${timeDiff}ms`)
      return null
    }

    // Sequence number validation (prevent replay attacks)
    if (data.sequenceNumber <= player.inputCount) {
      logger.warn(`Old sequence number for player ${player.id}: ${data.sequenceNumber} <= ${player.inputCount}`)
      return null
    }

    // Movement validation
    let targetX: number | undefined
    let targetY: number | undefined
    
    if (data.targetX !== undefined || data.targetY !== undefined) {
      if (typeof data.targetX !== 'number' || typeof data.targetY !== 'number') {
        return null
      }
      
      // Validate coordinates are within reasonable bounds
      if (!isValidCoordinate(data.targetX) || !isValidCoordinate(data.targetY)) {
        return null
      }
      
      targetX = data.targetX
      targetY = data.targetY
    }

    // Action validation
    const split = data.split === true
    const eject = data.eject === true
    const cashout = data.cashout === true

    // Prevent multiple actions in single input
    const actionCount = [split, eject, cashout].filter(Boolean).length
    if (actionCount > 1) {
      logger.warn(`Multiple actions in single input for player ${player.id}`)
      return null
    }

    // Rate limiting validation
    if (!validateInputRate(player, now)) {
      return null
    }

    // Create validated input
    const validatedInput: PlayerInput = {
      playerId: data.playerId,
      sessionToken: data.sessionToken,
      timestamp: data.timestamp,
      sequenceNumber: data.sequenceNumber,
      targetX,
      targetY,
      split: split || undefined,
      eject: eject || undefined,
      cashout: cashout || undefined,
    }

    return validatedInput

  } catch (error) {
    logger.error(error, `Error validating input for player ${player?.id}`)
    return null
  }
}

function isValidCoordinate(coord: number): boolean {
  return typeof coord === 'number' && 
         !isNaN(coord) && 
         isFinite(coord) && 
         coord >= -10000 && 
         coord <= 10000
}

function validateInputRate(player: Player, now: number): boolean {
  const timeSinceLastInput = now - player.lastInput
  
  // Minimum time between inputs
  if (timeSinceLastInput < RATE_LIMITS.MIN_INPUT_INTERVAL) {
    logger.warn(`Input rate too high for player ${player.id}: ${timeSinceLastInput}ms`)
    return false
  }
  
  return true
}

// Validate wallet signature for authentication
export function validateWalletSignature(
  wallet: string, 
  message: string, 
  signature: string
): boolean {
  try {
    // TODO: Implement proper Solana wallet signature validation
    // This would use @solana/web3.js to verify the signature
    
    // For now, return true for development
    return true
    
  } catch (error) {
    logger.error(error, 'Error validating wallet signature')
    return false
  }
}

// Validate join game data
export function validateJoinGameData(data: any): boolean {
  try {
    if (!data || typeof data !== 'object') {
      return false
    }

    // Required fields
    if (typeof data.wallet !== 'string' || data.wallet.length < 32) {
      return false
    }

    if (typeof data.signature !== 'string' || data.signature.length < 64) {
      return false
    }

    if (typeof data.wagerAmount !== 'number' || data.wagerAmount <= 0) {
      return false
    }

    // Optional fields
    if (data.username !== undefined && typeof data.username !== 'string') {
      return false
    }

    // Validate wager amount is within reasonable bounds
    if (data.wagerAmount > 1000) { // Max 1000 SOL
      return false
    }

    return true

  } catch (error) {
    logger.error(error, 'Error validating join game data')
    return false
  }
}

// Sanitize username
export function sanitizeUsername(username: string | undefined): string | undefined {
  if (!username) return undefined
  
  // Remove non-printable characters and limit length
  const sanitized = username
    .replace(/[^\x20-\x7E]/g, '') // Remove non-ASCII printable
    .trim()
    .substring(0, 20) // Max 20 characters
  
  return sanitized.length > 0 ? sanitized : undefined
}

// Validate cashout request
export function validateCashoutRequest(data: any, player: Player): boolean {
  try {
    if (!data || typeof data !== 'object') {
      return false
    }

    // Must be the correct player
    if (data.playerId !== player.id) {
      return false
    }

    // Must have valid session token
    if (data.sessionToken !== player.sessionToken) {
      return false
    }

    // Player must be alive and eligible for cashout
    if (!player.isAlive || !player.canCashout) {
      return false
    }

    return true

  } catch (error) {
    logger.error(error, 'Error validating cashout request')
    return false
  }
}

// Anti-cheat validation
export function detectSuspiciousActivity(player: Player, input: PlayerInput): boolean {
  const now = Date.now()
  
  // Check for impossible movement speeds
  if (input.targetX !== undefined && input.targetY !== undefined) {
    const timeDiff = now - player.lastValidatedTime
    if (timeDiff > 0) {
      const dx = input.targetX - player.lastValidatedPosition.x
      const dy = input.targetY - player.lastValidatedPosition.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      const speed = distance / (timeDiff / 1000) // pixels per second
      
      // Maximum possible speed based on game mechanics
      const maxSpeed = 500 // This would be calculated based on player's mass
      
      if (speed > maxSpeed) {
        logger.warn(`Suspicious speed detected for player ${player.id}: ${speed} px/s`)
        return true
      }
    }
  }
  
  // Check for rapid action spamming
  const actionCount = [input.split, input.eject, input.cashout].filter(Boolean).length
  if (actionCount > 0) {
    const timeSinceLastAction = now - player.lastInput
    if (timeSinceLastAction < 100) { // Minimum 100ms between actions
      logger.warn(`Action spam detected for player ${player.id}`)
      return true
    }
  }
  
  return false
}

// Input sanitization
export function sanitizeInput(input: PlayerInput): PlayerInput {
  return {
    ...input,
    // Clamp coordinates to reasonable bounds
    targetX: input.targetX !== undefined ? Math.max(-5000, Math.min(5000, input.targetX)) : undefined,
    targetY: input.targetY !== undefined ? Math.max(-5000, Math.min(5000, input.targetY)) : undefined,
    // Ensure timestamp is reasonable
    timestamp: Math.max(Date.now() - 5000, Math.min(Date.now() + 1000, input.timestamp)),
  }
}
