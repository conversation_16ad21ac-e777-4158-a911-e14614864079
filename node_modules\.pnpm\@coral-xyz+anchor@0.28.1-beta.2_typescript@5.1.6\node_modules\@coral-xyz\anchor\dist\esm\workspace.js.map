{"version": 3, "file": "workspace.js", "sourceRoot": "", "sources": ["../../src/workspace.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C;;;;;;GAMG;AACH,MAAM,SAAS,GAAG,IAAI,KAAK,CACzB,EAAE,EACF;IACE,GAAG,CAAC,cAA0C,EAAE,WAAmB;;QACjE,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,wEAAwE;QACxE,oDAAoD;QACpD,6EAA6E;QAC7E,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;QAErC,qDAAqD;QACrD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC1B,qEAAqE;YACrE,2EAA2E;YAC3E,sEAAsE;YACtE,2DAA2D;YAC3D,EAAE;YACF,qEAAqE;YACrE,uEAAuE;YACvE,sEAAsE;YACtE,uEAAuE;YACvE,wCAAwC;YACxC,WAAW,GAAG,WAAW;iBACtB,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;iBACvC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACvB;QAED,0CAA0C;QAC1C,IAAI,cAAc,CAAC,WAAW,CAAC;YAAE,OAAO,cAAc,CAAC,WAAW,CAAC,CAAC;QAEpE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE7B,sEAAsE;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAA,MAAA,UAAU,CAAC,QAAQ,0CAAG,SAAS,CAAC,0CAAG,WAAW,CAAC,CAAC;QAErE,IAAI,OAAe,CAAC;QACpB,IAAI,SAAS,CAAC;QACd,IAAI,OAAO,YAAY,KAAK,QAAQ,IAAI,YAAY,CAAC,GAAG,EAAE;YACxD,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC;YAC3B,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;SAClC;aAAM;YACL,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,OAAO,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CACb,GAAG,OAAO,+CAA+C,CAC1D,CAAC;SACH;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE;YACd,IAAI,CAAC,CAAA,MAAA,GAAG,CAAC,QAAQ,0CAAE,OAAO,CAAA,EAAE;gBAC1B,MAAM,IAAI,KAAK,CACb,qBAAqB,WAAW,gDAAgD;oBAC9E,iEAAiE,CACpE,CAAC;aACH;YACD,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;SAClC;QACD,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAE1D,OAAO,cAAc,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;CACF,CACF,CAAC;AAEF,eAAe,SAAS,CAAC"}