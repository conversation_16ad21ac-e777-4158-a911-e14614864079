import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Vir<PERSON>, 
  Ejected<PERSON><PERSON>, 
  <PERSON><PERSON>tyT<PERSON>, 
  <PERSON>ector2, 
  <PERSON>unds,
  GameEvent,
  GameEventType,
  LobbyConfig
} from './types.js'
import { QuadTreeImpl } from './quadtree.js'
import { GAME_CONFIG, SPATIAL_CONFIG } from '../config/lobby.js'
import { generateId } from '../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'game-state' })

export class GameState {
  private world: GameWorld
  private config: LobbyConfig
  private events: GameEvent[] = []
  private lastPelletSpawn = 0
  private lastVirusSpawn = 0

  constructor(lobbyTier: number, config: LobbyConfig) {
    this.config = config
    this.world = {
      players: new Map(),
      pellets: new Map(),
      viruses: new Map(),
      ejectedMass: new Map(),
      quadTree: new QuadTreeImpl(
        SPATIAL_CONFIG.QUADTREE_BOUNDS,
        SPATIAL_CONFIG.MAX_OBJECTS_PER_NODE,
        SPATIAL_CONFIG.MAX_LEVELS
      ),
      totalPlayers: 0,
      totalMass: 0,
      totalValue: 0,
      lastTick: Date.now(),
      tickCount: 0,
      lobbyTier,
    }

    this.initializeWorld()
  }

  // Initialize the game world with pellets and viruses
  private initializeWorld(): void {
    logger.info(`Initializing world for lobby tier ${this.world.lobbyTier}`)
    
    // Spawn initial pellets
    this.spawnPellets(GAME_CONFIG.PELLET_COUNT)
    
    // Spawn initial viruses
    this.spawnViruses(GAME_CONFIG.VIRUS_COUNT)
    
    logger.info(`World initialized with ${this.world.pellets.size} pellets and ${this.world.viruses.size} viruses`)
  }

  // Add a player to the game world
  addPlayer(player: Player): void {
    logger.info(`Adding player ${player.id} to lobby tier ${this.world.lobbyTier}`)
    
    // Find safe spawn position
    const spawnPosition = this.findSafeSpawnPosition()
    
    // Create initial player cell
    const cell: PlayerCell = {
      id: generateId(),
      type: EntityType.PLAYER_CELL,
      playerId: player.id,
      position: spawnPosition,
      radius: this.massToRadius(player.initialMass),
      mass: player.initialMass,
      velocity: { x: 0, y: 0 },
      targetPosition: spawnPosition,
      canMerge: true,
      mergeTime: 0,
      splitCooldown: 0,
      lastSplit: 0,
    }

    // Update player with initial cell
    player.cells = [cell]
    player.totalMass = player.initialMass
    player.score = player.initialMass
    player.targetPosition = spawnPosition
    player.isAlive = true
    player.canCashout = false
    player.lastValidatedPosition = spawnPosition
    player.lastValidatedTime = Date.now()

    // Add to world
    this.world.players.set(player.id, player)
    this.world.quadTree.insert(cell)
    
    // Update world statistics
    this.updateWorldStats()
    
    // Create event
    this.addEvent({
      type: GameEventType.PLAYER_JOINED,
      timestamp: Date.now(),
      playerId: player.id,
      data: {
        username: player.username,
        initialMass: player.initialMass,
        wagerAmount: player.wagerAmount,
        position: spawnPosition,
      },
    })

    logger.info(`Player ${player.id} added at position (${spawnPosition.x}, ${spawnPosition.y})`)
  }

  // Remove a player from the game world
  removePlayer(playerId: string, reason: string, eliminatedBy?: string): void {
    const player = this.world.players.get(playerId)
    if (!player) return

    logger.info(`Removing player ${playerId} - reason: ${reason}`)

    // Remove all player cells from quadtree
    for (const cell of player.cells) {
      this.world.quadTree.remove(cell)
    }

    // Remove player from world
    this.world.players.delete(playerId)
    
    // Update world statistics
    this.updateWorldStats()

    // Create event
    this.addEvent({
      type: reason === 'eliminated' ? GameEventType.PLAYER_ELIMINATED : GameEventType.PLAYER_LEFT,
      timestamp: Date.now(),
      playerId,
      data: {
        reason,
        eliminatedBy,
        finalMass: player.totalMass,
        finalValue: player.currentValue,
      },
    })

    logger.info(`Player ${playerId} removed`)
  }

  // Get current world state
  getWorld(): GameWorld {
    return this.world
  }

  // Get player by ID
  getPlayer(playerId: string): Player | undefined {
    return this.world.players.get(playerId)
  }

  // Get all players
  getPlayers(): Player[] {
    return Array.from(this.world.players.values())
  }

  // Get entities within a specific area (for client view)
  getEntitiesInArea(bounds: Bounds): {
    players: Player[]
    pellets: Pellet[]
    viruses: Virus[]
    ejectedMass: EjectedMass[]
  } {
    const entities = this.world.quadTree.retrieve(bounds)
    
    const players: Player[] = []
    const pellets: Pellet[] = []
    const viruses: Virus[] = []
    const ejectedMass: EjectedMass[] = []

    for (const entity of entities) {
      switch (entity.type) {
        case EntityType.PLAYER_CELL:
          const cell = entity as PlayerCell
          const player = this.world.players.get(cell.playerId)
          if (player && !players.find(p => p.id === player.id)) {
            players.push(player)
          }
          break
        case EntityType.PELLET:
          pellets.push(entity as Pellet)
          break
        case EntityType.VIRUS:
          viruses.push(entity as Virus)
          break
        case EntityType.EJECTED_MASS:
          ejectedMass.push(entity as EjectedMass)
          break
      }
    }

    return { players, pellets, viruses, ejectedMass }
  }

  // Get and clear events since last call
  getEvents(): GameEvent[] {
    const events = [...this.events]
    this.events = []
    return events
  }

  // Update world tick
  updateTick(): void {
    const now = Date.now()
    this.world.lastTick = now
    this.world.tickCount++
  }

  // Get current tick
  getCurrentTick(): number {
    return this.world.tickCount
  }

  // Spawn pellets in the world
  private spawnPellets(count: number): void {
    for (let i = 0; i < count; i++) {
      this.spawnPellet()
    }
  }

  // Spawn a single pellet
  private spawnPellet(): void {
    const pellet: Pellet = {
      id: generateId(),
      type: EntityType.PELLET,
      position: this.getRandomPosition(),
      radius: this.massToRadius(GAME_CONFIG.PELLET_MASS),
      mass: GAME_CONFIG.PELLET_MASS,
      color: this.getRandomPelletColor(),
    }

    this.world.pellets.set(pellet.id, pellet)
    this.world.quadTree.insert(pellet)
  }

  // Spawn viruses in the world
  private spawnViruses(count: number): void {
    for (let i = 0; i < count; i++) {
      this.spawnVirus()
    }
  }

  // Spawn a single virus
  private spawnVirus(): void {
    const virus: Virus = {
      id: generateId(),
      type: EntityType.VIRUS,
      position: this.getRandomPosition(),
      radius: this.massToRadius(GAME_CONFIG.VIRUS_MASS),
      mass: GAME_CONFIG.VIRUS_MASS,
      feedCount: 0,
      maxFeedCount: GAME_CONFIG.VIRUS_FEED_THRESHOLD,
      popCooldown: 0,
      lastPop: 0,
    }

    this.world.viruses.set(virus.id, virus)
    this.world.quadTree.insert(virus)
  }

  // Update world statistics
  private updateWorldStats(): void {
    this.world.totalPlayers = this.world.players.size

    let totalMass = 0
    let totalValue = 0

    for (const player of this.world.players.values()) {
      totalMass += player.totalMass
      totalValue += player.currentValue
    }

    this.world.totalMass = totalMass
    this.world.totalValue = totalValue
  }

  // Add an event to the event queue
  private addEvent(event: GameEvent): void {
    this.events.push(event)
  }

  // Convert mass to radius using game formula
  private massToRadius(mass: number): number {
    return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR
  }

  // Get random position within world bounds
  private getRandomPosition(): Vector2 {
    return {
      x: Math.random() * (this.config.worldBounds.width - 100) + 50,
      y: Math.random() * (this.config.worldBounds.height - 100) + 50,
    }
  }

  // Get random pellet color
  private getRandomPelletColor(): string {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  // Private helper methods
  private findSafeSpawnPosition(): Vector2 {
    const maxAttempts = 50
    const minDistanceFromOthers = 200

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const x = Math.random() * (this.config.worldBounds.width - 200) + 100
      const y = Math.random() * (this.config.worldBounds.height - 200) + 100

      // Check if position is safe (not too close to other players)
      let isSafe = true
      for (const player of this.world.players.values()) {
        for (const cell of player.cells) {
          const dx = cell.position.x - x
          const dy = cell.position.y - y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < minDistanceFromOthers) {
            isSafe = false
            break
          }
        }
        if (!isSafe) break
      }

      if (isSafe) {
        return { x, y }
      }
    }

    // Fallback to random position if no safe position found
    return {
      x: Math.random() * (this.config.worldBounds.width - 200) + 100,
      y: Math.random() * (this.config.worldBounds.height - 200) + 100,
    }
  }
}
