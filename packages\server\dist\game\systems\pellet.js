import { EntityType } from '../types.js';
import { GAME_CONFIG } from '../../config/lobby.js';
import { generateId } from '../../utils/id.js';
import pino from 'pino';
const logger = pino({ name: 'pellet-system' });
export class PelletSystem {
    gameState;
    lastSpawnTime = 0;
    spawnInterval = 1000 / GAME_CONFIG.PELLET_RESPAWN_RATE; // ms between spawns
    constructor(gameState) {
        this.gameState = gameState;
    }
    update(deltaTime) {
        const now = Date.now();
        // Check if we need to spawn more pellets
        if (now - this.lastSpawnTime >= this.spawnInterval) {
            this.maintainPelletCount();
            this.lastSpawnTime = now;
        }
        // Clean up any expired pellets (if we add expiration later)
        this.cleanupExpiredPellets();
    }
    maintainPelletCount() {
        const world = this.gameState.getWorld();
        const currentPelletCount = world.pellets.size;
        const targetPelletCount = GAME_CONFIG.PELLET_COUNT;
        if (currentPelletCount < targetPelletCount) {
            const pelletsToSpawn = Math.min(targetPelletCount - currentPelletCount, GAME_CONFIG.PELLET_RESPAWN_RATE // Don't spawn too many at once
            );
            for (let i = 0; i < pelletsToSpawn; i++) {
                this.spawnPellet();
            }
        }
    }
    spawnPellet() {
        const world = this.gameState.getWorld();
        const position = this.findSafePelletPosition();
        const pellet = {
            id: generateId(),
            type: EntityType.PELLET,
            position,
            radius: this.massToRadius(GAME_CONFIG.PELLET_MASS),
            mass: GAME_CONFIG.PELLET_MASS,
            color: this.getRandomPelletColor(),
        };
        world.pellets.set(pellet.id, pellet);
        world.quadTree.insert(pellet);
    }
    findSafePelletPosition() {
        const world = this.gameState.getWorld();
        const bounds = world.quadTree.root.bounds;
        const maxAttempts = 10;
        const minDistanceFromPlayers = 50;
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const x = Math.random() * (bounds.width - 100) + 50;
            const y = Math.random() * (bounds.height - 100) + 50;
            // Check if position is safe (not too close to players)
            let isSafe = true;
            for (const player of world.players.values()) {
                for (const cell of player.cells) {
                    const dx = cell.position.x - x;
                    const dy = cell.position.y - y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    if (distance < minDistanceFromPlayers) {
                        isSafe = false;
                        break;
                    }
                }
                if (!isSafe)
                    break;
            }
            if (isSafe) {
                return { x, y };
            }
        }
        // Fallback to random position
        return {
            x: Math.random() * (bounds.width - 100) + 50,
            y: Math.random() * (bounds.height - 100) + 50,
        };
    }
    cleanupExpiredPellets() {
        // For now, pellets don't expire
        // This method is here for future expansion
    }
    getRandomPelletColor() {
        const colors = [
            '#FF6B6B',
            '#4ECDC4',
            '#45B7D1',
            '#96CEB4',
            '#FFEAA7',
            '#DDA0DD',
            '#98D8C8',
            '#F39C12',
            '#E74C3C',
            '#3498DB',
            '#2ECC71',
            '#9B59B6',
            '#F1C40F',
            '#E67E22',
            '#1ABC9C', // Turquoise
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    massToRadius(mass) {
        return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR;
    }
    // Public methods for external access
    spawnPelletAt(position) {
        const world = this.gameState.getWorld();
        const pellet = {
            id: generateId(),
            type: EntityType.PELLET,
            position,
            radius: this.massToRadius(GAME_CONFIG.PELLET_MASS),
            mass: GAME_CONFIG.PELLET_MASS,
            color: this.getRandomPelletColor(),
        };
        world.pellets.set(pellet.id, pellet);
        world.quadTree.insert(pellet);
    }
    removePellet(pelletId) {
        const world = this.gameState.getWorld();
        const pellet = world.pellets.get(pelletId);
        if (pellet) {
            world.pellets.delete(pelletId);
            world.quadTree.remove(pellet);
        }
    }
    getPelletCount() {
        return this.gameState.getWorld().pellets.size;
    }
}
