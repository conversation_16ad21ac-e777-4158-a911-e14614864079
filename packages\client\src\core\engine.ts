import { Camera } from './camera.js'
import { Renderer } from './renderer.js'
import { useGameStore, useRenderStore } from '../store/game-store.js'
import { Vector2, GameSnapshot } from '../types/game.js'

export interface EngineConfig {
  targetFPS: number
  enableInterpolation: boolean
  maxDeltaTime: number
}

export class GameEngine {
  private canvas: HTMLCanvasElement
  private camera: Camera
  private renderer: Renderer
  private config: EngineConfig
  
  private isRunning: boolean = false
  private lastTime: number = 0
  private accumulator: number = 0
  private frameId: number = 0
  
  // Store subscriptions
  private unsubscribeGameStore?: () => void
  private unsubscribeRenderStore?: () => void
  
  constructor(canvas: HTMLCanvasElement, config: Partial<EngineConfig> = {}) {
    this.canvas = canvas
    this.config = {
      targetFPS: 60,
      enableInterpolation: true,
      maxDeltaTime: 1/30, // Cap at 30 FPS minimum
      ...config
    }
    
    // Initialize camera and renderer
    this.camera = new Camera()
    this.renderer = new Renderer(canvas, this.camera)
    
    // Setup event listeners
    this.setupEventListeners()
    
    // Subscribe to store changes
    this.setupStoreSubscriptions()
  }
  
  private setupEventListeners(): void {
    // Handle window resize
    window.addEventListener('resize', () => {
      this.renderer.resize()
    })
    
    // Handle visibility change (pause when tab is hidden)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pause()
      } else {
        this.resume()
      }
    })
  }
  
  private setupStoreSubscriptions(): void {
    // Subscribe to game state changes
    this.unsubscribeGameStore = useGameStore.subscribe(
      (state) => state.gameSnapshot,
      (snapshot) => {
        if (snapshot) {
          this.handleGameSnapshot(snapshot)
        }
      }
    )
    
    // Subscribe to render state changes
    this.unsubscribeRenderStore = useRenderStore.subscribe(
      (state) => state.camera,
      (cameraState) => {
        this.camera.setTarget(cameraState.x, cameraState.y)
        this.camera.setZoom(cameraState.targetZoom)
      }
    )
  }
  
  private handleGameSnapshot(snapshot: GameSnapshot): void {
    const renderStore = useRenderStore.getState()
    
    // Update render entities
    renderStore.updateEntities(snapshot)
    
    // Update camera to follow local player
    const gameStore = useGameStore.getState()
    const localPlayer = gameStore.localPlayer
    
    if (localPlayer && localPlayer.cells.length > 0) {
      // Calculate center of mass for all player cells
      const centerOfMass = this.calculateCenterOfMass(localPlayer.cells)
      this.camera.followPlayer(centerOfMass, localPlayer.totalMass)
    }
  }
  
  private calculateCenterOfMass(cells: any[]): Vector2 {
    if (cells.length === 0) return { x: 0, y: 0 }
    
    let totalMass = 0
    let weightedX = 0
    let weightedY = 0
    
    cells.forEach(cell => {
      totalMass += cell.mass
      weightedX += cell.position.x * cell.mass
      weightedY += cell.position.y * cell.mass
    })
    
    return {
      x: weightedX / totalMass,
      y: weightedY / totalMass
    }
  }
  
  /**
   * Start the game engine
   */
  start(): void {
    if (this.isRunning) return
    
    this.isRunning = true
    this.lastTime = performance.now()
    this.gameLoop()
  }
  
  /**
   * Stop the game engine
   */
  stop(): void {
    this.isRunning = false
    if (this.frameId) {
      cancelAnimationFrame(this.frameId)
      this.frameId = 0
    }
  }
  
  /**
   * Pause the game engine
   */
  pause(): void {
    this.isRunning = false
  }
  
  /**
   * Resume the game engine
   */
  resume(): void {
    if (!this.isRunning) {
      this.isRunning = true
      this.lastTime = performance.now()
      this.gameLoop()
    }
  }
  
  /**
   * Main game loop
   */
  private gameLoop = (): void => {
    if (!this.isRunning) return
    
    const currentTime = performance.now()
    let deltaTime = (currentTime - this.lastTime) / 1000
    
    // Cap delta time to prevent spiral of death
    deltaTime = Math.min(deltaTime, this.config.maxDeltaTime)
    
    this.lastTime = currentTime
    this.accumulator += deltaTime
    
    const fixedTimeStep = 1 / this.config.targetFPS
    
    // Fixed timestep updates
    while (this.accumulator >= fixedTimeStep) {
      this.update(fixedTimeStep)
      this.accumulator -= fixedTimeStep
    }
    
    // Render with interpolation
    const alpha = this.config.enableInterpolation ? this.accumulator / fixedTimeStep : 0
    this.render(deltaTime, alpha)
    
    this.frameId = requestAnimationFrame(this.gameLoop)
  }
  
  /**
   * Update game logic
   */
  private update(deltaTime: number): void {
    // Update camera
    this.camera.update(deltaTime)
    
    // Update particle effects
    const renderStore = useRenderStore.getState()
    renderStore.updateParticleEffects(deltaTime)
  }
  
  /**
   * Render the game
   */
  private render(deltaTime: number, alpha: number): void {
    const renderStore = useRenderStore.getState()
    const { entities, effects } = renderStore
    
    // Render the game world
    this.renderer.render(
      entities.players,
      entities.pellets,
      entities.viruses,
      entities.ejectedMass,
      effects,
      deltaTime
    )
  }
  
  /**
   * Get engine statistics
   */
  getStats(): {
    isRunning: boolean
    fps: number
    camera: ReturnType<Camera['getState']>
  } {
    return {
      isRunning: this.isRunning,
      fps: this.config.targetFPS,
      camera: this.camera.getState()
    }
  }
  
  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stop()
    
    // Unsubscribe from stores
    if (this.unsubscribeGameStore) {
      this.unsubscribeGameStore()
    }
    if (this.unsubscribeRenderStore) {
      this.unsubscribeRenderStore()
    }
    
    // Remove event listeners
    window.removeEventListener('resize', this.renderer.resize)
  }
}
