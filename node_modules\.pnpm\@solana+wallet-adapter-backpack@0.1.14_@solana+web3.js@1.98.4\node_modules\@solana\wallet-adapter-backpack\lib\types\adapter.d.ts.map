{"version": 3, "file": "adapter.d.ts", "sourceRoot": "", "sources": ["../../src/adapter.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAgB,sBAAsB,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AACpG,OAAO,EACH,8BAA8B,EAS9B,gBAAgB,EAInB,MAAM,6BAA6B,CAAC;AACrC,OAAO,KAAK,EAAE,UAAU,EAAuB,WAAW,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;AAC1G,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AA+B5C,MAAM,WAAW,2BAA2B;CAAG;AAE/C,eAAO,MAAM,kBAAkB,wBAAuC,CAAC;AAEvE,qBAAa,qBAAsB,SAAQ,8BAA8B;IACrE,IAAI,yBAAsB;IAC1B,GAAG,SAA0B;IAC7B,IAAI,SACy+E;IAC7+E,QAAQ,CAAC,4BAA4B,OAAQ;IAE7C,OAAO,CAAC,WAAW,CAAU;IAC7B,OAAO,CAAC,OAAO,CAAwB;IACvC,OAAO,CAAC,UAAU,CAAmB;IACrC,OAAO,CAAC,WAAW,CAGoB;gBAE3B,MAAM,GAAE,2BAAgC;IAkBpD,IAAI,SAAS,qBAEZ;IAED,IAAI,UAAU,YAEb;IAED,IAAI,SAAS,YAEZ;IAED,IAAI,UAAU,qBAEb;IAEK,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAuCxB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAkB3B,eAAe,CACjB,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,UAAU,EACtB,OAAO,GAAE,sBAA2B,GACrC,OAAO,CAAC,oBAAoB,CAAC;IAkB1B,eAAe,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAgBlE,mBAAmB,CAAC,CAAC,SAAS,WAAW,EAAE,YAAY,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAgB3E,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAgB3D,OAAO,CAAC,aAAa,CAWnB;CACL"}