import { PlayerInput, GameSnapshot, Vector2, Player } from '../types/game.js'
import { useGameStore } from '../store/game-store.js'

export interface ReconciliationConfig {
  maxPredictionTime: number // Maximum time to predict ahead (ms)
  maxInputBuffer: number // Maximum number of inputs to buffer
  interpolationDelay: number // Delay for interpolation (ms)
  correctionThreshold: number // Distance threshold for corrections
  smoothingFactor: number // Smoothing factor for corrections
}

export interface PredictedState {
  position: Vector2
  velocity: Vector2
  timestamp: number
  inputSequence: number
}

export class InputReconciliation {
  private config: ReconciliationConfig
  private inputBuffer: PlayerInput[] = []
  private predictedStates: PredictedState[] = []
  private lastServerState: Player | null = null
  private lastServerTimestamp: number = 0
  
  constructor(config: Partial<ReconciliationConfig> = {}) {
    this.config = {
      maxPredictionTime: 1000,
      maxInputBuffer: 60,
      interpolationDelay: 100,
      correctionThreshold: 5,
      smoothingFactor: 0.1,
      ...config
    }
  }
  
  /**
   * Add input to buffer and predict state
   */
  addInput(input: PlayerInput): void {
    // Add to buffer
    this.inputBuffer.push(input)
    
    // Limit buffer size
    if (this.inputBuffer.length > this.config.maxInputBuffer) {
      this.inputBuffer.shift()
    }
    
    // Predict state based on input
    this.predictState(input)
  }
  
  /**
   * Process server state update and reconcile
   */
  reconcileServerState(serverPlayer: Player, serverTimestamp: number): void {
    this.lastServerState = serverPlayer
    this.lastServerTimestamp = serverTimestamp
    
    // Find the input that corresponds to this server state
    const serverInputSequence = this.findServerInputSequence(serverTimestamp)
    
    if (serverInputSequence === -1) {
      // No corresponding input found, just accept server state
      this.acceptServerState(serverPlayer)
      return
    }
    
    // Remove acknowledged inputs from buffer
    this.removeAcknowledgedInputs(serverInputSequence)
    
    // Check if correction is needed
    const predictedState = this.findPredictedState(serverInputSequence)
    if (predictedState && this.needsCorrection(predictedState, serverPlayer)) {
      this.performCorrection(serverPlayer, serverInputSequence)
    }
  }
  
  private predictState(input: PlayerInput): void {
    const gameStore = useGameStore.getState()
    const localPlayer = gameStore.localPlayer
    
    if (!localPlayer || localPlayer.cells.length === 0) return
    
    // Simple prediction: assume player moves toward target
    const primaryCell = localPlayer.cells[0]
    const currentPos = primaryCell.position
    const targetPos = { x: input.targetX || currentPos.x, y: input.targetY || currentPos.y }
    
    // Calculate movement vector
    const dx = targetPos.x - currentPos.x
    const dy = targetPos.y - currentPos.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance > 0) {
      // Normalize and apply speed
      const speed = this.calculatePlayerSpeed(primaryCell.mass)
      const velocity = {
        x: (dx / distance) * speed,
        y: (dy / distance) * speed
      }
      
      // Predict position after a small time step
      const deltaTime = 16 / 1000 // Assume 60 FPS
      const predictedPosition = {
        x: currentPos.x + velocity.x * deltaTime,
        y: currentPos.y + velocity.y * deltaTime
      }
      
      const predictedState: PredictedState = {
        position: predictedPosition,
        velocity,
        timestamp: input.timestamp,
        inputSequence: input.sequenceNumber
      }
      
      this.predictedStates.push(predictedState)
      
      // Limit predicted states buffer
      if (this.predictedStates.length > this.config.maxInputBuffer) {
        this.predictedStates.shift()
      }
    }
  }
  
  private calculatePlayerSpeed(mass: number): number {
    // Speed decreases with mass (similar to server calculation)
    const baseMass = 100
    const baseSpeed = 200 // pixels per second
    return baseSpeed / (1 + Math.sqrt(mass / baseMass))
  }
  
  private findServerInputSequence(serverTimestamp: number): number {
    // Find the input sequence that best matches the server timestamp
    // This is a simplified approach - in practice, you'd need more sophisticated timing
    
    const timeDiff = Date.now() - serverTimestamp
    const estimatedInputTime = serverTimestamp - timeDiff / 2 // Rough estimate
    
    let closestInput: PlayerInput | null = null
    let closestTimeDiff = Infinity
    
    for (const input of this.inputBuffer) {
      const diff = Math.abs(input.timestamp - estimatedInputTime)
      if (diff < closestTimeDiff) {
        closestTimeDiff = diff
        closestInput = input
      }
    }
    
    return closestInput ? closestInput.sequenceNumber : -1
  }
  
  private removeAcknowledgedInputs(acknowledgedSequence: number): void {
    this.inputBuffer = this.inputBuffer.filter(input => 
      input.sequenceNumber > acknowledgedSequence
    )
    
    this.predictedStates = this.predictedStates.filter(state => 
      state.inputSequence > acknowledgedSequence
    )
  }
  
  private findPredictedState(inputSequence: number): PredictedState | null {
    return this.predictedStates.find(state => 
      state.inputSequence === inputSequence
    ) || null
  }
  
  private needsCorrection(predictedState: PredictedState, serverPlayer: Player): boolean {
    if (serverPlayer.cells.length === 0) return false
    
    const serverPos = serverPlayer.cells[0].position
    const predictedPos = predictedState.position
    
    const distance = Math.sqrt(
      Math.pow(serverPos.x - predictedPos.x, 2) + 
      Math.pow(serverPos.y - predictedPos.y, 2)
    )
    
    return distance > this.config.correctionThreshold
  }
  
  private performCorrection(serverPlayer: Player, fromInputSequence: number): void {
    console.log('Performing client-side correction')
    
    // Start from server state
    let correctedState = this.createStateFromPlayer(serverPlayer)
    
    // Re-apply all inputs after the acknowledged one
    const inputsToReapply = this.inputBuffer.filter(input => 
      input.sequenceNumber > fromInputSequence
    )
    
    for (const input of inputsToReapply) {
      correctedState = this.applyInputToState(correctedState, input)
    }
    
    // Update local player state with correction
    this.applyCorrection(correctedState)
  }
  
  private createStateFromPlayer(player: Player): PredictedState {
    const primaryCell = player.cells[0]
    return {
      position: { ...primaryCell.position },
      velocity: { ...primaryCell.velocity },
      timestamp: Date.now(),
      inputSequence: 0
    }
  }
  
  private applyInputToState(state: PredictedState, input: PlayerInput): PredictedState {
    const targetPos = { x: input.targetX || state.position.x, y: input.targetY || state.position.y }
    
    // Calculate movement
    const dx = targetPos.x - state.position.x
    const dy = targetPos.y - state.position.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance > 0) {
      const speed = this.calculatePlayerSpeed(1000) // Use estimated mass
      const velocity = {
        x: (dx / distance) * speed,
        y: (dy / distance) * speed
      }
      
      const deltaTime = 16 / 1000 // 60 FPS
      const newPosition = {
        x: state.position.x + velocity.x * deltaTime,
        y: state.position.y + velocity.y * deltaTime
      }
      
      return {
        position: newPosition,
        velocity,
        timestamp: input.timestamp,
        inputSequence: input.sequenceNumber
      }
    }
    
    return state
  }
  
  private applyCorrection(correctedState: PredictedState): void {
    const gameStore = useGameStore.getState()
    const localPlayer = gameStore.localPlayer
    
    if (!localPlayer || localPlayer.cells.length === 0) return
    
    // Apply smooth correction to avoid jarring movements
    const currentPos = localPlayer.cells[0].position
    const targetPos = correctedState.position
    
    const smoothedPos = {
      x: currentPos.x + (targetPos.x - currentPos.x) * this.config.smoothingFactor,
      y: currentPos.y + (targetPos.y - currentPos.y) * this.config.smoothingFactor
    }
    
    // Update local player position
    const updatedCells = localPlayer.cells.map((cell, index) => {
      if (index === 0) {
        return {
          ...cell,
          position: smoothedPos,
          velocity: correctedState.velocity
        }
      }
      return cell
    })
    
    gameStore.updateLocalPlayer({
      cells: updatedCells
    })
  }
  
  private acceptServerState(serverPlayer: Player): void {
    const gameStore = useGameStore.getState()
    gameStore.updateLocalPlayer(serverPlayer)
  }
  
  /**
   * Get current prediction for rendering
   */
  getCurrentPrediction(): PredictedState | null {
    if (this.predictedStates.length === 0) return null
    
    // Return the most recent prediction
    return this.predictedStates[this.predictedStates.length - 1]
  }
  
  /**
   * Clean up old data
   */
  cleanup(): void {
    const now = Date.now()
    const maxAge = this.config.maxPredictionTime
    
    // Remove old inputs
    this.inputBuffer = this.inputBuffer.filter(input => 
      now - input.timestamp < maxAge
    )
    
    // Remove old predicted states
    this.predictedStates = this.predictedStates.filter(state => 
      now - state.timestamp < maxAge
    )
  }
  
  /**
   * Get reconciliation statistics
   */
  getStats(): {
    inputBufferSize: number
    predictedStatesSize: number
    lastServerTimestamp: number
    correctionThreshold: number
  } {
    return {
      inputBufferSize: this.inputBuffer.length,
      predictedStatesSize: this.predictedStates.length,
      lastServerTimestamp: this.lastServerTimestamp,
      correctionThreshold: this.config.correctionThreshold
    }
  }
  
  /**
   * Update configuration
   */
  updateConfig(config: Partial<ReconciliationConfig>): void {
    this.config = { ...this.config, ...config }
  }
}
