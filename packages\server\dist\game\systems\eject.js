import { EntityType } from '../types.js';
import { GAME_CONFIG } from '../../config/lobby.js';
import { generateId } from '../../utils/id.js';
import pino from 'pino';
const logger = pino({ name: 'eject-system' });
export class EjectSystem {
    gameState;
    constructor(gameState) {
        this.gameState = gameState;
    }
    update(deltaTime) {
        const world = this.gameState.getWorld();
        const now = Date.now();
        // Update all ejected mass entities
        for (const ejectedMass of world.ejectedMass.values()) {
            this.updateEjectedMass(ejectedMass, deltaTime, now);
        }
        // Clean up expired ejected mass
        this.cleanupExpiredEjectedMass(now);
    }
    processEjectInput(input) {
        const player = this.gameState.getPlayer(input.playerId);
        if (!player || !player.isAlive)
            return;
        const now = Date.now();
        // Find the largest cell that can eject
        const cellToEject = this.findCellForEjection(player, now);
        if (!cellToEject)
            return;
        // Eject mass from the cell
        this.ejectMassFromCell(cellToEject, player.targetPosition, now);
    }
    updateEjectedMass(ejectedMass, deltaTime, now) {
        const world = this.gameState.getWorld();
        const dt = deltaTime / 1000; // Convert to seconds
        // Apply velocity (ejected mass moves in straight line initially)
        ejectedMass.position.x += ejectedMass.velocity.x * dt;
        ejectedMass.position.y += ejectedMass.velocity.y * dt;
        // Apply friction to slow down over time
        const friction = GAME_CONFIG.EJECTED_MASS_FRICTION;
        ejectedMass.velocity.x *= (1 - friction * dt);
        ejectedMass.velocity.y *= (1 - friction * dt);
        // Stop very slow movement
        if (Math.abs(ejectedMass.velocity.x) < 1 && Math.abs(ejectedMass.velocity.y) < 1) {
            ejectedMass.velocity.x = 0;
            ejectedMass.velocity.y = 0;
        }
        // Constrain to world bounds
        this.constrainToWorldBounds(ejectedMass);
        // Update quadtree position
        world.quadTree.update(ejectedMass);
        // Check if ejected mass can now be eaten by its owner
        if (!ejectedMass.canBeEaten && now - ejectedMass.decayTime >= GAME_CONFIG.EJECTED_MASS_OWNER_IMMUNITY) {
            ejectedMass.canBeEaten = true;
        }
    }
    findCellForEjection(player, now) {
        // Find the largest cell that has enough mass to eject
        let bestCell = null;
        let largestMass = 0;
        for (const cell of player.cells) {
            // Check if cell has enough mass and is not on cooldown
            if (cell.mass >= GAME_CONFIG.MIN_EJECT_MASS &&
                cell.mass > largestMass &&
                now - cell.lastSplit >= GAME_CONFIG.EJECT_COOLDOWN) {
                bestCell = cell;
                largestMass = cell.mass;
            }
        }
        return bestCell;
    }
    ejectMassFromCell(cell, targetPosition, now) {
        const world = this.gameState.getWorld();
        const player = world.players.get(cell.playerId);
        if (!player)
            return;
        // Calculate ejection direction
        const dx = targetPosition.x - cell.position.x;
        const dy = targetPosition.y - cell.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        let dirX = 1; // Default direction if no target
        let dirY = 0;
        if (distance > 0) {
            dirX = dx / distance;
            dirY = dy / distance;
        }
        // Calculate ejection position (just outside the cell)
        const ejectDistance = cell.radius + GAME_CONFIG.EJECTED_MASS_SIZE + 5;
        const ejectPosition = {
            x: cell.position.x + dirX * ejectDistance,
            y: cell.position.y + dirY * ejectDistance,
        };
        // Create ejected mass entity
        const ejectedMass = {
            id: generateId(),
            type: EntityType.EJECTED_MASS,
            position: ejectPosition,
            radius: GAME_CONFIG.EJECTED_MASS_SIZE,
            mass: GAME_CONFIG.EJECTED_MASS_VALUE,
            velocity: {
                x: dirX * GAME_CONFIG.EJECT_VELOCITY,
                y: dirY * GAME_CONFIG.EJECT_VELOCITY,
            },
            ownerId: cell.playerId,
            decayTime: now,
            canBeEaten: false, // Owner can't eat it immediately
        };
        // Ensure ejected mass is within bounds
        const bounds = world.quadTree.root.bounds;
        ejectedMass.position.x = Math.max(ejectedMass.radius, Math.min(bounds.width - ejectedMass.radius, ejectedMass.position.x));
        ejectedMass.position.y = Math.max(ejectedMass.radius, Math.min(bounds.height - ejectedMass.radius, ejectedMass.position.y));
        // Reduce cell mass
        cell.mass -= GAME_CONFIG.EJECTED_MASS_VALUE;
        cell.radius = this.massToRadius(cell.mass);
        // Add ejected mass to world
        world.ejectedMass.set(ejectedMass.id, ejectedMass);
        world.quadTree.insert(ejectedMass);
        // Update cell in quadtree
        world.quadTree.update(cell);
        logger.debug(`Player ${player.id} ejected mass from cell ${cell.id}`);
        // Create event
        // This would be handled by the GameState class
    }
    constrainToWorldBounds(ejectedMass) {
        const world = this.gameState.getWorld();
        const bounds = world.quadTree.root.bounds;
        const minX = ejectedMass.radius;
        const maxX = bounds.width - ejectedMass.radius;
        const minY = ejectedMass.radius;
        const maxY = bounds.height - ejectedMass.radius;
        if (ejectedMass.position.x < minX) {
            ejectedMass.position.x = minX;
            ejectedMass.velocity.x = 0;
        }
        else if (ejectedMass.position.x > maxX) {
            ejectedMass.position.x = maxX;
            ejectedMass.velocity.x = 0;
        }
        if (ejectedMass.position.y < minY) {
            ejectedMass.position.y = minY;
            ejectedMass.velocity.y = 0;
        }
        else if (ejectedMass.position.y > maxY) {
            ejectedMass.position.y = maxY;
            ejectedMass.velocity.y = 0;
        }
    }
    cleanupExpiredEjectedMass(now) {
        const world = this.gameState.getWorld();
        const expiredMass = [];
        for (const [id, ejectedMass] of world.ejectedMass) {
            // Remove ejected mass after it expires
            if (now - ejectedMass.decayTime >= GAME_CONFIG.EJECTED_MASS_LIFETIME) {
                expiredMass.push(id);
            }
        }
        // Remove expired ejected mass
        for (const id of expiredMass) {
            const ejectedMass = world.ejectedMass.get(id);
            if (ejectedMass) {
                world.ejectedMass.delete(id);
                world.quadTree.remove(ejectedMass);
            }
        }
        if (expiredMass.length > 0) {
            logger.debug(`Cleaned up ${expiredMass.length} expired ejected mass entities`);
        }
    }
    massToRadius(mass) {
        return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR;
    }
    // Public methods for external access
    canEject(playerId) {
        const player = this.gameState.getPlayer(playerId);
        if (!player || !player.isAlive)
            return false;
        const now = Date.now();
        return this.findCellForEjection(player, now) !== null;
    }
    getEjectedMassCount() {
        return this.gameState.getWorld().ejectedMass.size;
    }
    removeEjectedMass(ejectedMassId) {
        const world = this.gameState.getWorld();
        const ejectedMass = world.ejectedMass.get(ejectedMassId);
        if (ejectedMass) {
            world.ejectedMass.delete(ejectedMassId);
            world.quadTree.remove(ejectedMass);
        }
    }
    // Force eject mass in a specific direction (for virus feeding, etc.)
    forceEjectMass(playerId, direction, amount = GAME_CONFIG.EJECTED_MASS_VALUE) {
        const player = this.gameState.getPlayer(playerId);
        if (!player || !player.isAlive)
            return false;
        const largestCell = player.cells.reduce((largest, current) => current.mass > largest.mass ? current : largest);
        if (!largestCell || largestCell.mass < amount)
            return false;
        const world = this.gameState.getWorld();
        const now = Date.now();
        // Normalize direction
        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y);
        const dirX = length > 0 ? direction.x / length : 1;
        const dirY = length > 0 ? direction.y / length : 0;
        // Calculate ejection position
        const ejectDistance = largestCell.radius + GAME_CONFIG.EJECTED_MASS_SIZE + 5;
        const ejectPosition = {
            x: largestCell.position.x + dirX * ejectDistance,
            y: largestCell.position.y + dirY * ejectDistance,
        };
        // Create ejected mass
        const ejectedMass = {
            id: generateId(),
            type: EntityType.EJECTED_MASS,
            position: ejectPosition,
            radius: GAME_CONFIG.EJECTED_MASS_SIZE,
            mass: amount,
            velocity: {
                x: dirX * GAME_CONFIG.EJECT_VELOCITY,
                y: dirY * GAME_CONFIG.EJECT_VELOCITY,
            },
            ownerId: playerId,
            decayTime: now,
            canBeEaten: true, // Can be eaten immediately when force ejected
        };
        // Reduce cell mass
        largestCell.mass -= amount;
        largestCell.radius = this.massToRadius(largestCell.mass);
        // Add to world
        world.ejectedMass.set(ejectedMass.id, ejectedMass);
        world.quadTree.insert(ejectedMass);
        world.quadTree.update(largestCell);
        return true;
    }
}
