export var EntityType;
(function (EntityType) {
    EntityType["PLAYER_CELL"] = "player_cell";
    EntityType["PELLET"] = "pellet";
    EntityType["VIRUS"] = "virus";
    EntityType["EJECTED_MASS"] = "ejected_mass";
})(EntityType || (EntityType = {}));
// Game event types
export var GameEventType;
(function (GameEventType) {
    GameEventType["PLAYER_JOINED"] = "player_joined";
    GameEventType["PLAYER_LEFT"] = "player_left";
    GameEventType["PLAYER_ELIMINATED"] = "player_eliminated";
    GameEventType["PLAYER_SPLIT"] = "player_split";
    GameEventType["PLAYER_MERGED"] = "player_merged";
    GameEventType["PLAYER_EJECTED"] = "player_ejected";
    GameEventType["PLAYER_CONSUMED_PELLET"] = "player_consumed_pellet";
    GameEventType["PLAYER_CONSUMED_VIRUS"] = "player_consumed_virus";
    GameEventType["PLAYER_CONSUMED_PLAYER"] = "player_consumed_player";
    GameEventType["PLAYER_CASHOUT_STARTED"] = "player_cashout_started";
    GameEventType["PLAYER_CASHOUT_COMPLETED"] = "player_cashout_completed";
    GameEventType["PLAYER_CASHOUT_CANCELLED"] = "player_cashout_cancelled";
    GameEventType["VIRUS_POPPED"] = "virus_popped";
    GameEventType["PELLET_RESPAWNED"] = "pellet_respawned";
})(GameEventType || (GameEventType = {}));
