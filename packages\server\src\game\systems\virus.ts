import { GameState } from '../state.js'
import { Virus, EntityType, Vector2, PlayerCell, GameEventType } from '../types.js'
import { GAME_CONFIG } from '../../config/lobby.js'
import { generateId } from '../../utils/id.js'
import pino from 'pino'

const logger = pino({ name: 'virus-system' })

export class VirusSystem {
  private gameState: GameState

  constructor(gameState: GameState) {
    this.gameState = gameState
  }

  update(deltaTime: number): void {
    const world = this.gameState.getWorld()
    const now = Date.now()

    // Update all viruses
    for (const virus of world.viruses.values()) {
      this.updateVirus(virus, deltaTime, now)
    }

    // Maintain virus count
    this.maintainVirusCount()
  }

  private updateVirus(virus: Virus, deltaTime: number, now: number): void {
    // Update cooldowns
    if (virus.popCooldown > 0) {
      virus.popCooldown = Math.max(0, virus.popCooldown - deltaTime)
    }

    // Check if virus should split due to being fed
    if (virus.feedCount >= virus.maxFeedCount && virus.popCooldown === 0) {
      this.splitVirus(virus)
    }
  }

  private splitVirus(virus: Virus): void {
    const world = this.gameState.getWorld()
    
    logger.debug(`Virus ${virus.id} splitting due to overfeeding`)

    // Create new virus at random nearby position
    const angle = Math.random() * Math.PI * 2
    const distance = virus.radius * 3
    const newPosition: Vector2 = {
      x: virus.position.x + Math.cos(angle) * distance,
      y: virus.position.y + Math.sin(angle) * distance,
    }

    // Ensure new position is within bounds
    const bounds = world.quadTree.root.bounds
    newPosition.x = Math.max(virus.radius, Math.min(bounds.width - virus.radius, newPosition.x))
    newPosition.y = Math.max(virus.radius, Math.min(bounds.height - virus.radius, newPosition.y))

    const newVirus: Virus = {
      id: generateId(),
      type: EntityType.VIRUS,
      position: newPosition,
      radius: virus.radius,
      mass: virus.mass,
      feedCount: 0,
      maxFeedCount: GAME_CONFIG.VIRUS_FEED_THRESHOLD,
      popCooldown: 0,
      lastPop: Date.now(),
    }

    // Add new virus to world
    world.viruses.set(newVirus.id, newVirus)
    world.quadTree.insert(newVirus)

    // Reset original virus
    virus.feedCount = 0
    virus.popCooldown = GAME_CONFIG.VIRUS_POP_COOLDOWN
    virus.lastPop = Date.now()

    // Create event
    // This would be handled by the GameState class
    logger.info(`Virus split: new virus ${newVirus.id} created`)
  }

  private maintainVirusCount(): void {
    const world = this.gameState.getWorld()
    const currentVirusCount = world.viruses.size
    const targetVirusCount = GAME_CONFIG.VIRUS_COUNT

    if (currentVirusCount < targetVirusCount) {
      const virusesToSpawn = targetVirusCount - currentVirusCount
      for (let i = 0; i < virusesToSpawn; i++) {
        this.spawnVirus()
      }
    }
  }

  private spawnVirus(): void {
    const world = this.gameState.getWorld()
    const position = this.findSafeVirusPosition()

    const virus: Virus = {
      id: generateId(),
      type: EntityType.VIRUS,
      position,
      radius: this.massToRadius(GAME_CONFIG.VIRUS_MASS),
      mass: GAME_CONFIG.VIRUS_MASS,
      feedCount: 0,
      maxFeedCount: GAME_CONFIG.VIRUS_FEED_THRESHOLD,
      popCooldown: 0,
      lastPop: 0,
    }

    world.viruses.set(virus.id, virus)
    world.quadTree.insert(virus)
  }

  private findSafeVirusPosition(): Vector2 {
    const world = this.gameState.getWorld()
    const bounds = world.quadTree.root.bounds
    const maxAttempts = 20
    const minDistanceFromPlayers = 200
    const minDistanceFromOtherViruses = 150

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const x = Math.random() * (bounds.width - 200) + 100
      const y = Math.random() * (bounds.height - 200) + 100

      let isSafe = true

      // Check distance from players
      for (const player of world.players.values()) {
        for (const cell of player.cells) {
          const dx = cell.position.x - x
          const dy = cell.position.y - y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < minDistanceFromPlayers) {
            isSafe = false
            break
          }
        }
        if (!isSafe) break
      }

      // Check distance from other viruses
      if (isSafe) {
        for (const otherVirus of world.viruses.values()) {
          const dx = otherVirus.position.x - x
          const dy = otherVirus.position.y - y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < minDistanceFromOtherViruses) {
            isSafe = false
            break
          }
        }
      }

      if (isSafe) {
        return { x, y }
      }
    }

    // Fallback to random position
    return {
      x: Math.random() * (bounds.width - 200) + 100,
      y: Math.random() * (bounds.height - 200) + 100,
    }
  }

  // Handle player cell hitting virus
  handleVirusCollision(virus: Virus, cell: PlayerCell): void {
    const world = this.gameState.getWorld()
    const player = world.players.get(cell.playerId)
    
    if (!player) return

    // Check if cell is large enough to trigger virus
    if (cell.mass < GAME_CONFIG.VIRUS_SPLIT_THRESHOLD) {
      return
    }

    logger.debug(`Player cell ${cell.id} hit virus ${virus.id}`)

    // Split the player cell
    this.splitPlayerCellFromVirus(cell, virus)

    // Feed the virus
    this.feedVirus(virus)
  }

  private splitPlayerCellFromVirus(cell: PlayerCell, virus: Virus): void {
    const world = this.gameState.getWorld()
    const player = world.players.get(cell.playerId)
    
    if (!player) return

    // Calculate split mass
    const splitMass = cell.mass / GAME_CONFIG.VIRUS_SPLIT_COUNT
    const splitRadius = this.massToRadius(splitMass)

    // Remove original cell
    world.quadTree.remove(cell)
    player.cells = player.cells.filter(c => c.id !== cell.id)

    // Create multiple smaller cells
    for (let i = 0; i < GAME_CONFIG.VIRUS_SPLIT_COUNT; i++) {
      const angle = (Math.PI * 2 * i) / GAME_CONFIG.VIRUS_SPLIT_COUNT
      const distance = virus.radius + splitRadius + 10
      
      const newCell: PlayerCell = {
        id: generateId(),
        type: EntityType.PLAYER_CELL,
        playerId: cell.playerId,
        position: {
          x: virus.position.x + Math.cos(angle) * distance,
          y: virus.position.y + Math.sin(angle) * distance,
        },
        radius: splitRadius,
        mass: splitMass,
        velocity: {
          x: Math.cos(angle) * GAME_CONFIG.VIRUS_SPLIT_VELOCITY,
          y: Math.sin(angle) * GAME_CONFIG.VIRUS_SPLIT_VELOCITY,
        },
        targetPosition: cell.targetPosition,
        canMerge: false,
        mergeTime: Date.now() + GAME_CONFIG.MERGE_COOLDOWN,
        splitCooldown: Date.now() + GAME_CONFIG.SPLIT_COOLDOWN,
        lastSplit: Date.now(),
      }

      // Ensure new cell is within bounds
      const bounds = world.quadTree.root.bounds
      newCell.position.x = Math.max(newCell.radius, Math.min(bounds.width - newCell.radius, newCell.position.x))
      newCell.position.y = Math.max(newCell.radius, Math.min(bounds.height - newCell.radius, newCell.position.y))

      player.cells.push(newCell)
      world.quadTree.insert(newCell)
    }

    logger.info(`Player ${player.id} split by virus into ${GAME_CONFIG.VIRUS_SPLIT_COUNT} cells`)
  }

  private feedVirus(virus: Virus): void {
    virus.feedCount++
    logger.debug(`Virus ${virus.id} fed, count: ${virus.feedCount}/${virus.maxFeedCount}`)
  }

  private massToRadius(mass: number): number {
    return Math.sqrt(mass / Math.PI) * GAME_CONFIG.MASS_TO_RADIUS_FACTOR
  }

  // Public methods
  getVirusCount(): number {
    return this.gameState.getWorld().viruses.size
  }

  removeVirus(virusId: string): void {
    const world = this.gameState.getWorld()
    const virus = world.viruses.get(virusId)
    
    if (virus) {
      world.viruses.delete(virusId)
      world.quadTree.remove(virus)
    }
  }
}
