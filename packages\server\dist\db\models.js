import prisma from './client.js';
// User repository functions
export const UserRepository = {
    async findByWallet(wallet) {
        return prisma.user.findUnique({
            where: { wallet },
        });
    },
    async createUser(data) {
        return prisma.user.create({
            data: {
                wallet: data.wallet,
                username: data.username,
                ipAddress: data.ipAddress,
                countryCode: data.countryCode,
            },
        });
    },
    async updateUser(id, data) {
        return prisma.user.update({
            where: { id },
            data,
        });
    },
    async getUserWithSessions(wallet) {
        return prisma.user.findUnique({
            where: { wallet },
            include: {
                sessions: {
                    where: { status: 'active' },
                    orderBy: { startedAt: 'desc' },
                },
                transactions: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
            },
        });
    },
};
// Game session repository functions
export const GameSessionRepository = {
    async createSession(data) {
        return prisma.gameSession.create({
            data: {
                userId: data.userId,
                lobbyTier: data.lobbyTier,
                sessionToken: data.sessionToken,
                wagerAmount: data.wagerAmount,
                initialMass: data.initialMass,
                currentMass: data.initialMass,
                currentValue: data.wagerAmount,
            },
        });
    },
    async findBySessionToken(sessionToken) {
        return prisma.gameSession.findUnique({
            where: { sessionToken },
            include: {
                user: true,
                transactions: true,
            },
        });
    },
    async updateSession(id, data) {
        return prisma.gameSession.update({
            where: { id },
            data,
        });
    },
    async endSession(id, data) {
        return prisma.gameSession.update({
            where: { id },
            data: {
                status: data.status,
                endedAt: data.endedAt,
                eliminatedBy: data.eliminatedBy,
                cashoutAmount: data.cashoutAmount,
            },
        });
    },
    async getActiveSessions(lobbyTier) {
        return prisma.gameSession.findMany({
            where: {
                status: 'active',
                ...(lobbyTier && { lobbyTier }),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        wallet: true,
                        username: true,
                    },
                },
            },
        });
    },
};
// Transaction repository functions
export const TransactionRepository = {
    async createTransaction(data) {
        return prisma.transaction.create({
            data: {
                userId: data.userId,
                sessionId: data.sessionId,
                type: data.type,
                amount: data.amount,
                feeAmount: data.feeAmount || '0',
                solPriceUsd: data.solPriceUsd,
            },
        });
    },
    async updateTransaction(id, data) {
        return prisma.transaction.update({
            where: { id },
            data,
        });
    },
    async findBySignature(signature) {
        return prisma.transaction.findUnique({
            where: { signature },
        });
    },
    async getUserTransactions(userId, limit = 50) {
        return prisma.transaction.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    },
};
// Audit log repository functions
export const AuditLogRepository = {
    async createLog(data) {
        return prisma.auditLog.create({
            data: {
                userId: data.userId,
                sessionId: data.sessionId,
                eventType: data.eventType,
                eventData: data.eventData,
                severity: data.severity || 'info',
                ipAddress: data.ipAddress,
                userAgent: data.userAgent,
            },
        });
    },
    async getLogsByUser(userId, limit = 100) {
        return prisma.auditLog.findMany({
            where: { userId },
            orderBy: { timestamp: 'desc' },
            take: limit,
        });
    },
    async getLogsByType(eventType, limit = 100) {
        return prisma.auditLog.findMany({
            where: { eventType },
            orderBy: { timestamp: 'desc' },
            take: limit,
        });
    },
};
// System configuration repository functions
export const SystemConfigRepository = {
    async getValue(key) {
        const config = await prisma.systemConfig.findUnique({
            where: { key },
        });
        return config?.value || null;
    },
    async setValue(key, value, type = 'string', updatedBy) {
        return prisma.systemConfig.upsert({
            where: { key },
            update: {
                value,
                type,
                updatedBy,
            },
            create: {
                key,
                value,
                type,
                updatedBy,
            },
        });
    },
    async getAllConfig() {
        return prisma.systemConfig.findMany({
            orderBy: { key: 'asc' },
        });
    },
};
// Lobby state repository functions
export const LobbyStateRepository = {
    async getLobbyState(lobbyTier) {
        return prisma.lobbyState.findUnique({
            where: { id: lobbyTier },
        });
    },
    async updateLobbyState(lobbyTier, data) {
        return prisma.lobbyState.upsert({
            where: { id: lobbyTier },
            update: {
                ...data,
                lastActivity: new Date(),
            },
            create: {
                id: lobbyTier,
                ...data,
                lastActivity: new Date(),
            },
        });
    },
    async getAllLobbyStates() {
        return prisma.lobbyState.findMany({
            orderBy: { id: 'asc' },
        });
    },
};
// Price history repository functions
export const PriceHistoryRepository = {
    async recordPrice(data) {
        return prisma.priceHistory.create({
            data,
        });
    },
    async getLatestPrice() {
        return prisma.priceHistory.findFirst({
            orderBy: { publishTime: 'desc' },
        });
    },
    async getPriceHistory(hours = 24) {
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        return prisma.priceHistory.findMany({
            where: {
                publishTime: {
                    gte: since,
                },
            },
            orderBy: { publishTime: 'desc' },
        });
    },
};
