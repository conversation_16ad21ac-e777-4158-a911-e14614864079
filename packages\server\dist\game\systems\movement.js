import { GAME_CONFIG } from '../../config/lobby.js';
import pino from 'pino';
const logger = pino({ name: 'movement-system' });
export class MovementSystem {
    gameState;
    constructor(gameState) {
        this.gameState = gameState;
    }
    update(deltaTime) {
        const world = this.gameState.getWorld();
        const dt = deltaTime / 1000; // Convert to seconds
        // Update all player cells
        for (const player of world.players.values()) {
            if (!player.isAlive)
                continue;
            for (const cell of player.cells) {
                this.updateCellMovement(cell, player.targetPosition, dt);
                this.constrainToWorldBounds(cell);
                // Update quadtree position
                world.quadTree.update(cell);
            }
            // Update player's total mass and current value
            this.updatePlayerStats(player);
        }
    }
    processMovementInput(input) {
        const player = this.gameState.getPlayer(input.playerId);
        if (!player || !player.isAlive)
            return;
        // Validate input coordinates
        if (input.targetX === undefined || input.targetY === undefined)
            return;
        const world = this.gameState.getWorld();
        const worldBounds = world.quadTree.root.bounds;
        // Clamp target position to world bounds
        const targetX = Math.max(0, Math.min(worldBounds.width, input.targetX));
        const targetY = Math.max(0, Math.min(worldBounds.height, input.targetY));
        // Update player's target position
        player.targetPosition = { x: targetX, y: targetY };
        // Anti-cheat: Validate movement is reasonable
        if (this.validateMovement(player, { x: targetX, y: targetY })) {
            player.lastValidatedPosition = { x: targetX, y: targetY };
            player.lastValidatedTime = Date.now();
        }
        else {
            // Suspicious movement detected
            player.suspiciousActivity = true;
            logger.warn(`Suspicious movement detected for player ${player.id}`);
        }
    }
    updateCellMovement(cell, targetPosition, deltaTime) {
        // Calculate direction to target
        const dx = targetPosition.x - cell.position.x;
        const dy = targetPosition.y - cell.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance < 1) {
            // Close enough to target, stop moving
            cell.velocity.x = 0;
            cell.velocity.y = 0;
            return;
        }
        // Normalize direction
        const dirX = dx / distance;
        const dirY = dy / distance;
        // Calculate speed based on mass
        const speed = this.calculateSpeed(cell.mass);
        // Update velocity
        cell.velocity.x = dirX * speed;
        cell.velocity.y = dirY * speed;
        // Update position
        cell.position.x += cell.velocity.x * deltaTime;
        cell.position.y += cell.velocity.y * deltaTime;
        // Update target position for this cell
        cell.targetPosition = { ...targetPosition };
    }
    calculateSpeed(mass) {
        // Speed formula: base_speed / (1 + mass^exponent)
        const speed = GAME_CONFIG.BASE_SPEED / (1 + Math.pow(mass, GAME_CONFIG.SPEED_MASS_EXPONENT));
        return Math.max(speed, GAME_CONFIG.MIN_SPEED);
    }
    constrainToWorldBounds(cell) {
        const world = this.gameState.getWorld();
        const bounds = world.quadTree.root.bounds;
        // Keep cell within world bounds
        const minX = cell.radius;
        const maxX = bounds.width - cell.radius;
        const minY = cell.radius;
        const maxY = bounds.height - cell.radius;
        if (cell.position.x < minX) {
            cell.position.x = minX;
            cell.velocity.x = 0;
        }
        else if (cell.position.x > maxX) {
            cell.position.x = maxX;
            cell.velocity.x = 0;
        }
        if (cell.position.y < minY) {
            cell.position.y = minY;
            cell.velocity.y = 0;
        }
        else if (cell.position.y > maxY) {
            cell.position.y = maxY;
            cell.velocity.y = 0;
        }
    }
    updatePlayerStats(player) {
        // Calculate total mass from all cells
        let totalMass = 0;
        for (const cell of player.cells) {
            totalMass += cell.mass;
        }
        player.totalMass = totalMass;
        player.score = Math.max(player.score, totalMass); // Score is highest mass achieved
        // Update current value based on mass (will be calculated with oracle price)
        // For now, use a simple ratio
        const massRatio = totalMass / player.initialMass;
        player.currentValue = player.wagerAmount * massRatio;
        // Update cashout eligibility
        player.canCashout = totalMass >= GAME_CONFIG.CASHOUT_MIN_MASS && !player.cashoutStartTime;
    }
    validateMovement(player, newTarget) {
        // Check if movement is too fast (potential speed hack)
        const timeDiff = Date.now() - player.lastValidatedTime;
        if (timeDiff < 100)
            return true; // Too soon to validate
        const dx = newTarget.x - player.lastValidatedPosition.x;
        const dy = newTarget.y - player.lastValidatedPosition.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        // Calculate maximum possible distance based on player's speed
        const maxSpeed = this.calculateSpeed(Math.min(...player.cells.map((c) => c.mass)));
        const maxDistance = maxSpeed * (timeDiff / 1000) * GAME_CONFIG.MAX_VELOCITY_MULTIPLIER;
        return distance <= maxDistance;
    }
    // Utility methods
    static calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    static normalizeVector(vector) {
        const length = Math.sqrt(vector.x * vector.x + vector.y * vector.y);
        if (length === 0)
            return { x: 0, y: 0 };
        return { x: vector.x / length, y: vector.y / length };
    }
    static multiplyVector(vector, scalar) {
        return { x: vector.x * scalar, y: vector.y * scalar };
    }
    static addVectors(v1, v2) {
        return { x: v1.x + v2.x, y: v1.y + v2.y };
    }
    static subtractVectors(v1, v2) {
        return { x: v1.x - v2.x, y: v1.y - v2.y };
    }
}
